import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'academic_resource_detail_page.dart';
import 'login_page.dart';

class AcademicResourcesPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedAcademicResources;
  final bool isFromDetailPage;

  const AcademicResourcesPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedAcademicResources,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _AcademicResourcesPageState createState() => _AcademicResourcesPageState();
}

class _AcademicResourcesPageState extends State<AcademicResourcesPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('academic_resources_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _academicResources = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    if (widget.preloadedAcademicResources != null && widget.preloadedAcademicResources!.isNotEmpty) {
      setState(() {
        _academicResources = List<Map<String, dynamic>>.from(widget.preloadedAcademicResources!);
        _academicResources.forEach((resource) {
          resource['_isImageLoading'] = false;
        });
        _academicResources.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedAcademicResources!.length >= _pageSize;
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      await _loadAcademicResourcesFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final academicResourcesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academicresources';
    try {
      final response = await Supabase.instance.client
          .from(academicResourcesTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_academicResources.length, _academicResources.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadAcademicResourcesFromSupabase({bool initialLoad = false}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) return;

    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final academicResourcesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academicresources';

    try {
      final start = initialLoad ? 0 : _academicResources.length;
      final end = start + _pageSize - 1;

      final response = await Supabase.instance.client
          .from(academicResourcesTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(start, end);

      if (_isDisposed) return;

      final updatedResources = await _updateResourceImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _academicResources = updatedResources;
        } else {
          _academicResources.addAll(updatedResources);
        }
        _academicResources.forEach((resource) {
          resource['_isImageLoading'] = false;
        });
        _hasMore = response.length == _pageSize;
        if (initialLoad) _page = 0; else _page++;
      });

    } on SocketException {
      if (_isDisposed) return;
      _showErrorSnackbar("Offline. Please check your internet connection.");
    } catch (e) {
      if (_isDisposed) return;
      final errorStr = e.toString().toLowerCase();
      if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
        _showErrorSnackbar("Almost all data for this institution hasn't been added yet.");
      } else {
        _showErrorSnackbar("Error fetching academic resources: $e");
      }
      setState(() => _hasMore = false);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateResourceImageUrls(
      List<Map<String, dynamic>> resources) async {
    List<Future<void>> futures = [];
    for (final resource in resources) {
      if (resource['image_url'] == null ||
          resource['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(resource));
      }
    }
    await Future.wait(futures);
    return resources;
  }

  void _setupRealtime() {
    final academicResourcesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academicresources';
    _realtimeChannel = Supabase.instance.client
        .channel('academic_resources')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: academicResourcesTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newRecord = Map<String, dynamic>.from(payload.newRecord);
          final updatedResource = await _updateResourceImageUrls([newRecord]);
          setState(() {
            _academicResources.add(updatedResource.first);
            updatedResource.first['_isImageLoading'] = false;
            _academicResources.sort((a, b) =>
                (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          });
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedRecord = Map<String, dynamic>.from(payload.newRecord);
          setState(() {
            _academicResources = _academicResources.map((resource) {
              return resource['id'] == updatedRecord['id'] ? updatedRecord : resource;
            }).toList();
            _academicResources.sort((a, b) =>
                (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          });
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedRecordId = payload.oldRecord['id'];
          setState(() {
            _academicResources.removeWhere((resource) => resource['id'] == deletedRecordId);
          });
        }
      },
    ).subscribe();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadAcademicResourcesFromSupabase(initialLoad: false);
    }
  }
  
  Future<void> _reloadResources() async {
    setState(() {
      _page = 0;
      _hasMore = true;
    });
    await _loadAcademicResourcesFromSupabase(initialLoad: true);
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> resource) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AcademicResourceDetailPage(
            resource: resource,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Academic Resources',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: _reloadResources,
              child: _academicResources.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No academic resources available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                      itemCount: _academicResources.length + (_isLoadingMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _academicResources.length) {
                          final resource = _academicResources[index];
                          return VisibilityDetector(
                            key: Key('resource_${resource['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (resource['image_url'] == null ||
                                      resource['image_url'] == 'assets/placeholder_image.png') &&
                                  !(resource['_isImageLoading'] ?? false)) {
                                _fetchImageUrl(resource);
                              }
                            },
                            child: _buildResourceCard(resource, theme, currentIsDarkMode),
                          );
                        } else {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> resource) async {
    if (resource['_isImageLoading'] == true) return;
    if (resource['image_url'] != null && resource['image_url'] != 'assets/placeholder_image.png') return;

    if (mounted) {
      setState(() => resource['_isImageLoading'] = true);
    }

    final fullname = resource['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeResourceBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/academic_resources';

    try {
      imageUrl = Supabase.instance.client.storage.from(collegeResourceBucket).getPublicUrl(imageNameWebp);
      // Pre-cache the image to check for existence without a full download
      await precacheImage(CachedNetworkImageProvider(imageUrl), context);
    } catch (e) {
      imageUrl = ''; // Reset if image doesn't exist or fails to load
      print('Error fetching/pre-caching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        resource['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        resource['_isImageLoading'] = false;
      });
    }
  }

  Widget _buildResourceCard(
    Map<String, dynamic> resource,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = resource['fullname'] ?? 'Unknown';
    final String building = resource['building'] ?? '';
    final String room = resource['room'] ?? '';
    final String hours = resource['hours'] ?? '';
    final String about = resource['about'] ?? '';
    final String imageUrl = resource['image_url'] ?? '';

    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, resource),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.library_books,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.library_books,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.library_books,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          hours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}