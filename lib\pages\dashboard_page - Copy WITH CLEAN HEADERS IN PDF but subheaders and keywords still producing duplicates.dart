// At the top of the file:
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf_render/pdf_render.dart' as pdf_render;
import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:file_picker/file_picker.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:path_provider/path_provider.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flip_card/flip_card.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart' as sf_pdf;
import 'dart:typed_data';
import 'package:url_launcher/url_launcher.dart';
import 'package:universal_html/html.dart' as html;
import 'dart:ui' as ui;
import 'dart:ui';
import 'package:flutter_windowmanager_plus/flutter_windowmanager_plus.dart';
import 'package:secure_application/secure_application.dart';
import 'package:secure_application/secure_gate.dart';
import '../widgets/content_helpers.dart';
import '../widgets/pdf_helpers.dart';
import '../widgets/content_segment.dart';
import 'package:path/path.dart' as path;
import 'dart:io' as io;
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdf/pdf.dart' as pdf_package;
import 'package:pdf/pdf.dart';
import 'package:flutter_math_fork/flutter_math.dart';
import '../widgets/latex_helpers.dart' as latex_helpers;
import '../widgets/latex_image_renderer.dart';
import '../utils/text_recognition_helper.dart';
import 'ai_credits_wallet_page.dart';
import '../utils/youtube_transcript_extractor.dart';
import '../utils/url_text_extractor.dart';
import '../utils/docx_extractor.dart';
import '../utils/pptx_extractor.dart';
import '../utils/excel_extractor.dart';
import '../utils/csv_extractor.dart';
import '../utils/audio_extractor.dart';
import '../utils/data_analyzer.dart';
import '../services/supabase_storage_service.dart';
import '../services/local_storage_service.dart';
import 'saved_content_page.dart';
import 'package:fl_chart/fl_chart.dart';



class DashboardPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DashboardPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

enum TtsState { playing, stopped, paused, continued }

class _DashboardPageState extends State<DashboardPage> {
  // Add text recognition and input controllers here in the STATE class
  final TextEditingController _textInputController = TextEditingController();
  final textRecognizer = TextRecognizer();

  // Combined Markdown and LaTeX renderer widget
  static Widget buildLatexContent(String content, bool isDarkMode, double fontSize) {
    return latex_helpers.buildLatexContent(content, isDarkMode, fontSize);
  }

  String? _email;
  bool _isLoading = false;
  bool _isUploading = false;
  bool _isProcessing = false;
  bool _isResponseComplete = true; // Whether the response is complete or cutoff
  double _uploadProgress = 0.0;
  double _processingProgress = 0.0;
  String? _geminiOutput;
  List<FileWithPageRange> _pickedFiles = [];
  String _processType = 'notes';
  final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // Replace with your actual API key
  late GenerativeModel _geminiModel;
  late ChatSession _chatSession;
  late stt.SpeechToText _speech;
  bool _speechAvailable = false;
  final ScrollController _chatScrollController = ScrollController();
  List<ChatMessage> _chatMessages = [];
  final TextEditingController _chatController = TextEditingController();
  List<Flashcard> _flashcards = [];
  List<QuizQuestion> _quizQuestions = [];
  List<ExamQuestion> _examQuestions = [];
  int _quizScore = 0;
  int _currentQuestionIndex = 0;
  Map<int, int?> _userAnswers = {};
  String _fileContent = '';
  List<String> _lessonSteps = [];
  int _currentLessonStepIndex = 0;
  bool _lessonPlaying = false;
  double _lessonSpeed = 1.0;
  double _lessonFontSize = 14.0;
  bool _isNarrationMuted = false;

  // State for Skills Gap Analysis
  String? _desiredCareer;
  String? _seniorityLevel = 'Junior/Entry Level';
  final TextEditingController _careerController = TextEditingController();


  late FlutterTts flutterTts;
  double volume = 1.0;
  double pitch = 1.0;
  double rate = 0.5;
  TtsState ttsState = TtsState.stopped;
  get isPlaying => ttsState == TtsState.playing;
  get isStopped => ttsState == TtsState.stopped;
  get isPaused => ttsState == TtsState.paused;
  get isContinued => ttsState == TtsState.continued;
  String _ttsLanguage = 'en-US';
  String? _readingGradeLevel;
  String? _difficultyLevel;
  String? _outputLanguage = 'English';
  String? _numberOfDays = '1 Week';

  String _displayText = '';
  int _currentCharIndex = 0;
  Timer? _textAnimationTimer;
  bool _isTextAnimationActive = false;

  
  
  Color get generalTextColor => widget.isDarkMode ? Colors.white : Colors.black;
  
  
String removeLatex(String text) {
  // Remove display math
  text = text.replaceAllMapped(RegExp(r'\$\$.*?\$\$', dotAll: true), (match) => '');

  // Remove inline math
  text = text.replaceAllMapped(RegExp(r'\$.*?\$', dotAll: true), (match) => '');

  // Remove LaTeX environments but preserve content
  text = text.replaceAllMapped(
    RegExp(r'\\begin\{.*?\}(.*?)\\end\{.*?\}', dotAll: true),
    (match) => match.group(1) ?? ''
  );

  // Remove LaTeX commands but preserve content
  text = text.replaceAllMapped(
    RegExp(r'\\(\w+)(?:\{(.*?)\})?', dotAll: true),
    (match) => match.group(2) ?? ''
  );

  // Remove double backslashes
  text = text.replaceAll('\\\\', ' ');
  text = text.replaceAll('#', '');

  // Replace escaped braces
  text = text.replaceAll('\\{', '{');
  text = text.replaceAll('\\}', '}');

  // Remove leading LaTeX artifacts
  text = text.replaceFirst(RegExp(r'^.*?latex', dotAll: true), '');

  // Preserve sup/sub tags by not removing angle brackets
  return text.trim();
}

// Helper function to remove preliminary text from AI responses
String removePreliminaryText(String text) {
  // Remove common AI response prefixes
  text = text.replaceFirst(RegExp(r"^(Here are|Here is|Based on|I'll|Let me|This is).*?:\s*", caseSensitive: false), '');

  // Remove duplicate headers
  text = text.replaceAllMapped(
    RegExp(r'^\s*\*\*(.*?)\*\*[\s\n]+\1', multiLine: true),
    (match) => '**${match.group(1)}**',
  );
  
  // Remove "Here's the..." patterns
  text = text.replaceFirst(RegExp(r"^Here's (the|a|an).*?:\s*", caseSensitive: false), '');

  // Remove markdown code block indicators if they wrap the entire content
  if (text.startsWith('```') && text.endsWith('```')) {
    final lines = text.split('\n');
    if (lines.length > 2) {
      // Remove first and last line (the ``` markers)
      text = lines.sublist(1, lines.length - 1).join('\n');
    }
  }

  return text.trim();
}
  
  

  // Build notes content without LaTeX rendering, using specified font hierarchy
// Add this new function to fix malformed closing tags
String fixClosingTags(String text) {
  text = text.replaceAll('<sub/>', '</sub>');
  text = text.replaceAll('<sup/>', '</sup>');
  return text;
}

// Update _buildNotesContent to preprocess the text
Widget _buildNotesContent(String content) {
  // Enhanced regex to handle all cases of "Example" with punctuation
  content = content.replaceAllMapped(
    RegExp(r'\b(example|Example|EXAMPLE)\b(\s*[:.]?)\s*', caseSensitive: false),
    (match) => '**${match.group(1)}${match.group(2)}** ',
  );

  List<InlineSpan> spans = _parseMarkdownToSpans(content);
  return RichText(
    text: TextSpan(
      children: spans,
      style: TextStyle(
        fontFamily: 'NotoSans',
        fontFamilyFallback: [
          'STIXTwoMath',
          'NotoSansSymbols',
          'Bravura',
          'JetBrainsMono',
          'ISOCP',
          'Symbola'
        ],
        fontSize: _lessonFontSize > 0 ? _lessonFontSize : 16.0,
        color: widget.isDarkMode ? Colors.white : Colors.black,
        height: 1.5,
      ),
    ),
  );
}

List<InlineSpan> _parseMarkdownToSpans(String text) {
  // Make "Example" bold by wrapping it with **, accounting for punctuation and case-insensitivity
  text = text.replaceAllMapped(
      RegExp(r'\bExample\b(\s*[:.]?)\s*', caseSensitive: false),
      (match) => '**Example${match.group(1)}** ');

  List<InlineSpan> spans = [];
  final pattern = RegExp(
    r'(\*\*(.*?)\*\*|__(.*?)__)|(\*(.*?)\*|_(.*?)_)|(<sup>(.*?)</sup>)|(<sub>(.*?)</sub>)|(\bexample\b|\bExample\b|\bEXAMPLE\b)(\s*[:.]?)\s*',
    dotAll: true,
    caseSensitive: false,
  );
  
  int lastEnd = 0;
  
  for (final match in pattern.allMatches(text)) {
    if (match.start > lastEnd) {
      spans.add(TextSpan(text: text.substring(lastEnd, match.start)));
    }
    
    // Bold (both ** and __ syntax)
    if (match.group(1) != null) {
      final content = match.group(2) ?? match.group(3) ?? '';
      spans.add(TextSpan(
          text: content, 
          style: const TextStyle(fontWeight: FontWeight.bold)));
    }
    // Italics (both * and _ syntax)
    else if (match.group(4) != null) {
      final content = match.group(5) ?? match.group(6) ?? '';
      spans.add(TextSpan(
          text: content, 
          style: const TextStyle(fontStyle: FontStyle.normal)));
    }
    // Superscript (both <sup> and ^ syntax)
    else if (match.group(7) != null) {
      final content = match.group(8) ?? '';
      spans.add(WidgetSpan(
        child: Transform.translate(
          offset: const Offset(0, -5),
          child: Text(
            content,
            style: TextStyle(fontSize: _lessonFontSize * 0.7),
          ),
        ),
      ));
    }
    // Subscript (<sub> syntax)
    else if (match.group(9) != null) {
      final content = match.group(10) ?? '';
      spans.add(WidgetSpan(
        child: Transform.translate(
          offset: const Offset(0, 3),
          child: Text(
            content,
            style: TextStyle(fontSize: _lessonFontSize * 0.7),
          ),
        ),
      ));
    }
    // Special case for "Example" (handles all casings and punctuation)
    else if (match.group(11) != null) {
      final content = '${match.group(11)}${match.group(12)}';
      spans.add(TextSpan(
        text: content,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ));
    }
    
    lastEnd = match.end;
  }
  
  if (lastEnd < text.length) {
    spans.add(TextSpan(text: text.substring(lastEnd)));
  }
  
  return spans;
}

  int? _quizTimeLimitMinutes; // Quiz time limit in minutes
  Timer? _quizTimer; // Timer for quiz
  Duration _timeRemaining = Duration.zero; // Time remaining for quiz

  pw.Font? notoSansRegular;
  pw.Font? notoSansBold;
  pw.Font? notoSansItalic;
  pw.Font? notoSansBoldItalic;
  pw.Font? notoSansSymbols;
  pw.Font? stixTwoMathRegular;
  pw.Font? bravura;
  pw.Font? jetBrainsMonoRegular;
  pw.Font? isocpRegular;
  pw.Font? symbola;

  @override
  void initState() {
    super.initState();
    _loadEmail();
    _initializeGemini();
    _speech = stt.SpeechToText();
    _initSpeech();
    _setupSupabaseListeners();
    _initTts();
    _loadFonts();
    _secureScreen();
  }

  Future<void> _secureScreen() async {
    if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) {
      // Prevent screenshots and screen recording
      await FlutterWindowManagerPlus.addFlags(FlutterWindowManagerPlus.FLAG_SECURE);

      // Listen for screen recording attempts (iOS)
      if (Platform.isIOS) {
        // iOS specific code for screen recording detection
        // This would typically use native code through method channels
      }
    }
  }

  Future<void> _loadFonts() async {
    // Load Noto Sans variants
    notoSansRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-Regular.ttf'));
    notoSansBold = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-Bold.ttf'));
    notoSansItalic = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-Italic.ttf'));
    notoSansBoldItalic = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-BoldItalic.ttf'));
    notoSansSymbols = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSansSymbols-Regular.ttf'));

    // Load STIX Two Math
    stixTwoMathRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/STIXTwoMath-Regular.ttf'));

    // Load special fonts
    bravura = pw.Font.ttf(await rootBundle.load('assets/fonts/Bravura.ttf'));
    jetBrainsMonoRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/JetBrainsMono-Regular.ttf'));
    isocpRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/ISOCP-Regular.ttf')); // Matches pubspec name
    symbola = pw.Font.ttf(await rootBundle.load('assets/fonts/Symbola.ttf'));
  }


  @override
  void didUpdateWidget(covariant DashboardPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (_lessonSteps.isNotEmpty && !_isTextAnimationActive) {
      _startLessonStepDisplay();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_lessonSteps.isNotEmpty && !_isTextAnimationActive) {
      _startLessonStepDisplay();
    }
  }

  @override
  void dispose() {
    _textAnimationTimer?.cancel();
    _stopTts();
    _cancelQuizTimer();
    _chatScrollController.dispose();
    _careerController.dispose();
    textRecognizer.close(); // Close text recognizer here
    super.dispose();
  }



// Camera and problem solving methods removed




  List<InlineSpan> parseText(String text, TextStyle defaultStyle) {
    // Check if the text contains any LaTeX expressions
    final latexRegExp = RegExp(r'(\$\$.*?\$\$|\$.*?\$|\\\[.*?\\\]|\\\(.*?\\\))', dotAll: true);
    if (latexRegExp.hasMatch(text)) {
      // If LaTeX is found, use the buildLatexContent method to render it
      return [WidgetSpan(
        child: _DashboardPageState.buildLatexContent(
          text,
          defaultStyle.color == Colors.white, // Determine if dark mode based on text color
          defaultStyle.fontSize ?? 16.0,
        ),
      )];
    }

    // If no LaTeX, continue with the original parsing
    List<InlineSpan> spans = [];
    StringBuffer buffer = StringBuffer();
    int i = 0;

    while (i < text.length) {
      if (text.startsWith('<sup>', i)) {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
          buffer.clear();
        }
        int end = text.indexOf('</sup>', i + 5);
        if (end != -1) {
          String content = text.substring(i + 5, end);
          spans.add(WidgetSpan(
            child: Transform.translate(
              offset: const Offset(0, -5), // Move superscript up
              child: Text(
                content,
                style: defaultStyle.copyWith(fontSize: defaultStyle.fontSize! * 0.7),
              ),
            ),
          ));
          i = end + 6;
        } else {
          buffer.write('<sup>');
          i += 5;
        }
      } else if (text.startsWith('<sub>', i)) {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
          buffer.clear();
        }
        int end = text.indexOf('</sub>', i + 5);
        if (end != -1) {
          String content = text.substring(i + 5, end);
          spans.add(WidgetSpan(
            child: Transform.translate(
              offset: const Offset(0, 3), // Move subscript down
              child: Text(
                content,
                style: defaultStyle.copyWith(fontSize: defaultStyle.fontSize! * 0.7),
              ),
            ),
          ));
          i = end + 6;
        } else {
          buffer.write('<sub>');
          i += 5;
        }
      } else if (text[i] == '^') {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
          buffer.clear();
        }
        int end = text.indexOf(' ', i + 1);
        if (end == -1) end = text.length;
        String exponent = text.substring(i + 1, end);
        spans.add(WidgetSpan(
          child: Transform.translate(
            offset: const Offset(0, -5), // Move exponent up
            child: Text(
              exponent,
              style: defaultStyle.copyWith(fontSize: defaultStyle.fontSize! * 0.7),
            ),
          ),
        ));
        i = end;
      } else {
        buffer.write(text[i]);
        i++;
      }
    }

    if (buffer.isNotEmpty) {
      spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
    }

    return spans;
  }

  void _setupSupabaseListeners() {
    Supabase.instance.client
        .from('user_activities')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('New activity: ${snapshot.last['type']}', overflow: TextOverflow.ellipsis)),
        );
      }
    });
  }

  void _initializeGemini() {
    _geminiModel = GenerativeModel(
      model: 'gemini-2.5-flash',
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        maxOutputTokens: 8192,
        temperature: 0.5,
      ),
      safetySettings: [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.none),
      ],
    );
    _chatSession = _geminiModel.startChat();
  }

  Future<void> _initSpeech() async {
    _speechAvailable = await _speech.initialize();
    setState(() {});
  }

  Future<void> _loadEmail() async {
    setState(() => _isLoading = true);
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (mounted) {
        setState(() {
          _email = user?.email ?? 'No email found';
          _isLoading = false;
        });
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _email = 'Error loading email';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'pdf', 'mp3', 'txt', 'doc', 'docx', 'jpg', 'jpeg', 'png',
          'pptx', 'ppt', 'xlsx', 'xls', 'csv'
        ],
        withData: true,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        List<PlatformFile> validFiles = result.files
            .where((file) =>
            (file.bytes != null && file.bytes!.isNotEmpty) ||
                file.path != null)
            .toList();

        if (validFiles.isEmpty) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'The selected files could not be processed - no content or path available', overflow: TextOverflow.ellipsis),
              ),
            );
          }
          return;
        }

        if (mounted) {
          setState(() {
            _pickedFiles
                .addAll(validFiles.map((file) => FileWithPageRange(file: file)));
            _isUploading = true;
            _uploadProgress = 0.0;
          });
        }

        for (int i = 0; i <= 100; i++) {
          await Future.delayed(const Duration(milliseconds: 20));
          if (mounted) {
            setState(() => _uploadProgress = i / 100);
          }
        }

        if (mounted) {
          setState(() => _isUploading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Files ready: ${_pickedFiles.map((fileRange) => fileRange.file.name).join(", ")}',
                   overflow: TextOverflow.ellipsis),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isUploading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking file: $e', overflow: TextOverflow.ellipsis)),
        );
      }
    }
  }

  void _deleteFile(FileWithPageRange fileToDeleteRange) {
    setState(() {
      _pickedFiles.remove(fileToDeleteRange);
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${fileToDeleteRange.file.name} removed')),
    );
  }

  Future<String?> _extractTextFromPdf(List<int> pdfBytes,
      {int? startPage, int? endPage}) async {
    try {
      final document = sf_pdf.PdfDocument(inputBytes: pdfBytes);
      final pageCount = document.pages.count;
      final extractor = sf_pdf.PdfTextExtractor(document);

      StringBuffer resultText = StringBuffer();

      int start = startPage != null ? startPage - 1 : 0;
      int end = endPage != null ? endPage - 1 : pageCount - 1;

      start = start.clamp(0, pageCount - 1);
      end = end.clamp(0, pageCount - 1);

      for (int i = start; i <= end; i++) {
        try {
          final pageText =
              extractor.extractText(startPageIndex: i, endPageIndex: i);
          if (pageText.isNotEmpty) {
            resultText.writeln(pageText);
            resultText.writeln();
          }
        } catch (e) {
          print('Error extracting text from page $i: $e');
        }
      }

      document.dispose();
      return resultText.toString();
    } catch (e) {
      print('PDF extraction failed: $e');
      throw Exception('PDF extraction failed: $e');
    }
  }

  Future<String?> _extractTextFromAudio(PlatformFile audioFile) async {
    if (!_speechAvailable) {
      _initSpeech();
      if (!_speechAvailable) {
        throw Exception('Speech recognition not available');
      }
    }

    if (audioFile.bytes != null) {
      final tempDir = await getTemporaryDirectory();
      final tempPath = path.join(tempDir.path, audioFile.name);
      final file = File(tempPath);
      await file.writeAsBytes(audioFile.bytes!);

      bool available = await _speech.initialize();
      if (!available) {
        throw Exception('Speech service not initialized');
      }

      String textResult = '';
// In _extractTextFromAudio function:
      try {
        await _speech.listen(
          onResult: (result) {
            textResult = result.recognizedWords;
          },
          listenMode: stt.ListenMode.dictation,
          pauseFor: const Duration(seconds: 3),
          localeId: 'en_US',
          partialResults: false,
          cancelOnError: true,
          // Remove the onAudioBuffer parameter
        );
      } catch (e) {
        print('Speech recognition error: $e');
        throw Exception('Speech recognition error: $e');
      } finally {
        _speech.stop();
        file.delete();
      }
      return textResult;
    } else {
      throw Exception('Audio file bytes are null');
    }
  }


Future<void> _processInput() async {
  if (_textInputController.text.isEmpty && _pickedFiles.isEmpty) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Please select files or enter text')),
    );
    return;
  }

  // Handle chat process type immediately
  if (_processType == 'chat' || _processType == 'interview_practice') {
    if (mounted) {
      setState(() {
        _isProcessing = false;
        _processingProgress = 1.0;
      });
    }
    _startChatSession();
    return;
  }

  // Immediate UI update for instant feedback
  setState(() {
    _isProcessing = true;
    _processingProgress = 0.0;
    _geminiOutput = null;
    _flashcards = [];
    _quizQuestions = [];
    _examQuestions = [];
    _lessonSteps = [];
    _isTextAnimationActive = false;
    _cancelQuizTimer();
    _timeRemaining = Duration.zero;
  });

  // Show immediate visual feedback
  final progressController = StreamController<double>();
  progressController.stream.listen((progress) {
    if (mounted) setState(() => _processingProgress = progress);
  });

  // Start progress animation immediately
  for (int i = 0; i <= 10; i++) {
    await Future.delayed(const Duration(milliseconds: 50));
    progressController.add(i / 100);
  }

  try {
    // Check if input is a URL (article or YouTube video)
    final inputText = _textInputController.text.trim();
    if (inputText.isNotEmpty) {
      // Check if it's a URL
      final urlRegex = RegExp(r'^(http|https)://[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}(/\S*)?$');
      final youtubeRegex = RegExp(r'^(https?://)?(www\.)?(youtube\.com|youtu\.?be)/.+$');

      if (urlRegex.hasMatch(inputText)) {
        // Show immediate feedback
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Detected URL. Extracting content...')),
        );

        setState(() {
          _processingProgress = 0.1;
        });

        try {
          String extractedContent = '';

          // Check if it's a YouTube URL
          if (youtubeRegex.hasMatch(inputText)) {
            // Extract YouTube transcript
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Extracting YouTube transcript... This may take a moment.')),
            );

            setState(() {
              _processingProgress = 0.3;
            });

            try {
              // Use the imported YouTube transcript extractor
              extractedContent = await YoutubeTranscriptExtractor.extractTranscript(inputText);
              extractedContent = "YouTube Transcript:\n\n$extractedContent";
            } catch (e) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error extracting YouTube transcript: $e', overflow: TextOverflow.ellipsis)),
              );
              // Continue with regular URL extraction as fallback
              try {
                // Use the imported URL text extractor
                extractedContent = await UrlTextExtractor.extractTextFromUrl(inputText);
                extractedContent = "Article Content:\n\n$extractedContent";
              } catch (e2) {
                throw Exception('Failed to extract content: $e2');
              }
            }
          } else {
            // Regular URL extraction
            setState(() {
              _processingProgress = 0.3;
            });

            try {
              // Use the imported URL text extractor
              extractedContent = await UrlTextExtractor.extractTextFromUrl(inputText);
              extractedContent = "Article Content:\n\n$extractedContent";
            } catch (e) {
              throw Exception('Failed to extract content from URL: $e');
            }
          }

          // Update the text input with the extracted content
          setState(() {
            _textInputController.text = extractedContent;
            _processingProgress = 0.6;
          });

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Content extracted successfully!')),
          );
        } catch (e) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error extracting content: $e', overflow: TextOverflow.ellipsis)),
          );
          setState(() {
            _isProcessing = false;
          });
          await progressController.close();
          return;
        }
      }
    }

    await Future.microtask(() async {
      String content = '';
      final specialCareerTypes = ['resume_feedback', 'cover_letter_feedback', 'skills_gap_analysis'];

      // Special handling for career prep types to combine file and text input
      if (specialCareerTypes.contains(_processType)) {
        String fileContent = _pickedFiles.isNotEmpty ? await _extractContentFromFiles() : '';
        String textContent = _textInputController.text;

        final contentBuilder = StringBuffer();
        if (fileContent.isNotEmpty) {
          contentBuilder.writeln("--- DOCUMENT CONTENT (e.g., Resume/CV) ---");
          contentBuilder.writeln(fileContent);
          contentBuilder.writeln("--- END DOCUMENT CONTENT ---");
          contentBuilder.writeln();
        }
        if (textContent.isNotEmpty) {
          contentBuilder.writeln("--- PASTED TEXT (e.g., Job Description) ---");
          contentBuilder.writeln(textContent);
          contentBuilder.writeln("--- END PASTED TEXT ---");
        }
        content = contentBuilder.toString();
        _fileContent = content; // Store combined content
      } else { // Original logic for all other types
        if (_textInputController.text.isNotEmpty) {
          content = _textInputController.text;
          _fileContent = content;
        } else if (_pickedFiles.isNotEmpty) {
          content = await _extractContentFromFiles();
          _fileContent = content;
        }
      }
      
      // Animate progress regardless of content source
      for (int i = 0; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 20));
        progressController.add(0.1 + (i / 10 * 0.5)); // Progress to 60%
      }

      for (int i = 0; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 30));
        progressController.add(0.6 + (i / 10 * 0.3)); // Progress to 90%
      }

      final prompt = _buildPrompt(content);
      final response = await _geminiModel.generateContent([Content.text(prompt)]);

      if (response.text != null) {
        if (mounted) {
          setState(() {
            // Use the more reliable finishReason to determine if the response is complete
            if (response.candidates.isNotEmpty) {
              _isResponseComplete = response.candidates.first.finishReason == FinishReason.stop;
            } else {
              _isResponseComplete = true; // Fallback if no candidates array
            }
            _handleResponse(response.text!);
            progressController.add(0.95);
          });
        }
      }

      for (int i = 0; i <= 5; i++) {
        await Future.delayed(const Duration(milliseconds: 50));
        progressController.add(0.95 + (i / 5 * 0.05));
      }
    });
  } catch (e) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Processing failed: $e', overflow: TextOverflow.ellipsis)),
      );
    }
  } finally {
    await progressController.close();
    if (mounted) {
      setState(() {
        _isProcessing = false;
        if (_processingProgress < 1.0) _processingProgress = 1.0;
      });
    }
  }
}



Future<String> _extractContentFromFiles() async {
  String combinedFileContent = '';
  final totalFiles = _pickedFiles.length;

  for (int fileIndex = 0; fileIndex < totalFiles; fileIndex++) {
    final fileRange = _pickedFiles[fileIndex];
    final pickedFile = fileRange.file;
    String fileContent = '';
    final fileName = pickedFile.name.toLowerCase();
    Uint8List? fileBytes = pickedFile.bytes;
    // Only access path if not on web platform
    String? filePath = kIsWeb ? null : pickedFile.path;
    bool contentExtracted = false;

    print("Processing file: ${pickedFile.name}, Has Bytes: ${fileBytes != null}, Has Path: ${filePath != null}, Is Web: $kIsWeb");

    // --- Ensure we have bytes if possible (Mobile/Desktop) - Only if bytes are null initially ---
    if (fileBytes == null && filePath != null && !kIsWeb) {
      try {
        print("Reading bytes from path (non-web): $filePath");
        fileBytes = await io.File(filePath).readAsBytes();
        print("Bytes read from path successfully: ${fileBytes?.length ?? 0} bytes");
      } catch (e) {
        print("Error reading file from path $filePath (non-web): $e");
        // Don't set error content yet, let subsequent logic handle it
      }
    }

    // --- PDF Handling ---
    if (fileName.endsWith('.pdf')) {
      String extractedText = '';
      bool attemptOcr = false;
      Uint8List? pdfDataToUse = fileBytes; // Prefer bytes first

      // If bytes are still null and we are NOT on web, try the path again (redundant but safe)
      if (pdfDataToUse == null && filePath != null && !kIsWeb) {
           try {
               pdfDataToUse = await io.File(filePath).readAsBytes();
               print("Re-read bytes from path for PDF (non-web): ${pdfDataToUse?.length ?? 0} bytes");
           } catch (e) {
                print("Error re-reading PDF from path $filePath (non-web): $e");
                pdfDataToUse = null;
           }
      }

      // Check if we have data to process the PDF
      if (pdfDataToUse == null) {
          print("Error: No data (bytes or readable non-web path) available for PDF: ${pickedFile.name}");
          fileContent = "[Error: PDF data unavailable or not readable]";
      } else {
          // Attempt direct text extraction first using the available data
          try {
              print("Attempting direct text extraction for PDF: ${pickedFile.name} using ${pdfDataToUse.length} bytes.");
              extractedText = await _extractTextFromPdf(
                      pdfDataToUse,
                      startPage: fileRange.startPage,
                      endPage: fileRange.endPage) ?? '';

              if (extractedText.trim().length < 100) {
                 print("PDF ${pickedFile.name}: Minimal text extracted directly (${extractedText.trim().length} chars), will attempt OCR.");
                 attemptOcr = true;
              } else {
                 print("PDF ${pickedFile.name}: Direct text extraction successful.");
                 fileContent = extractedText;
                 contentExtracted = true;
              }
          } catch (e) {
              print("Direct PDF text extraction failed for ${pickedFile.name}, attempting OCR. Error: $e");
              attemptOcr = true;
              extractedText = ''; // Reset text if direct failed
          }

          // Attempt OCR if needed, using the same data source
          if (attemptOcr) {
              try {
                 print("Processing PDF ${pickedFile.name} with OCR using ${pdfDataToUse.length} bytes...");
                 // Pass BYTES to OCR function. Path is only relevant inside if NOT on web and bytes failed initially.
                 extractedText = await _extractTextFromImagePdf(
                    pdfDataToUse, // ALWAYS pass the bytes we have confirmed are available
                    kIsWeb ? null : filePath, // Only pass path if not on web
                    startPage: fileRange.startPage,
                    endPage: fileRange.endPage);
                 print("OCR processing finished for ${pickedFile.name}. Text length: ${extractedText.length}");
                 fileContent = extractedText;
                 // Consider OCR successful even if text is short
                 contentExtracted = true;
              } catch (e) {
                 print("OCR PDF extraction failed for ${pickedFile.name}: $e");
                 if (fileContent.isEmpty) { // Only set error if no previous content
                   fileContent = '[Error during OCR PDF processing: $e]';
                 }
              }
          }
      }
    }
    // --- PowerPoint Handling ---
    else if (fileName.endsWith('.pptx') || fileName.endsWith('.ppt')) {
      if (fileBytes != null) {
        try {
          print("Processing PowerPoint ${pickedFile.name} with PptxExtractor...");
          if (fileName.endsWith('.pptx')) {
            fileContent = await PptxExtractor.extractText(fileBytes);
          } else {
            fileContent = PptxExtractor.extractTextFromPpt(fileBytes);
          }

          if (!fileContent.startsWith('[Error')) {
            contentExtracted = true;
            print("PowerPoint extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
          } else {
            print("PowerPoint extraction returned an error: $fileContent");
          }
        } catch (e) {
          print("Error extracting text from PowerPoint ${pickedFile.name}: $e");
          fileContent = '[Error extracting text from PowerPoint: $e]';
        }
      } else {
        print("Cannot process PowerPoint ${pickedFile.name} without bytes.");
        fileContent = '[PowerPoint file content not available]';
      }
    }
    // --- Excel Handling ---
    else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
      if (fileBytes != null) {
        try {
          print("Processing Excel ${pickedFile.name} with ExcelExtractor...");
          fileContent = await ExcelExtractor.extractText(fileBytes);

          if (!fileContent.startsWith('[Error')) {
            contentExtracted = true;
            print("Excel extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
          } else {
            print("Excel extraction returned an error: $fileContent");
          }
        } catch (e) {
          print("Error extracting text from Excel ${pickedFile.name}: $e");
          fileContent = '[Error extracting text from Excel: $e]';
        }
      } else {
        print("Cannot process Excel ${pickedFile.name} without bytes.");
        fileContent = '[Excel file content not available]';
      }
    }
    // --- CSV Handling ---
    else if (fileName.endsWith('.csv')) {
      if (fileBytes != null) {
        try {
          print("Processing CSV ${pickedFile.name} with CsvExtractor...");
          fileContent = await CsvExtractor.extractText(fileBytes);

          if (!fileContent.startsWith('[Error')) {
            contentExtracted = true;
            print("CSV extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
          } else {
            print("CSV extraction returned an error: $fileContent");
          }
        } catch (e) {
          print("Error extracting text from CSV ${pickedFile.name}: $e");
          fileContent = '[Error extracting text from CSV: $e]';
        }
      } else {
        print("Cannot process CSV ${pickedFile.name} without bytes.");
        fileContent = '[CSV file content not available]';
      }
    }
    // --- Image Handling (JPG, PNG, etc.) ---
    else if (fileName.endsWith('.jpg') ||
             fileName.endsWith('.jpeg') ||
             fileName.endsWith('.png') ||
             fileName.endsWith('.bmp') ||
             fileName.endsWith('.gif'))
    {
       print("Processing Image ${pickedFile.name} with OCR...");

       // Check if we have bytes to process
       if (fileBytes != null) {
         try {
           // Create an instance of the TextRecognitionHelper
           final textRecognitionHelper = TextRecognitionHelper();

           // Process the image for text recognition
           fileContent = await textRecognitionHelper.processImageForText(fileBytes, filePath);
           contentExtracted = true;
           print("Enhanced OCR successful for ${pickedFile.name}. Text length: ${fileContent.length}");
         } catch (e) {
           print("Enhanced OCR failed for ${pickedFile.name}: $e");
           fileContent = '[Error during enhanced OCR processing: $e]';
         }
       } else if (filePath != null && !kIsWeb) {
         // Fallback: try to read bytes from file path for non-web platforms
         try {
           print("Reading image bytes from path for ${pickedFile.name}: $filePath");
           fileBytes = await io.File(filePath).readAsBytes();

           final textRecognitionHelper = TextRecognitionHelper();
           fileContent = await textRecognitionHelper.processImageForText(fileBytes, filePath);
           contentExtracted = true;
           print("Enhanced OCR successful from path for ${pickedFile.name}. Text length: ${fileContent.length}");
         } catch (e) {
           print("Enhanced OCR failed from path for ${pickedFile.name}: $e");
           fileContent = '[Error during enhanced OCR processing from path: $e]';
         }
       } else {
         print("Cannot process image ${pickedFile.name} - no bytes or path available");
         fileContent = '[Image file content not available for OCR]';
       }
    }
    // --- TXT Handling ---
    else if (fileName.endsWith('.txt')) {
      try {
          if (fileBytes != null) {
            fileContent = utf8.decode(fileBytes, allowMalformed: true);
            contentExtracted = true;
          } else if (filePath != null && !kIsWeb) {
            fileContent = await io.File(filePath).readAsString();
            contentExtracted = true;
          } else {
             print("TXT file content not available for ${pickedFile.name}");
             fileContent = '[TXT file content not available]';
          }
      } catch (e) {
          print("Error reading TXT file ${pickedFile.name}: $e");
          fileContent = '[Error reading text file: $e]';
      }
    }
    // --- DOC/DOCX Handling with improved extraction ---
    else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
       if (fileBytes != null) {
         try {
           // Use the DocxExtractor for better extraction
           try {
             // Use the imported DOCX extractor
             if (fileName.endsWith('.docx')) {
               fileContent = await DocxExtractor.extractText(fileBytes);
             } else {
               // For older .doc format
               fileContent = DocxExtractor.extractTextFromDoc(fileBytes);
             }

             contentExtracted = true;
             print("Extracted DOC/DOCX ${pickedFile.name} using DocxExtractor");
           } catch (e) {
             print("Error using DocxExtractor for ${pickedFile.name}: $e");

             // Fallback to basic decoding if extractor fails
             try {
               // Try UTF-8 first
               fileContent = utf8.decode(fileBytes, allowMalformed: true);
               // Basic check if decoding likely failed (lots of replacement chars)
               if (fileContent.contains('\uFFFD') && fileContent.length < 100 && fileContent.replaceAll('\uFFFD', '').trim().isEmpty) {
                  throw Exception('Likely invalid UTF-8, trying latin1');
               }
               contentExtracted = true;
               print("Decoded DOC/DOCX ${pickedFile.name} as UTF-8");
             } catch (_) {
               try {
                 // Fallback to Latin-1
                 fileContent = latin1.decode(fileBytes);
                 contentExtracted = true;
                 print("Decoded DOC/DOCX ${pickedFile.name} as Latin-1");
               } catch (e2) {
                 print("Error decoding DOC/DOCX file ${pickedFile.name} with UTF-8 and Latin-1: $e2");
                 fileContent = '[Could not decode DOC/DOCX content - requires specific library for complex formats]';
               }
             }
           }
         } catch (e) {
           print("All DOC/DOCX extraction methods failed for ${pickedFile.name}: $e");
           fileContent = '[Failed to extract content from DOC/DOCX file]';
         }
       } else {
           print("Cannot process DOC/DOCX ${pickedFile.name} without bytes.");
           fileContent = '[Cannot process DOC/DOCX from path or without bytes]';
       }
    }
    // --- MP3 Handling ---
    else if (fileName.endsWith('.mp3')) {
      // Check for bytes first, as path might not be usable on web for audio either
      if (fileBytes != null) {
          try {
              print("Processing MP3 ${pickedFile.name} with AudioExtractor...");
              fileContent = await AudioExtractor.extractText(fileBytes, pickedFile.name);
              if (!fileContent.startsWith('[')) { // Check if it's not an error message
                 contentExtracted = true;
                 print("MP3 extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
              } else {
                 print("MP3 extraction returned an error message: $fileContent");
                 // Fallback to the old method if the new extractor fails
                 final tempAudioFile = PlatformFile(
                     name: pickedFile.name,
                     size: fileBytes.length,
                     bytes: fileBytes);
                 fileContent = await _extractTextFromAudio(tempAudioFile) ?? '[Audio extraction returned null]';
                 if (!fileContent.startsWith('[')) { // Check if it's not an error message
                    contentExtracted = true;
                    print("Fallback MP3 extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
                 }
              }
          } catch (e) {
              print("Audio extraction failed for ${pickedFile.name} (using bytes): $e");
              // Fallback to the old method if the new extractor fails
              try {
                final tempAudioFile = PlatformFile(
                    name: pickedFile.name,
                    size: fileBytes.length,
                    bytes: fileBytes);
                fileContent = await _extractTextFromAudio(tempAudioFile) ?? '[Audio extraction returned null]';
                if (!fileContent.startsWith('[')) { // Check if it's not an error message
                   contentExtracted = true;
                   print("Fallback MP3 extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
                } else {
                   fileContent = '[Error extracting audio content: $e]';
                }
              } catch (e2) {
                print("Both audio extraction methods failed for ${pickedFile.name}: $e2");
                fileContent = '[Error extracting audio content: $e2]';
              }
          }
      } else if (filePath != null && !kIsWeb) {
         // Fallback for non-web if bytes weren't available
         print("Attempting MP3 extraction from path (non-web): $filePath");
         try {
              // Read bytes from file path
              final fileBytes = await io.File(filePath).readAsBytes();
              fileContent = await AudioExtractor.extractText(fileBytes, pickedFile.name);
              if (!fileContent.startsWith('[')) {
                 contentExtracted = true;
                 print("MP3 extraction from path successful for ${pickedFile.name}. Text length: ${fileContent.length}");
              } else {
                 // Fallback to old method
                 final tempAudioFile = PlatformFile(
                     name: pickedFile.name,
                     size: fileBytes.length,
                     path: filePath);
                 fileContent = await _extractTextFromAudio(tempAudioFile) ?? '[Audio extraction returned null]';
                 if (!fileContent.startsWith('[')) {
                    contentExtracted = true;
                    print("Fallback MP3 extraction from path successful for ${pickedFile.name}. Text length: ${fileContent.length}");
                 }
              }
         } catch (e) {
             print("Audio extraction failed for ${pickedFile.name} (using path): $e");
             fileContent = '[Error extracting audio content from path: $e]';
         }
      } else {
          print("MP3 file ${pickedFile.name} has no bytes or non-web path.");
          fileContent = '[MP3 data unavailable]';
      }
    }
     // --- Unsupported File Type ---
    else {
       if (fileContent.isEmpty) { // Only set if no prior error message exists
         print("Unsupported file type: ${pickedFile.name}");
         fileContent = '[Unsupported file type: ${fileName.split('.').last}]';
       }
    }

    // --- Append to Combined Content ---
    // Include file name header regardless of success to indicate which file was processed
    combinedFileContent += "## ${pickedFile.name}\n\n";
    if (contentExtracted && fileContent.trim().isNotEmpty) {
      combinedFileContent += "$fileContent\n\n";
    } else if (fileContent.isNotEmpty) { // Include error messages/placeholders
      combinedFileContent += "$fileContent\n\n";
    } else { // Fallback if fileContent remained empty for some reason
      combinedFileContent += "[No content extracted or extraction failed without specific error]\n\n";
    }

    print("Finished processing file: ${pickedFile.name}. Content Extracted: $contentExtracted. Appended Length: ${fileContent.length}");

  } // End of file loop

  print("Finished extracting content from all files.");
  return combinedFileContent;
}




// Helper function to convert raw pixels (RGBA) to PNG bytes
Future<Uint8List?> _encodePixelsToPng(Uint8List pixels, int width, int height) async {
  final completer = Completer<ui.Image>();
  ui.decodeImageFromPixels(
    pixels,
    width,
    height,
    ui.PixelFormat.rgba8888, // Assuming pdf_render provides RGBA
    (ui.Image img) {
      completer.complete(img);
    },
    // Add onError callback if desired
  );

  try {
    final ui.Image decodedImage = await completer.future;
    final ByteData? byteData = await decodedImage.toByteData(format: ui.ImageByteFormat.png);
    decodedImage.dispose(); // Dispose the ui.Image
    return byteData?.buffer.asUint8List();
  } catch (e) {
    print("Error encoding pixels to PNG: $e");
    return null;
  }
}


Future<String> _extractTextFromImagePdf(
    Uint8List? pdfBytes,
    String? pdfPath,
    {int? startPage, int? endPage}) async {

  StringBuffer combinedText = StringBuffer();
  pdf_render.PdfDocument? doc;
  final currentTextRecognizer = TextRecognizer();

  io.Directory? tempDir; // Mobile only
  String? tempDirPath; // Mobile only

  if (!kIsWeb) {
      try {
          tempDir = await getTemporaryDirectory();
          tempDirPath = tempDir.path;
          print("Using temp directory for PDF OCR (non-web): $tempDirPath");
      } catch (e) {
          print("Error getting temp directory for PDF OCR (non-web): $e");
          await currentTextRecognizer.close();
          return "[Error: Cannot access temp storage for PDF OCR.]";
      }
  } else {
      print("Running PDF OCR on Web - no temp directory needed.");
  }

  try {
    // --- Open the PDF document ---
    if (pdfBytes != null) {
      print("Opening PDF from bytes for OCR.");
      doc = await pdf_render.PdfDocument.openData(pdfBytes);
    } else if (pdfPath != null) {
      if (!kIsWeb) {
         print("Opening PDF from path for OCR (non-web): $pdfPath");
         doc = await pdf_render.PdfDocument.openFile(pdfPath);
      } else {
          print("Error: Cannot open PDF from path on Web for OCR. Bytes required.");
           await currentTextRecognizer.close();
          return "[Error: Cannot open PDF from path on Web.]";
      }
    } else {
      throw Exception("No PDF bytes or path provided for OCR.");
    }

    if (doc == null) {
      throw Exception("Failed to open PDF document for OCR.");
    }

    // --- Determine Page Range ---
    final pageCount = doc.pageCount;
    int start = (startPage != null && startPage > 0) ? startPage - 1 : 0;
    int end = (endPage != null && endPage <= pageCount) ? endPage - 1 : pageCount - 1;
    start = start.clamp(0, pageCount - 1);
    end = end.clamp(start, pageCount - 1);

    print("Processing PDF pages ${start + 1} to ${end + 1} with OCR.");

    // --- Process each page ---
    for (int i = start; i <= end; i++) {
      pdf_render.PdfPage? page;
      io.File? tempImageFile; // Mobile only

      try {
        print("Processing page ${i + 1}...");
        page = await doc.getPage(i + 1);

        // Render page to raw pixels
final pdf_render.PdfPageImage? pageImage = await page.render(
  width: (page.width * 2).toInt(), // Higher resolution
  height: (page.height * 2).toInt(),
  // Format parameter removed if not required by your version
);

        // **** CORRECTED CHECK: Use .pixels ****
        if (pageImage == null || pageImage.pixels == null) {
          print('Warning: Skipping page ${i + 1} due to rendering failure or null pixels.');
          continue;
        }

        // **** CORRECTED ACCESS: Use .pixels ****
        final Uint8List rawPixels = pageImage.pixels;
        final int imageWidth = pageImage.width;
        final int imageHeight = pageImage.height;

        InputImage? inputImage;

        // --- Create InputImage (Platform Specific) ---
if (kIsWeb) {
  // Web: Use InputImage.fromBytes with raw pixel data
  print("Creating InputImage from PDF page raw pixels (Web) for page ${i+1}");
  try {
    inputImage = InputImage.fromBytes(
      bytes: rawPixels,
      metadata: InputImageMetadata(
        size: Size(imageWidth.toDouble(), imageHeight.toDouble()),
        rotation: InputImageRotation.rotation0deg, // Assume no rotation
        format: InputImageFormat.bgra8888, // Changed to match available enum value
        bytesPerRow: imageWidth * 4 // RGBA = 4 bytes/pixel
      )
    );
    print("InputImage created from raw pixels for page ${i + 1} (Web).");
  } catch (e) {
    print("Error creating InputImage from raw pixels on web (Page ${i+1}): $e");
    combinedText.writeln("--- Page ${i + 1} (OCR Prep Error - Raw Pixels Web: $e) ---");
    inputImage = null;
  }
} else {
            // Mobile: Encode pixels to PNG, save PNG to temp file, use fromFilePath
            if (tempDirPath == null) {
                 print("Error: Temp directory path is null on mobile for PDF page ${i + 1}. Skipping OCR.");
                 combinedText.writeln("--- Page ${i + 1} (Error: No Temp Dir) ---");
                 inputImage = null;
            } else {
                print("Encoding page ${i+1} pixels to PNG (Mobile)...");
                final Uint8List? pngBytes = await _encodePixelsToPng(rawPixels, imageWidth, imageHeight);

                if (pngBytes != null) {
                    final tempFileName = 'pdf_page_${i + 1}_${DateTime.now().millisecondsSinceEpoch}.png';
                    final tempFilePath = path.join(tempDirPath, tempFileName);
                    tempImageFile = io.File(tempFilePath);
                    try {
                        await tempImageFile.writeAsBytes(pngBytes);
                        print("Creating InputImage from path for PDF page ${i + 1} (Mobile): $tempFilePath");
                        inputImage = InputImage.fromFilePath(tempFilePath);
                    } catch (e) {
                         print("Error writing/reading temp PNG file on mobile (Page ${i+1}): $e");
                         combinedText.writeln("--- Page ${i + 1} (Temp PNG File Error: $e) ---");
                         inputImage = null;
                         if (await tempImageFile.exists()) {
                             try { await tempImageFile.delete(); } catch (_) {}
                         }
                    }
                } else {
                     print("Error encoding page ${i + 1} pixels to PNG. Skipping OCR.");
                     combinedText.writeln("--- Page ${i + 1} (Error: PNG Encoding Failed) ---");
                     inputImage = null;
                }
            }
        }

        // --- Perform OCR ---
        if (inputImage != null) {
            try {
                print("Performing OCR on page ${i + 1}...");
                final RecognizedText recognizedText = await currentTextRecognizer.processImage(inputImage);
                combinedText.writeln("--- Page ${i + 1} ---");
                combinedText.writeln(recognizedText.text);
                combinedText.writeln();
                print("OCR successful for page ${i + 1}. Text length: ${recognizedText.text.length}");
            } catch (ocrError) {
                 print("Error during OCR processing for page ${i + 1}: $ocrError");
                 combinedText.writeln("--- Page ${i + 1} (OCR Execution Error: $ocrError) ---");
            }
        } else {
             print("Skipping OCR for page ${i + 1} as InputImage creation failed.");
             if (!combinedText.toString().contains("Page ${i + 1} (Error")) {
                 combinedText.writeln("--- Page ${i + 1} (Skipped OCR due to preparation error) ---");
             }
        }

      } catch (pageProcessingError) {
        print('Error processing page ${i + 1}: $pageProcessingError');
        combinedText.writeln("--- Page ${i + 1} (Error: $pageProcessingError) ---");
      } finally {
         // --- Clean up resources for the current page ---
         // *** REMOVED page.close() as it doesn't exist ***

         // Delete mobile temp file if it was created
         if (tempImageFile != null && await tempImageFile.exists()) {
            try {
               await tempImageFile.delete();
               print("Deleted temp file for page ${i + 1}.");
            } catch (deleteError) {
               print("Error deleting temp pdf page file for page ${i + 1}: $deleteError");
            }
         }
      }
    } // End of page loop

  } catch (e) {
    print('Fatal error during PDF OCR process: $e');
    combinedText.writeln("\n[Extraction failed due to error: $e]");
  } finally {
    // --- Final Cleanup ---
    if (doc != null) {
        try {
           doc.dispose(); // Dispose the main PDF document
           print("PDF document disposed.");
        } catch (disposeError) {
           print("Error disposing PDF document: $disposeError");
        }
    }
    try {
       await currentTextRecognizer.close(); // Close the TextRecognizer
       print("TextRecognizer closed.");
    } catch (closeError) {
       print("Error closing TextRecognizer: $closeError");
    }
  }

  return combinedText.toString();
}

// Helper function
Future<ui.Image> _createImageFromPixels(Uint8List pixels, int width, int height) async {
  final completer = Completer<ui.Image>();
  ui.decodeImageFromPixels(
    pixels,
    width,
    height,
    ui.PixelFormat.rgba8888,
    (ui.Image img) {
      completer.complete(img);
    },
  );
  return completer.future;
}





// 6. Optional Helper to create InputImage from bytes via temp file
Future<InputImage?> _createInputImageFromBytes(Uint8List imageBytes, String tempDirPath, String tempFileName) async {
  File? tempImageFile;
  try {
      final tempFilePath = path.join(tempDirPath, tempFileName);
      tempImageFile = File(tempFilePath);
      await tempImageFile.writeAsBytes(imageBytes);
      return InputImage.fromFilePath(tempFilePath);
      // Note: The caller is responsible for deleting the temp file after ML Kit processing
  } catch (e) {
      print("Error creating temp file for InputImage: $e");
      // Clean up if file creation failed partially
       try {
            if (tempImageFile != null && await tempImageFile.exists()) {
               await tempImageFile.delete();
            }
       } catch (deleteError) {
           print("Error deleting temp image file during error handling: $deleteError");
       }
      return null;
  }
}




  String _buildPrompt(String content) {
    String gradeLevelText = '';
    if (_readingGradeLevel != null && _readingGradeLevel!.isNotEmpty) {
      gradeLevelText = ' Tailor the content to a ${_readingGradeLevel!.toLowerCase()} reading level.';
    }
    String difficultyLevelText = '';
    if (_difficultyLevel != null && _difficultyLevel!.isNotEmpty) {
      difficultyLevelText = ' Difficulty level: ${_difficultyLevel!.toLowerCase()}.';
    }
    String languageText = '';
    if (_outputLanguage != null && _outputLanguage != 'English') {
      languageText = ' Write the output in ${_outputLanguage!} language. All responses must be in ${_outputLanguage!} language only.';
    }
    final String tableInstruction = '''
- For any tabular data, you MUST enclose it within [TABLE_START] and [TABLE_END] tags. Inside, use standard markdown pipe syntax.
  Example:
  [TABLE_START]
  | Header 1 | Header 2 |
  |---|---|
  | Data A | Data B |
  [TABLE_END]''';

    final prompts = {
      'notes': '''Generate ULTRA-DETAILED, COMPREHENSIVE NOTES atleast 50% the length of the original document(s) that teach the subject matter directly:$gradeLevelText$languageText
- Formatting:
  - Surround all headers and subheaders with ** ** to make them bold. This includes numbered headers (e.g., '**1. Main Header**'), lettered headers (e.g., '**a. Subtopic**'), and Roman numeral headers (e.g., '**I. Introduction**').- Clear headings
- all headers and subheaders should be bold including all numbered, lettered, and roman numeral headers should also be bold
- The main topic should not be numbered or lettered 
- no introductory text like "Here is a study guide on sets". Just go straight to writing the study guide
- Do not use the term study guide in the main title/topic e.g. Dont say 'Sets A Comprehensive Study Guide' but just say 'Sets'
- titles and subtitles should be bold
- no bullet points infront of number like 1. or (1)
- Bullet points
- Key terms in **bold**
  - DO NOT use any italics.
- $tableInstruction
- no lines like **01. Clothing & Apparel Supply:** but 01. Clothing & Apparel Supply:
- no lines like *Example:* but Example:
- Use dash (-) for bullet points in markdown
- do not mention the source name
- in pdf export, the header text of every section of the document should be bold, make topic titles bold, headings bold"
- in pdf export properly italicize and make text bold in the right places, dont use * or surround text with ** or lead any text with #. clean output
- include tables whenever necessary
- For tables, use markdown pipe syntax with headers
- DO NOT attempt to generate or include images, illustrations, pictures, or diagrams
- DO NOT use pollinations.ai or any other image generation service
- DO NOT include any image examples or visual illustrations in the output
- Never use unicode symbols or special characters
- First create a study guide based on the material(s) then Cover EVERY concept in the given file or files exhaustively without omission in atleast 5 pages with unique content on each page and without repeating anything thats been covered before. leave no stone unturned and the notes should be the condensed but comprehensive and exhaustive version of the material, no omissions
- Include ALL foundational elements needed for complete understanding
- Never use "the document states" or similar meta-references - present as primary knowledge
- output only the bold headers and subheaders
Content: $content''',

      'notes_qa': '''Generate ULTRA-DETAILED, COMPREHENSIVE NOTES in a Question and Answer format based on the provided content.
- Formatting:
  - Each entry should start with a bolded question on its own line: **Question:** [Your Question Here]
  - Followed by a detailed, clear answer on a **separate new line**: **Answer:** [Your Answer Here]
  - Ensure that the question and answer are on separate lines, with a line break between them. Do not allow the answer to wrap onto the same line as the question.
  - Surround all other headers and subheaders with ** ** to make them bold.
  - DO NOT use any italics.
- no introductory text like "Here is a study guide on sets". Just go straight to writting the study guide
- Do not use the term study guide in the main title/topic e.g. Dont say 'Sets A Comprehensive Study Guide' but just say 'Sets'
- $tableInstruction
- The tone should be educational and clear.
- Cover every concept in the source material exhaustively.
- no lines like **01. Clothing & Apparel Supply:** but 01. Clothing & Apparel Supply:
- no lines like *Example:* but Example:
- The main topic or header should not be start with * or ** or # or ##
- no line should start with # or ##
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.
- number the different questions
- Have a header about the topic at hand thats separate from the individual questions so we dont have to use the first action item as the title for all. 

Example Format:
**Question:** What is the capital of France?
**Answer:** Paris is the capital of France


Content: $content''',
      'chat': '''Use this document as your ONLY knowledge source:
$content
Respond to ALL subsequent questions using ONLY this information but you can infer from the document to give a response to a question.$languageText''',
      'interactive_lesson': '''Generate an interactive lesson from the content provided, suitable for a smartboard presentation for ${gradeLevelText.isNotEmpty ? gradeLevelText : 'a general audience'}.$languageText
Structure the lesson in sequential steps, as if teaching the material step-by-step on a whiteboard. Each step should be a concise point or explanation.
Focus on text-based content only. DO NOT include placeholders for visual elements, images, or multimedia.
- Use clear, descriptive text instead of visual elements
- Provide detailed explanations rather than referring to charts or diagrams
- DO NOT include image references, illustrations, or visual placeholders
- $tableInstruction

Ensure the lesson is easy to parse into individual steps. Focus on clarity and conciseness for each step, suitable for display on a digital whiteboard.
Example output format (step by step, each on a new line):

Dont include the words Step and number when writing on the board e.g Step 1. Malaria example but should simply be written as Malaria example

Introduction to Supply and Demand

What is Demand? - Demand is how much of a product consumers are willing to buy at different prices.
[chart] - Demand Curve showing price vs quantity demanded.

What is Supply? - Supply is how much of a product producers are willing to sell at different prices.
[chart] - Supply Curve showing price vs quantity supplied.

Market Equilibrium - Equilibrium is where supply and demand meet.
[chart] - Equilibrium Point on Supply and Demand Curves.

Factors Affecting Demand - Discuss factors like income, consumer preferences, etc.

Factors Affecting Supply - Discuss factors like cost of production, technology, etc.

Conclusion - Summary of Supply and Demand concepts.

Content: $content''',
      'cheatsheet': '''Generate a concise yet comprehensive cheatsheet for the content.$gradeLevelText$languageText
Include a clear topic title, key formulas, definitions, and examples in a well‑structured markdown format.
Use dash (-) for bullet points in markdown.
Formatting:
- no introductory text like "Here is a study guide on sets". Just go straight to writing the cheatsheet
- Do not use the term study guide in the main title/topic e.g. Dont say 'Sets A Comprehensive Cheatsheet' but just say 'Sets'
- The main topic or header should not be start with * or ** or # or ##
- no line should start with # or ##  
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.
$tableInstruction

Content: $content''',
      'flashcards': '''Generate at least 50 comprehensive and no repeats when we change reading grade level flashcards (Q: question / A: answer) or generate a comprehensive set of flashcards if the content is less but can be comprehensively covered:$gradeLevelText$languageText

Q: [Question]
A: [Answer]

- Each card must contain:
  Front:
  ||Core Concept||: Concise question
  ||Type||: [Definition/Application/Analysis/Connection]

  Full Explanation: (1 sentence)

  Requirements:
  1. Cover EVERY concept from source material
  2. 15-50 cards per major topic
  3. Progressive difficulty within topics
  4. Cross-link cards through connection points
  5. before the full explanation in the back dont put any text like :0 A:

Content: $content''',
      'quiz': '''Generate an interactive quiz in TEXT format with at least 50 questions comprehensive and no repeats when we change difficulty level.$difficultyLevelText$languageText Use the following EXACT format for each question. Do NOT generate code. Just the plain text quiz. the questions should be different on every difficulty level so there is no repetition of questions

Example Format:
1. What is the capital of France?
A) London
B) Paris
C) Berlin
D) Rome
Answer: B

2. What is the chemical symbol for water?
A) H2O
B) CO2
C) NaCl
D) O2
Answer: A
Now generate a quiz based on the following content, using the EXACT format above:

Content: $content''',
      'transcript': '''Create comprehensive and no repeats when we change reading grade level transcript:$languageText
- Speaker labels
- Timestamps
- Paragraph breaks

Content: $content''',
      'summary': '''Generate a summary/brief of the following content.$languageText

Instruction: Dynamically adapt the summary based on the content type.

If the content is a research paper or academic paper, provide a detailed summary with these sections:
- Background
- Research Question
- Study Method
- Study Limitations
- Global Alignment (if applicable)
- Findings
- include quantitative information where necessary
- Policy Recommendations
- Stakeholder Implications (for donors, government, public, decision-makers, private sector, students, academics)
$tableInstruction

Otherwise, if the content is not a research paper, provide a concise general summary of the main points.
Formatting:
- no introductory text like "Here is a summary on sets". Just go straight to writing the summary
- The main topic or header should not be start with * or ** or # or ##
- no line should start with # or ##  

Content: $content''',
      'exam': '''Generate a comprehensive practice exam with at least 50 NEW questions (different from a quiz, more detailed, paper-based exam style) covering all aspects of the content.$difficultyLevelText$languageText
Use this EXACT format for each question and answer:

[Question Number]. [Question Text]
A) [Option 1]
B) [Option 2]
C) [Option 3]
D) [Option 4]
Answer: [Full Correct Answer Text - not just the letter, explain the answer in detail]

Do NOT include any markdown formatting, headers, or explanatory text outside of the question/answer format.

Content: $content''',
      'minutes': '''Generate comprehensive meeting minutes based on the content provided.$gradeLevelText$languageText
Include:
- Meeting Title
- Date and Time
- Attendees
- Agenda Items
- Discussions
- Action Items
- Decisions
$tableInstruction

Content: $content''',
      'lesson_plan': '''Generate a detailed lesson plan based on the content.$gradeLevelText$languageText Include:
- Learning objectives
- Required materials
- Step-by-step teaching instructions
- Classroom activities
- Assessment methods
Formatting:
- no introductory text like "Here is a lesson_plan on sets". Just go straight to writing the lesson_plan
- The main topic or header should not be start with * or ** or # or ##
- no line should start with # or ##  
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.

$tableInstruction
Content: $content''',
      'worksheet': '''Generate a worksheet with practice questions and exercises based on the content designed to be completed in ${_numberOfDays ?? '1 Week'}.$languageText
$tableInstruction
Formatting:
- no introductory text like "Here is a worksheet on sets". Just go straight to writing the worksheet
- no line should start with # or ##  
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.

Content: $content''',
      'workbook': '''Generate a comprehensive workbook with practice questions and exercises based on the provided content. The workbook is for students to practice, so it must ONLY contain questions and leave space for answers.
- DO NOT provide any answers, solutions, or sample responses.
- Structure it with clear question numbers.
- Include a variety of question types (e.g., fill-in-the-blanks, short answer, problem-solving) relevant to the content.
- Add lines or a designated area for the user to write their answers. For example: "Answer: _____________" or use a text box placeholder.
- The workbook should be designed to be completed in ${_numberOfDays ?? '1 Week'}.$languageText
$tableInstruction
Formatting:
- no introductory text like "Here is a workbook on sets". Just go straight to writing the workbook
- The main topic or header should not be start with * or ** or # or ##
- no line should start with # or ##  
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.
- Have a header thats separate from the individual quiz questions so we dont have to use the first question as the title for all. 
- remove 'Answer:' and all answers and just leave questions so students can practice

Content: $content''',
      'homework_guide': '''Generate a homework guide that includes practice problems and solutions based on the content.$gradeLevelText$languageText
$tableInstruction
Formatting:
- no introductory text like "Here is a homework guide on sets". Just go straight to writing the homework guide
- The main topic or header should not be start with * or ** or # or ##
- no line should start with # or ##  
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.
Content: $content''',
      'project_ideas': '''Generate project ideas based on the content, suitable for students.$gradeLevelText$languageText
$tableInstruction
Formatting:
- no introductory text like "Here are project ideas on sets". Just go straight to writing the project ideas
- The main topic or header should not be start with * or ** or # or ##
- no line should start with # or ##  
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.
- number and bold the different project ideas
- Have a header thats separate from the individual project ideas so we dont have to use the first case study title as the title for all. 

Content: $content''',
      'exam_free': '''Generate a 20 comprehensive free-response exam with essay questions based on the content.$difficultyLevelText$languageText
Use this EXACT format for each question and answer:

[Question Number]. [Question Text]
Answer: [Sample answer or solution to the question]

Do NOT include any markdown formatting, headers, or explanatory text outside of the question/answer format.

Content: $content''',
      'exam_case': '''Generate a comprehensive case-based exam with scenario questions based on the content. All questions must be free-response (no multiple-choice).$difficultyLevelText$languageText
For each case, provide a scenario followed by one or more free-response questions.

Use this format:
[Question Number]. [Case Scenario]
[Question Text]
Answer: [Sample answer or solution to the question]

Do NOT include any multiple-choice options, markdown formatting, headers, or explanatory text outside of the question/answer format.

Content: $content''',
      'grammar': '''Check the grammar and suggest improvements for the following text. Provide corrections and explanations.$gradeLevelText$languageText

Content: $content''',
      'paper_grader': '''Provide detailed writing feedback on this paper.$languageText Include:
- Grammar corrections
- Structural improvements
- Argument strength analysis
- Style suggestions
- Citation feedback
$tableInstruction
Formatting:
- no introductory text like "Here is feedback to improve the paper". Just go straight to writing the paper feedback
- The main topic or header should not be start with * or ** or # or ##
- no line should start with # or ##  
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.

Content: $content''',
      'case_studies': '''Generate relevant case studies or real-life applications based on the content.$gradeLevelText$languageText
$tableInstruction
Formatting:
- no introductory text like "Here case studies on sets". Just go straight to writing the case studies
- The main topic or header should not be start with * or ** or # or ##
- no line should start with # or ##  
- titles and subtitles should be bold
- Key terms in **bold** 
- DO NOT use any italics.
- number and bold the different case studies
- Have a header thats separate from the individual case studies so we dont have to use the first case study title as the title for all. 


Content: $content''',
      'experiment': '''Generate an experiment or lab activity based on the content.$gradeLevelText$languageText
$tableInstruction
Formatting:
- no introductory text like "Here is are experiments on sets". Just go straight to writing the experiments
- The main topic or header should not be start with * or ** or # or ##
- no line should start with # or ##  
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.

Content: $content''',
      'scheme_of_work': '''Generate a comprehensive scheme of work based on the content for a duration of ${_numberOfDays ?? '1 Week'}.$languageText
Include:
- Learning objectives
- Daily/Weekly breakdown of topics
- Required resources
- Assessment methods
- Suggested activities
- Cross-curricular links
$tableInstruction
Formatting:
- no introductory text like "Here is a scheme of work on sets". Just go straight to writing the scheme of work
- The main topic or header should not be start with * or ** or # or ##
- no line should start with # or ##  
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.

Content: $content''',
      'data_analysis': '''Conduct a comprehensive data analysis on the provided data.$languageText
Include:
- Summary statistics (mean, median, mode, standard deviation, etc.)
- Key trends and patterns
- Correlations between variables
- Visualizations (describe charts that would be useful)
- Insights and recommendations
- Anomalies or outliers
- Potential business implications

Format the output with clear sections, tables for statistics, and detailed descriptions of what visualizations would show.
$tableInstruction

Content: $content''',
      'resume_feedback': '''Act as an expert career coach and hiring manager.$languageText Review the provided documents. The user has supplied their resume/CV (likely in the "DOCUMENT CONTENT" section) and may have provided a job description (likely in the "PASTED TEXT" section).
Focus on:
1.  **Clarity and Impact:** Are the descriptions clear? Do they use strong action verbs and quantifiable achievements?
2.  **Formatting and Readability:** Suggest improvements for a clean, professional layout.
3.  **ATS Compatibility:** Highlight any potential issues for Applicant Tracking Systems.
4.  **Alignment with Job Description:** If a job description is provided, analyze how well the resume is tailored to it.
5.  **Overall Impression:** Give a summary of the resume's strengths and weaknesses.
$tableInstruction
Formatting:
- no introductory text like "okay, ive reviewed the resume". Just go straight to writing the feedback
- The main topic or header should not be start with * or ** or # or ##.
- no line should start with # or ##.
- At the end, write a completely revised version of the resume that incorporates all your feedback and is tailored to the provided job description.
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.
- if a job description is not provided, dont provide an improved resume based on a hypothetical job description. Just provide improvements to the resume as it is
Content:
$content''',
      'cover_letter_feedback': '''Act as an expert career coach and hiring manager.$languageText Review the provided documents. The user has supplied their cover letter (likely in the "DOCUMENT CONTENT" or "PASTED TEXT" section) and may have provided a job description.
Focus on:
1.  **Opening:** Is the opening strong and engaging?
2.  **Structure and Flow:** Is the letter well-organized and easy to follow?
3.  **Connection to Job:** Does it effectively link the applicant's skills and experiences to the job requirements (if provided)?
4.  **Tone and Professionalism:** Is the tone appropriate? Are there any grammatical errors?
5.  **Call to Action:** Is there a clear and confident closing?
$tableInstruction
Formatting:
- no introductory text like "okay, ive reviewed the coverletter". Just go straight to writing the feedback
- The main topic or header should not be start with * or ** or # or ##. 
- no line should start with # or ##  
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.
- At the bottom of everything, write a complete coverletter incorporating the suggestions you made tailored for the job description.
- After the revised cover letter, create a new section titled '### Sample Application Email'. In this section, write a concise, professional email that the user can send to a hiring manager when attaching their resume and cover letter.

Content:
$content''',
      'interview_practice': '''This is a placeholder prompt for initializing the interview chatbot. The actual system message is handled in the _startChatSession method. Content: $content''',
      'action_item_extractor': '''From the content provided, extract all specific action items, tasks, or to-do list entries.$languageText For each item, identify who is responsible if mentioned, and any deadlines. Format the output as a clear, bulleted list.
$tableInstruction
Formatting:
- number and bold the different action items
- Have a header thats separate from the individual action items so we dont have to use the first action item as the title for all. 

Content: $content''',
      'quiz_fill_in_the_blank': '''Generate a comprehensive fill-in-the-blank quiz based on the provided content. Create at least 20 questions.
- Use several underscores in a row (e.g., `________`) to represent the blank space where the answer should go.
- After each question, provide the correct answer on a **separate new line**, prefixed with "Answer:". The question line and the answer line must not be on the same line.
- Ensure that each question and corresponding answer are on separate lines, with a line break between them. Do not allow the answer to wrap onto the same line as the question.
- Have a header thats separate from the individual quiz questions so we dont have to use the first question as the title for all. 
- bold 'Answer:'

Example:
1. The capital of France is ________.
Answer: Paris

Formatting:
- no introductory text like "okay, here is the quiz". Just go straight to writing the quiz.

Content: $content''',
      'discussion_prompts': '''Generate a list of at least 10 thought-provoking discussion prompts based on the provided content.$languageText These prompts should be open-ended and designed to encourage critical thinking, analysis, and deeper engagement with the material in a classroom or group setting.
Formatting:
- no introductory text like "okay, here are discussion prompts". Just go straight to writing the discussion prompts
- The main topic or header should not be start with * or ** or # or ##
- no line should start with # or ##  
- Key terms in **bold**
- DO NOT use any italics.

Content: $content''',
      'skills_gap_analysis': '''Act as an expert career coach and talent development specialist.$languageText Analyze the provided resume/CV against the user's desired career goal.

**User's Goal:**
- **Career/Job Title:** ${_desiredCareer ?? 'Not specified'}
- **Seniority Level:** ${_seniorityLevel ?? 'Not specified'}

**Your Task:**
1.  **Skills Gap Analysis:**
    - Identify the key skills (technical and soft) required for the desired role at the specified seniority level.
    - Compare these required skills with the skills and experience present in the user's resume/CV.
    - Clearly list the skills the user **already has** that are relevant.
    - Clearly list the **skill gaps** (skills the user is missing or needs to strengthen).
2.  **Actionable Recommendations:**
    - Provide a step-by-step plan to bridge these gaps. Suggest specific types of projects, experiences, or learning paths.
3.  **Certification Recommendations:**
    - Recommend specific, industry-respected certifications that would strengthen the user's profile for this career path. Explain why each certification is valuable.
4.  **Resume Enhancement:**
    - Suggest how the user can better highlight their existing skills on their resume to align with the target role.

Format the output with clear headings for each section (e.g., "**Skills Gap Analysis**", "**Certification Recommendations**").
- no line should start with # or ##  
- titles and subtitles should be bold
- Key terms in **bold**
- DO NOT use any italics.

Content (User's Resume/CV and optional Job Description):
$content''',
    };


    return prompts[_processType] ?? "Process: $content";
  }

  void _handleResponse(String response) {
    if (mounted) {
      setState(() {
        _geminiOutput = response;
      });
    }
    switch (_processType) {
      case 'flashcards':
        _parseFlashcards(response);
        break;
      case 'quiz':
        _parseQuiz(response);
        _startQuizTimer(); // Start timer when quiz is generated and displayed
        break;
      case 'exam':
      case 'exam_free':
      case 'exam_case':
        _parseExam(response);
        break;
      case 'chat':
      case 'interview_practice':
        _startChatSession();
        break;
      case 'interactive_lesson':
        _parseInteractiveLesson(response);
        break;
      case 'data_analysis':
        // Data analysis parsing removed
        break;
      default:
        break;
    }
  }

  // Continue generating content when the response is cutoff
  Future<void> _continueGenerating() async {
    if (_geminiOutput == null || _isResponseComplete) return;

    setState(() {
      _isProcessing = true;
      _processingProgress = 0.0;
    });

    final progressController = StreamController<double>();
    try {
      progressController.stream.listen((progress) {
        if (mounted) setState(() => _processingProgress = progress);
      });

      // Simulate progress for better UX
      for (int i = 0; i <= 20; i++) {
        progressController.add(i / 100);
        await Future.delayed(const Duration(milliseconds: 50));
      }

      // Create a prompt asking to continue from where it left off
      String languageText = '';
      if (_outputLanguage != null && _outputLanguage != 'English') {
        languageText = ' Continue in ${_outputLanguage!} language only.';
      }
      final prompt = 'Please continue generating the response from where you left off. Do not repeat any part of the previous response. Here is the previous response to provide context of where you stopped:\n\n${_geminiOutput}${languageText}';
      final response = await _geminiModel.generateContent([Content.text(prompt)]);

      if (response.text != null) {
        if (mounted) {
          setState(() {
            // Append the new response to the existing one
            _geminiOutput = '${_geminiOutput!}\n\n${response.text!}';
            // Use the more reliable finishReason to determine if the response is now complete
            if (response.candidates.isNotEmpty) {
              _isResponseComplete = response.candidates.first.finishReason == FinishReason.stop;
            } else {
              _isResponseComplete = true; // Fallback
            }
            progressController.add(0.95);
          });
        }
      }

      for (int i = 0; i <= 5; i++) {
        await Future.delayed(const Duration(milliseconds: 50));
        progressController.add(0.95 + (i / 5 * 0.05));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to continue generating: $e', overflow: TextOverflow.ellipsis)),
        );
      }
    } finally {
      await progressController.close();
      if (mounted) {
        setState(() {
          _isProcessing = false;
          if (_processingProgress < 1.0) _processingProgress = 1.0;
        });
      }
    }
  }

  void _parseInteractiveLesson(String response) {
    // Split by double newlines but filter out empty steps
    final rawSteps = response.split('\n\n');
    final steps = <String>[];

    // Process steps to ensure no empty boards
    for (final step in rawSteps) {
      final trimmedStep = step.trim();
      if (trimmedStep.isNotEmpty) {
        steps.add(trimmedStep);
      }
    }

    setState(() {
      _lessonSteps = steps;
      _currentLessonStepIndex = 0;
      _lessonPlaying = true; // Start playing automatically
      _displayText = '';
      _currentCharIndex = 0;
    });

    if (_lessonSteps.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _startLessonStepDisplay();
        }
      });
    }
  }

  // Data analysis method removed but Excel and CSV support maintained

  void _startChatSession() {
    try {
      _chatSession = _geminiModel.startChat();

      String contextContent = _fileContent;
      String pastedText = _textInputController.text.trim();
      if (pastedText.isNotEmpty) {
        contextContent = contextContent.isEmpty ? pastedText : "$contextContent\n\n$pastedText";
      }

      String languageText = '';
      if (_outputLanguage != null && _outputLanguage != 'English') {
        languageText = "Respond only in ${_outputLanguage!} language. ";
      }

      String systemMessage;
      String initialAiMessage;

      if (_processType == 'interview_practice') {
        systemMessage = '''You are an expert interviewer conducting a job interview. The user has provided their resume/CV and/or a job description. Your task is to:
1.  Act as a friendly but professional hiring manager.
2.  Ask a mix of behavioral, technical, and situational questions based on the provided documents.
3.  Ask **one question at a time** and wait for the user's response.
4.  After the user answers, provide brief, constructive feedback on their answer before asking the next question.
5.  Start the interview by introducing yourself and the role (you can invent a company name and the role based on the job description).
$languageText
Context documents (Resume/CV/Job Description):
$contextContent''';
        initialAiMessage = "Hello! I'm ready to start your mock interview. Whenever you're ready, just send your first message to begin.";
      } else { // Default to AI Tutor
        systemMessage = '''Document content:
$contextContent

You are an assistant for this content. Base all responses strictly on this information. If asked about something not in the document, you can infer from the document to give an answer, but be sure to state that the information is not explicitly in the provided document. $languageText''';
        initialAiMessage = "I've analyzed the source material and am ready to answer questions about it.";
      }

      _chatSession.sendMessage(Content.text(systemMessage));

      if (mounted) {
        setState(() {
          _chatMessages = [ChatMessage(initialAiMessage, false)];
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat initialization error: ${e.toString()}', overflow: TextOverflow.ellipsis)),
        );
      }
    }
  }

  void _parseFlashcards(String response) {
    final flashcards = <Flashcard>[];
    final blocks = response.split(RegExp(r'\n\s*(?=Q:|Question|\d+\.)'));

    final qaRegex = RegExp(
      r'^(?:Q:|Question|\d+\.?)\s*(.*?)\s*(?:A:|Answer:?|\n)(.*)',
      caseSensitive: false,
      dotAll: true,
    );

    for (final block in blocks) {
      final match = qaRegex.firstMatch(block);
      if (match != null) {
        String question = match.group(1)?.trim() ?? '';
        String answer = match.group(2)?.trim() ?? '';

        // Clean question: Remove leading colons and spaces
        question = question
            .replaceAll(RegExp(r'\|\|.*?\|\||::.*?::|\*\*|\*|`|#+|- |\[.*?\]|^[:\s]+|[:\s]+$'), '')
            .trim();

        // Clean answer: Remove "::0" and other patterns
        answer = answer
            .replaceAll(RegExp(r'(Definition|Full Explanation|Application|Analysis|Connection|::.*?::|\|\|.*?\|\||[-*#`]|^A:?\s*|::0\s*)'), '')
            .trim();

        // Final trimming: Remove any remaining leading or trailing colons
        question = question.replaceFirst(RegExp(r'^:+'), '').replaceFirst(RegExp(r':+$'), '').trim();
        answer = answer.replaceFirst(RegExp(r'^:+'), '').replaceFirst(RegExp(r':+$'), '').trim();

        if (question.isNotEmpty && answer.isNotEmpty) {
          flashcards.add(Flashcard(
            question: question,
            answer: answer,
          ));
        }
      }
    }

    if (mounted) {
      setState(() => _flashcards = flashcards);
    }
  }

  void _parseQuiz(String response) {
    final quizQuestions = <QuizQuestion>[];
    // Updated regex to be more flexible with different languages
    final questionRegex = RegExp(
        r'(?:\n|^)\s*(\d+)\.\s+([\s\S]+?)(?=(?:\n\s*[A-D]\))|$)',
        dotAll: true);
    final optionRegex = RegExp(r'([A-D])\)\s+([\s\S]+?)(?=(?:\n\s*[A-D]\))|(?:\n\s*(?:Answer|Answers|Correct|Réponse|Respuesta|Jawaban|Odpowiedź|Antwoord|Antwort|Risposta|Svar|Vastaus|Svaret|Odpověď|Válasz|Răspuns|Απάντηση|Отговор|Відповідь|Ответ|答案|回答|정답|उत्तर|جवाब|پاسخ|الإجابة|الجواب):))',
        dotAll: true);
    final answerRegex = RegExp(r'(?:Answer|Answers|Correct|Réponse|Respuesta|Jawaban|Odpowiedź|Antwoord|Antwort|Risposta|Svar|Vastaus|Svaret|Odpověď|Válasz|Răspuns|Απάντηση|Отговор|Відповідь|Ответ|答案|回答|정답|उत्तर|جवाब|پاسخ|الإجابة|الجواب):\s*([A-D]?)',
        caseSensitive: false);

    final matches = questionRegex.allMatches(response);

    print('Raw Gemini Quiz Response:\n$response');

    for (final match in matches) {
      print('\n--- Quiz Question Match ---');
      print('Full Match: ${match.group(0)}');
      print('Question Number: ${match.group(1)}');
      print('Question Text: ${match.group(2)}');

      String questionText = match.group(2)?.trim() ?? '';
      questionText = questionText.replaceFirst(RegExp(r'^[#\s]*'), '');
      if (questionText.isEmpty) continue;

      // Get the full text from this question to the next question
      int startPos = match.start;
      int endPos = response.length;

      // Calculate the next question number
      int currentQuestionNumber = int.parse(match.group(1) ?? '0');
      int nextQuestionNumber = currentQuestionNumber + 1;

      // Find the next question to determine the end of this question block
      final nextQuestionPattern = '(?:\\n|^)\\s*$nextQuestionNumber\\.\\s+';
      final nextQuestionMatch = RegExp(nextQuestionPattern).firstMatch(response.substring(match.end));

      if (nextQuestionMatch != null) {
        endPos = match.end + nextQuestionMatch.start;
      }

      final fullQuestionBlock = response.substring(startPos, endPos);
      print('Full Question Block: $fullQuestionBlock');

      final optionMatches = optionRegex.allMatches(fullQuestionBlock);
      final options = <String>[];
      final optionLetters = <String>[];

      print('Options:');
      for (final optMatch in optionMatches) {
        final letter = optMatch.group(1)?.toUpperCase() ?? '';
        final optionText = optMatch.group(2)?.trim() ?? '';
        optionLetters.add(letter);
        options.add(optionText);
        print('  Letter: $letter, Option Text: $optionText');
      }

      // Find the answer in the full question block
      final answerMatch = answerRegex.firstMatch(fullQuestionBlock);
      String correctLetter = answerMatch?.group(1)?.toUpperCase() ?? '';
      print('Correct Letter: $correctLetter');

      int? correctAnswerIndex;
      if (correctLetter.isNotEmpty) {
        correctAnswerIndex = optionLetters.indexOf(correctLetter);
        if (correctAnswerIndex == -1) {
          print(
              'Warning: Correct letter "$correctLetter" not found in options letters: $optionLetters');

          // Try to infer the correct answer by position (A=0, B=1, etc.)
          final letterIndex = correctLetter.codeUnitAt(0) - 'A'.codeUnitAt(0);
          if (letterIndex >= 0 && letterIndex < options.length) {
            correctAnswerIndex = letterIndex;
            print('Inferred correct answer index by letter position: $correctAnswerIndex');
          } else {
            correctAnswerIndex = null;
          }
        }
      }

      quizQuestions.add(QuizQuestion(
        question: questionText,
        options: options,
        correctAnswerIndex: correctAnswerIndex,
      ));
    }

    if (mounted) {
      setState(() {
        _quizQuestions = quizQuestions;
        _currentQuestionIndex = 0;
        _userAnswers = {};
        _quizScore = 0;
        _timeRemaining = _quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0
            ? Duration(minutes: _quizTimeLimitMinutes!)
            : Duration.zero; // Reset timer on new quiz
      });
    }
  }

void _parseExam(String response) {
  final examQuestions = <ExamQuestion>[];
  final questionBlocks = response.split(RegExp(r'(?=\n\d+\.\s)'));

  for (final block in questionBlocks) {
    final lines = block.trim().split('\n').map((line) => line.trim()).toList();
    if (lines.isEmpty) continue;

    // Find the index of the answer line
    int answerIndex = -1;
    for (int i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('Answer: ') || lines[i].startsWith('Sample Answer: ') || lines[i].startsWith('Solution: ')) {
        answerIndex = i;
        break;
      }
    }
    if (answerIndex == -1) continue;

    // Extract answer text
    String answerText = lines[answerIndex].substring(lines[answerIndex].indexOf(':') + 1).trim();
    if (answerIndex + 1 < lines.length) {
      answerText += '\n' + lines.sublist(answerIndex + 1).join('\n').trim();
    }

    // Extract question lines
    final questionLines = lines.sublist(0, answerIndex);

    // Remove question number from first line
    String questionText = questionLines[0].replaceFirst(RegExp(r'^\d+\.\s*'), '');

    // Extract options and question text
    final options = <String>[];
    bool inOptions = false;
    for (int i = 1; i < questionLines.length; i++) {
      final line = questionLines[i];
      if (RegExp(r'^[A-D]\)\s').hasMatch(line)) {
        inOptions = true;
        final optionText = line.replaceFirst(RegExp(r'^[A-D]\)\s'), '').trim();
        options.add(optionText);
      } else if (inOptions) {
        // Stop at the first non-option line after options start
        break;
      } else {
        questionText += '\n' + line;
      }
    }

    if (questionText.isNotEmpty) {
      examQuestions.add(ExamQuestion(
        question: questionText.trim(),
        options: options,
        correctAnswer: answerText.trim(),
      ));
    }
  }

  if (mounted) {
    setState(() {
      _examQuestions = examQuestions;
    });
  }
}

  void _startQuizTimer() {
    _cancelQuizTimer();
    if (_quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0) {
      _timeRemaining = Duration(minutes: _quizTimeLimitMinutes!);
      _quizTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (_timeRemaining.inSeconds > 0) {
          setState(() => _timeRemaining -= const Duration(seconds: 1));
        } else {
          _cancelQuizTimer();
          _submitQuiz();
        }
      });
    }
  }

  void _cancelQuizTimer() {
    _quizTimer?.cancel();
    _quizTimer = null;
  }

  String get _formattedTimeRemaining {
    final minutes = _timeRemaining.inMinutes.remainder(60);
    final seconds = _timeRemaining.inSeconds.remainder(60);
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Widget _buildQuizView(ThemeData theme, Color generalTextColor) {
    return _quizQuestions.isEmpty
        ? Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          _isProcessing
              ? 'Generating quiz questions...'
              : 'No quiz questions generated. Try processing the file first.',
          style: GoogleFonts.notoSans(color: generalTextColor),
        ),
      ),
    )
        : Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Question ${_currentQuestionIndex + 1}/${_quizQuestions.length}',
                  style: GoogleFonts.notoSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: generalTextColor),
                ),
                if (_quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0)
                  Text(
                    'Time: ${_formattedTimeRemaining}',
                    style: GoogleFonts.notoSans(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.secondary),
                  ),
                Text(
                  'Score: $_quizScore',
                  style: GoogleFonts.notoSans(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: generalTextColor),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.dividerColor),
              ),
              child: _DashboardPageState.buildLatexContent(
                _quizQuestions[_currentQuestionIndex].question,
                widget.isDarkMode,
                _lessonFontSize > 0 ? _lessonFontSize : 16.0,
              ),
            ),
            const SizedBox(height: 20),
            ..._buildQuizOptions(theme, generalTextColor),
            const SizedBox(height: 24),
            _buildQuizNavigationButtons(theme, generalTextColor),
          ],
        ),
      ),
    );
  }

List<Widget> _buildQuizOptions(ThemeData theme, Color generalTextColor) {
  return List.generate(
    _quizQuestions[_currentQuestionIndex].options.length,
    (index) {
      if (_quizQuestions[_currentQuestionIndex].options[index].isEmpty)
        return const SizedBox.shrink();

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Material(
          color: _userAnswers[_currentQuestionIndex] == index
              ? (widget.isDarkMode ? Colors.grey[700] : Colors.grey[200])
              : theme.cardColor,
          borderRadius: BorderRadius.circular(8),
          borderOnForeground: false, // Explicitly disable border
          child: InkWell(
            onTap: () {
              setState(() => _userAnswers[_currentQuestionIndex] = index);
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Text(
                    '${String.fromCharCode('A'.codeUnitAt(0) + index)}. ',
                    style: GoogleFonts.notoSans(
                      fontWeight: FontWeight.bold,
                      color: generalTextColor,
                    ),
                  ),
                  Expanded(
                    child: _DashboardPageState.buildLatexContent(
                      _quizQuestions[_currentQuestionIndex].options[index],
                      widget.isDarkMode,
                      _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                    ),
                  ),
                  Radio<int>(
                    value: index,
                    groupValue: _userAnswers[_currentQuestionIndex],
                    onChanged: (value) {
                      setState(() => _userAnswers[_currentQuestionIndex] = value);
                    },
                    activeColor: widget.isDarkMode ? Colors.white : Colors.black,
                    fillColor: MaterialStateProperty.resolveWith<Color>((states) {
                      if (states.contains(MaterialState.selected)) {
                        return widget.isDarkMode ? Colors.white : Colors.black;
                      }
                      return widget.isDarkMode ? Colors.grey[400]! : Colors.grey[600]!;
                    }),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    },
  );
}

  Widget _buildQuizNavigationButtons(ThemeData theme, Color generalTextColor) {
    return ElevatedButton(
      onPressed: _userAnswers[_currentQuestionIndex] != null
          ? () {
        if (_userAnswers[_currentQuestionIndex] ==
            _quizQuestions[_currentQuestionIndex].correctAnswerIndex) {
          setState(() => _quizScore++);
        }
        if (_currentQuestionIndex < _quizQuestions.length - 1) {
          setState(() => _currentQuestionIndex++);
        } else {
          _submitQuiz();
        }
      }
          : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: widget.isDarkMode ? Colors.white : Colors.black,
        padding: const EdgeInsets.symmetric(vertical: 16),
        disabledBackgroundColor: Colors.grey,
      ),
      child: Text(
        _currentQuestionIndex < _quizQuestions.length - 1
            ? 'Next Question'
            : 'Finish Quiz',
        style: GoogleFonts.notoSans(
            color: _userAnswers[_currentQuestionIndex] != null
                ? widget.isDarkMode
                ? Colors.black
                : Colors.white
                : Colors.grey[400],
            fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildExamView(ThemeData theme, Color generalTextColor) {
    // If no exam questions are available, show a placeholder
    if (_examQuestions.isEmpty) {
      return Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            _isProcessing
                ? 'Generating exam questions...'
                : 'No exam questions generated. Process file as Exam.',
            style: GoogleFonts.notoSans(color: generalTextColor),
          ),
        ),
      );
    }

    // For all exam types, show the exam content
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
				Text(
				  _processType == 'exam_free'
					? 'Exam (Free)'
					: _processType == 'exam_case'
					  ? 'Exam (Case)'
					  : 'Exam',
				  style: GoogleFonts.notoSans(
					fontSize: 22,
					fontWeight: FontWeight.bold,
					color: generalTextColor
				  ),
				  textAlign: TextAlign.center
				),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Font size controls
                    IconButton(
                      icon: Icon(Icons.text_decrease, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize > 12) _lessonFontSize -= 2;
                        });
                      },
                      tooltip: 'Decrease font size',
                    ),
                    IconButton(
                      icon: Icon(Icons.text_increase, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize < 24) _lessonFontSize += 2;
                        });
                      },
                      tooltip: 'Increase font size',
                    ),
                    IconButton(
                      icon: Icon(Icons.save, color: generalTextColor),
                      onPressed: () => _saveContent(),
                      tooltip: 'Save Content',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            ..._examQuestions.asMap().entries.map((entry) {
              int index = entry.key;
              ExamQuestion question = entry.value;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Question text
                  _DashboardPageState.buildLatexContent(
                    '${index + 1}. ${question.question}',
                    widget.isDarkMode,
                    _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                  ),
                  const SizedBox(height: 8),
                  // Options (only for multiple choice exams)
                  if (_processType == 'exam' && question.options.isNotEmpty)
                    ...List.generate(question.options.length, (optionIndex) {
                      return Padding(
                        padding: const EdgeInsets.only(left: 16.0, bottom: 4.0),
                        child: _DashboardPageState.buildLatexContent(
                          '${String.fromCharCode('A'.codeUnitAt(0) + optionIndex)}) ${question.options[optionIndex]}',
                          widget.isDarkMode,
                          _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                        ),
                      );
                    }),
                  const SizedBox(height: 12),
                  // Answer section - always in dedicated box below options/question
                  if (question.correctAnswer.isNotEmpty)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: widget.isDarkMode ? Colors.grey[800] : Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: widget.isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _processType == 'exam_free' ? 'Sample Response:' :
                            _processType == 'exam_case' ? 'Solution:' : 'Answer:',
                            style: GoogleFonts.notoSans(
                              fontWeight: FontWeight.bold,
                              color: generalTextColor,
                              fontSize: 14,
                            )
                          ),
                          const SizedBox(height: 8),
                          _processType == 'notes'
                            ? _buildNotesContent(question.correctAnswer)
                            : _DashboardPageState.buildLatexContent(
                                question.correctAnswer,
                                widget.isDarkMode,
                                _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                              ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 20),
                ],
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  void _submitQuiz() {
    final incorrectQuestions = _quizQuestions.where((q) {
      final userAnswer = _userAnswers[_quizQuestions.indexOf(q)];
      return userAnswer != q.correctAnswerIndex;
    }).toList();

    showDialog(
      context: context,
      builder: (context) => ExamResultsDialog(
        questions: _quizQuestions,
        incorrectQuestions: incorrectQuestions,
        textColor: generalTextColor,
        onGenerateNotes: () => _generateWeaknessNotes(incorrectQuestions),
        userAnswers: _userAnswers,
      ),
    );
  }


  Future<void> _generateWeaknessNotes(List<QuizQuestion> incorrectQuestions) async {
    final incorrectContent = incorrectQuestions.map((q) => q.question).join('\n');
    final prompt = '''Generate comprehensive notes focused on the topics covered in these quiz questions that the user answered incorrectly. Use the following source material to create the notes. Focus specifically on areas where the user demonstrated weakness in the quiz.

Incorrect Questions:
$incorrectContent

Source Material:
$_fileContent''';

    final response = await _geminiModel.generateContent([Content.text(prompt)]);
    if (response.text != null) {
      final weaknessNotes = response.text!;

      // Close the previous dialog
      Navigator.of(context).pop();

      // Show the results dialog with the weakness notes
      showDialog(
        context: context,
        builder: (context) => ExamResultsDialog(
          questions: _quizQuestions,
          incorrectQuestions: incorrectQuestions,
          textColor: generalTextColor,
          onGenerateNotes: () => _generateWeaknessNotes(incorrectQuestions),
          userAnswers: _userAnswers,
          weaknessNotes: weaknessNotes,
        ),
      );

      // Also show the notes in a separate dialog for viewing
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Improvement Notes', style: GoogleFonts.notoSans(color: generalTextColor)),
          content: SingleChildScrollView(
            child: MarkdownBody(
              data: weaknessNotes,
              styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
                p: GoogleFonts.notoSans(color: generalTextColor, fontSize: 16),
                h1: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 24),
                h2: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 20),
                h3: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 18),
                h4: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 16),
                h5: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 14),
                h6: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 12),
                code: GoogleFonts.sourceCodePro(backgroundColor: Theme.of(context).cardColor, color: generalTextColor),
                blockquote: GoogleFonts.notoSans(color: generalTextColor.withOpacity(0.6)),
                strong: GoogleFonts.notoSans(fontWeight: FontWeight.bold, color: generalTextColor),
                em: GoogleFonts.notoSans(fontStyle: FontStyle.italic, color: generalTextColor),
                listBullet: GoogleFonts.notoSans(color: generalTextColor),
                checkbox: GoogleFonts.notoSans(color: generalTextColor),
              ),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Download PDF', style: GoogleFonts.notoSans(color: generalTextColor)),
              onPressed: () async {
                Navigator.of(context).pop();
                // Use the enhanced download function for improvement notes
                await _downloadImprovementNotes(weaknessNotes);
              },
            ),
            TextButton(
              child: Text('Close', style: GoogleFonts.notoSans(color: generalTextColor)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to generate improvement notes.')),
      );
    }
  }


  List<pw.Widget> _buildPdfContent(String markdownText, String contentType) {
    if (contentType == 'exam' || contentType == 'exam_free' || contentType == 'exam_case') {
      return _buildPdfExamContent(markdownText);
    }
    else if (contentType == 'solution') {
      return _buildSolutionPdfContent(markdownText);
    }
    else {
      return _buildPdfNotesContent(markdownText, contentType); // Pass contentType here
    }
  }

  // New function with improved page breaking and LaTeX removal
List<pw.Widget> _buildPdfContentWithPageBreaking(String markdownText, String contentType) {
  Set<String> seenHeaders = {};
  List<pw.Widget> widgets = [];

  // Add header
  final String formattedTitle = contentType
      .replaceAll('_', ' ')
      .split(' ')
      .map((word) => word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : '')
      .join(' ');
  widgets.add(pw.Header(
    level: 1,
    child: pw.Text('Refactr AI - $formattedTitle',
        style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold,
            fontFallback: [
              stixTwoMathRegular!,
              notoSansSymbols!,
              bravura!,
              jetBrainsMonoRegular!,
              isocpRegular!,
              symbola!
            ])),
  ));

  int index = 0;
  while (index < markdownText.length) {
    int tableStart = markdownText.indexOf('[TABLE_START]', index);
    if (tableStart == -1) {
      widgets.addAll(_buildTextWidgets(markdownText.substring(index), seenHeaders));
      break;
    }
    if (tableStart > index) {
      widgets.addAll(_buildTextWidgets(markdownText.substring(index, tableStart), seenHeaders));
    }
    int tableEnd = markdownText.indexOf('[TABLE_END]', tableStart);
    if (tableEnd == -1) {
      widgets.addAll(_buildTextWidgets(markdownText.substring(tableStart), seenHeaders));
      break;
    }
    String tableMarkdown = markdownText.substring(tableStart + '[TABLE_START]'.length, tableEnd).trim();
    widgets.add(_buildPdfTable(tableMarkdown));
    index = tableEnd + '[TABLE_END]'.length;
  }
  return widgets;
}


List<pw.Widget> _buildSolutionPdfContent(String solutionText) {
  List<pw.Widget> widgets = [];

  // Add header
  widgets.add(pw.Header(
    level: 1,
    child: pw.Text('Refactr AI - Problem Solution',
        style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold)),
  ));

  // Use the PDF helpers to convert content
  widgets.addAll(convertLatexToPdfWidgets(
    removePreliminaryText(solutionText),  // Remove preliminary text
    notoSansRegular!,
    notoSansBold!,
    notoSansItalic!,
    [
      stixTwoMathRegular!,
      notoSansSymbols!,
      bravura!,
      jetBrainsMonoRegular!,
      isocpRegular!,
      symbola!
    ]
  ));

  return widgets;
}

// Cleaned PDF content functions without LaTeX
List<pw.Widget> _buildSolutionPdfContentCleaned(String solutionText) {
  List<pw.Widget> widgets = [];

  // Add header with proper font hierarchy
  widgets.add(pw.Header(
    level: 1,
    child: pw.Text('Refactr AI - Problem Solution',
        style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold,
            fontFallback: [
              stixTwoMathRegular!,
              notoSansSymbols!,
              bravura!,
              jetBrainsMonoRegular!,
              isocpRegular!,
              symbola!
            ])),
  ));

  // Process content with proper page breaking
  widgets.addAll(_buildCleanedTextWidgets(solutionText));

  return widgets;
}

List<pw.Widget> _buildPdfNotesContentCleaned(String markdownText, String contentType) {
  List<pw.Widget> widgets = [];
  final String formattedTitle = contentType
    .replaceAll('_', ' ')
    .split(' ')
    .map((word) => word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : '')
    .join(' ');
  // Add header with proper font hierarchy
  widgets.add(pw.Header(
    level: 1,
    child: pw.Text('Refactr AI - $formattedTitle',
        style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold,
            fontFallback: [
              stixTwoMathRegular!,
              notoSansSymbols!,
              bravura!,
              jetBrainsMonoRegular!,
              isocpRegular!,
              symbola!
            ])),
  ));

  // Process content with proper page breaking
  widgets.addAll(_buildCleanedTextWidgets(markdownText));

  return widgets;
}

List<pw.Widget> _buildPdfExamContentCleaned(String markdownText) {
  List<pw.Widget> widgets = [];

  // Add header
  widgets.add(pw.Header(
    level: 1,
    child: pw.Text('Refactr AI - Exam',
        style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold,
            fontFallback: [
              stixTwoMathRegular!,
              notoSansSymbols!,
              bravura!,
              jetBrainsMonoRegular!,
              isocpRegular!,
              symbola!
            ])),
  ));

  // Process content with proper page breaking
  widgets.addAll(_buildCleanedTextWidgets(markdownText));

  return widgets;
}



// New implementation of _buildPdfNotesContent that uses the PDF helpers
List<pw.Widget> _buildPdfNotesContent(String markdownText, String contentType) {
  // Preprocess markdown to remove duplicate headers
  final String cleanedMarkdown = markdownText.replaceAllMapped(
    RegExp(r'^\s*\*\*(.*?)\*\*[\s\n]+\1', multiLine: true),
    (match) => '**${match.group(1)}**',
  );

  
  List<pw.Widget> widgets = [];
  final String formattedTitle = contentType
    .replaceAll('_', ' ')
    .split(' ')
    .map((word) => word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : '')
    .join(' ');

  // Add header
  widgets.add(pw.Header(
    level: 1,
    child: pw.Text('Refactr AI - $formattedTitle',
        style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold,
            fontFallback: [
              stixTwoMathRegular!,
              notoSansSymbols!,
              bravura!,
              jetBrainsMonoRegular!,
              isocpRegular!,
              symbola!
            ])),
  ));

  // Use the cleaned markdown
  widgets.addAll(convertLatexToPdfWidgets(
    cleanedMarkdown,
    notoSansRegular!,
    notoSansBold!,
    notoSansItalic!,
    [
      stixTwoMathRegular!,
      notoSansSymbols!,
      bravura!,
      jetBrainsMonoRegular!,
      isocpRegular!,
      symbola!
    ]
  ));

  return widgets;
}

// Helper function to build cleaned text widgets with proper page breaking
// Helper function to build cleaned text widgets with proper page breaking
List<pw.Widget> _buildCleanedTextWidgets(String text) {
  List<pw.Widget> widgets = [];
  int index = 0;
  final Set<String> seenHeaders = {}; // Initialize the set for tracking headers

  while (index < text.length) {
    int tableStart = text.indexOf('[TABLE_START]', index);
    if (tableStart == -1) {
      // Pass the seenHeaders set to the function call
      widgets.addAll(_buildTextWidgets(text.substring(index), seenHeaders));
      break;
    }
    if (tableStart > index) {
      // Pass the seenHeaders set to the function call
      widgets.addAll(_buildTextWidgets(text.substring(index, tableStart), seenHeaders));
    }
    int tableEnd = text.indexOf('[TABLE_END]', tableStart);
    if (tableEnd == -1) {
      // Pass the seenHeaders set to the function call
      widgets.addAll(_buildTextWidgets(text.substring(tableStart), seenHeaders));
      break;
    }
    String tableMarkdown = text.substring(tableStart + '[TABLE_START]'.length, tableEnd).trim();
    widgets.add(_buildPdfTable(tableMarkdown));
    index = tableEnd + '[TABLE_END]'.length;
  }
  return widgets;
}

// Function to remove immediate duplicate sequences
String removeDuplicates(String text) {
  // This new regex requires the repeated sequence to be at least 2 characters long,
  // preventing it from matching single letters inside words like "Google".
  final pattern = RegExp(r'(.{2,}?)\1+');
  return text.replaceAllMapped(pattern, (match) => match.group(1)!);
}
// Function to build a bullet item with formatted text
// In class _DashboardPageState

pw.Widget _buildBulletItem(String text) {
  final formattedParagraph = _buildFormattedParagraph(text);
  return pw.Row(
    crossAxisAlignment: pw.CrossAxisAlignment.start,
    children: [
      pw.Text('• ', style: pw.TextStyle(
        fontSize: 12,
        font: notoSansSymbols, // Use a font known to have the bullet character
        fontFallback: [stixTwoMathRegular!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!]
      )),
      pw.Expanded(child: formattedParagraph),
    ],
  );
}
// In class _DashboardPageState

// In class _DashboardPageState

// In class _DashboardPageState

// In class _DashboardPageState

// In class _DashboardPageState

List<pw.Widget> _buildTextWidgets(String text, Set<String> seenHeaders) {
  // Helper to clean various OCR duplication artifacts like "TitleTitle" or "A. TitleA. Title"
  String cleanOcrDuplicates(String input) {
    String cleaned = input.trim();
    int len = cleaned.length;
    // This check handles cases where a string is perfectly duplicated.
    if (len >= 6 && len % 2 == 0) {
      int mid = len ~/ 2;
      if (cleaned.substring(0, mid) == cleaned.substring(mid)) {
        return cleaned.substring(0, mid);
      }
    }
    return cleaned;
  }

  final paragraphs = text.split('\n\n').where((p) => p.trim().isNotEmpty).toList();
  List<pw.Widget> widgets = [];

  for (final paragraph in paragraphs) {
    if (paragraph.startsWith('#')) {
      final headerLevel = paragraph.indexOf(' ');
      if (headerLevel > 0) {
        final level = paragraph.substring(0, headerLevel).length;
        String headerText = paragraph.substring(headerLevel + 1).trim();
        headerText = headerText.replaceAll(RegExp(r'\*\*|__|`'), '');

        headerText = cleanOcrDuplicates(headerText);

        if (seenHeaders.contains(headerText)) {
          continue;
        }
        seenHeaders.add(headerText);

        widgets.add(pw.SizedBox(height: level == 1 ? 16 : 12));
        widgets.add(pw.Text(
          headerText,
          style: pw.TextStyle(
            fontSize: level == 1 ? 20 : (level == 2 ? 18 : 16),
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold,
            fontFallback: [stixTwoMathRegular!, notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!]
          ),
        ));
        widgets.add(pw.SizedBox(height: 8));
      }
    } else if (paragraph.trim().startsWith('**') && paragraph.trim().endsWith('**')) {
      String headerText = paragraph.trim().substring(2, paragraph.trim().length - 2).trim();
      
      headerText = cleanOcrDuplicates(headerText);

      if (seenHeaders.contains(headerText)) {
        continue;
      }
      seenHeaders.add(headerText);

      widgets.add(pw.SizedBox(height: 16));
      widgets.add(pw.Text(
        headerText,
        style: pw.TextStyle(
          fontSize: 20,
          fontWeight: pw.FontWeight.bold,
          font: notoSansBold,
          fontFallback: [stixTwoMathRegular!, notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!]
        ),
      ));
      widgets.add(pw.SizedBox(height: 8));
    } else if (paragraph.startsWith('- ') || paragraph.startsWith('* ')) {
      String itemText = paragraph.substring(2).trim();
      widgets.add(_buildBulletItem(itemText));
      widgets.add(pw.SizedBox(height: 4));
    } else {
      widgets.add(_buildFormattedParagraph(paragraph));
      widgets.add(pw.SizedBox(height: 6));
    }
  }
  return widgets;
}

// Helper function to build formatted paragraph with markdown support
// In class _DashboardPageState

// In class _DashboardPageState

// In class _DashboardPageState

pw.Widget _buildFormattedParagraph(String text) {
  // Helper to clean various OCR duplication artifacts like "wordword"
  String cleanOcrDuplicates(String input) {
    String cleaned = input.trim();
    // This regex looks for a word of at least 4 characters followed immediately by itself.
    cleaned = cleaned.replaceAllMapped(RegExp(r'(\w{4,})\1'), (m) => m.group(1)!);

    int len = cleaned.length;
    // This check handles cases where a multi-word phrase is perfectly duplicated.
    if (len >= 8 && len % 2 == 0) {
      int mid = len ~/ 2;
      if (cleaned.substring(0, mid) == cleaned.substring(mid)) {
        return cleaned.substring(0, mid);
      }
    }
    return cleaned;
  }

  final cleanedText = cleanOcrDuplicates(text);
  final spans = <pw.TextSpan>[];
  final boldPattern = RegExp(r'\*\*(.*?)\*\*|__(.*?)__');
  final italicPattern = RegExp(r'\*(.*?)\*|_(.*?)_');
  final codePattern = RegExp(r'`(.*?)`');
  int lastEnd = 0;

  final allMatches = <RegExpMatch>[];
  allMatches.addAll(boldPattern.allMatches(cleanedText));
  allMatches.addAll(italicPattern.allMatches(cleanedText));
  allMatches.addAll(codePattern.allMatches(cleanedText));
  allMatches.sort((a, b) => a.start.compareTo(b.start));

  for (final match in allMatches) {
    if (match.start > lastEnd) {
      spans.add(pw.TextSpan(text: cleanedText.substring(lastEnd, match.start)));
    }
    final matchText = match.group(1) ?? match.group(2) ?? '';
    if (boldPattern.hasMatch(match.group(0)!)) {
      spans.add(pw.TextSpan(
        text: matchText,
        style: pw.TextStyle(fontWeight: pw.FontWeight.bold, font: notoSansBold),
      ));
    } else if (italicPattern.hasMatch(match.group(0)!)) {
      spans.add(pw.TextSpan(
        text: matchText,
        style: pw.TextStyle(fontStyle: pw.FontStyle.italic, font: notoSansItalic),
      ));
    } else if (codePattern.hasMatch(match.group(0)!)) {
      spans.add(pw.TextSpan(
        text: matchText,
        style: pw.TextStyle(font: jetBrainsMonoRegular),
      ));
    }
    lastEnd = match.end;
  }

  if (lastEnd < cleanedText.length) {
    spans.add(pw.TextSpan(text: cleanedText.substring(lastEnd)));
  }

  return pw.RichText(
    text: pw.TextSpan(
      children: spans,
      style: pw.TextStyle(
        fontSize: 12,
        height: 1.5,
        font: notoSansRegular,
        fontFallback: [
          stixTwoMathRegular!,
          notoSansSymbols!,
          bravura!,
          jetBrainsMonoRegular!,
          isocpRegular!,
          symbola!
        ],
      ),
    ),
  );
}



pw.Widget _buildPdfTable(String tableMarkdown) {
  List<String> lines = tableMarkdown.split('\n');
  if (lines.length < 3) return pw.Text('Invalid table');
  List<String> headers = lines[0].split('|').map((cell) => cell.trim()).toList();
  if (headers.first.isEmpty) headers.removeAt(0);
  if (headers.last.isEmpty) headers.removeLast();
  List<List<String>> rows = [];
  for (int i = 2; i < lines.length; i++) {
    List<String> cells = lines[i].split('|').map((cell) => cell.trim()).toList();
    if (cells.first.isEmpty) cells.removeAt(0);
    if (cells.last.isEmpty) cells.removeLast();
    if (cells.length == headers.length) rows.add(cells);
  }
  return pw.Table(
    border: pw.TableBorder.all(),
    children: [
      pw.TableRow(
        children: headers.map((header) => pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(header, style: pw.TextStyle(fontWeight: pw.FontWeight.bold, font: notoSansBold)),
        )).toList(),
      ),
      ...rows.map((row) => pw.TableRow(
        children: row.map((cell) => pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: _buildFormattedParagraph(cell), // Support formatting in cells
        )).toList(),
      )),
    ],
  );
}

// Table functionality removed as requested

  List<pw.Widget> _buildPdfExamContent(String markdownText) {
    List<String> lines = markdownText.split('\n');
    List<pw.Widget> widgets = [];

    for (var line in lines) {
      if (line.trim().isEmpty) continue;

      // Regular text line (simplified for now)
      widgets.add(pw.Text(line,
          style: pw.TextStyle(
              fontSize: 12,
              font: stixTwoMathRegular!,
              fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!])));
      widgets.add(pw.SizedBox(height: 8));
    }
    return widgets;
  }

  // Helper method to handle LaTeX in PDF export
  Future<pw.Widget> _buildPdfLatexLine(String line) async {
    // Check for LaTeX expressions
    final latexRegExp = RegExp(r'(\$\$.*?\$\$|\$.*?\$|\\\[.*?\\\]|\\\(.*?\\\))', dotAll: true);
    final matches = latexRegExp.allMatches(line);

    if (matches.isEmpty) {
      // No LaTeX expressions found, return regular text
      return pw.Text(
        line,
        style: pw.TextStyle(
          fontSize: 12,
          font: stixTwoMathRegular!,
          fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
        ),
      );
    }

    // Process line with LaTeX expressions
    List<pw.Widget> lineWidgets = [];
    int lastEnd = 0;

    for (final match in matches) {
      // Add text before LaTeX expression
      if (match.start > lastEnd) {
        lineWidgets.add(
          pw.Text(
            line.substring(lastEnd, match.start),
            style: pw.TextStyle(
              fontSize: 12,
              font: stixTwoMathRegular!,
              fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
            ),
          ),
        );
      }

      // Render LaTeX expression as image
      final latexExpression = match.group(0)!;
      try {
        // Render LaTeX as image
        final Uint8List? imageData = await LatexImageRenderer.renderLatexAsImage(
          latexExpression,
          14.0, // Font size for LaTeX
          false, // Use light mode for PDF
        );

        if (imageData != null) {
          // Add LaTeX image to PDF
          lineWidgets.add(
            pw.Image(
              pw.MemoryImage(imageData),
              fit: pw.BoxFit.contain,
              height: 24, // Adjust height as needed
            ),
          );
        } else {
          // Fallback to text if image rendering fails
          lineWidgets.add(
            pw.Text(
              latexExpression,
              style: pw.TextStyle(
                fontSize: 12,
                font: stixTwoMathRegular!,
                fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
              ),
            ),
          );
        }
      } catch (e) {
        // Fallback to text if image rendering fails
        lineWidgets.add(
          pw.Text(
            latexExpression,
            style: pw.TextStyle(
              fontSize: 12,
              font: stixTwoMathRegular!,
              fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
            ),
          ),
        );
      }

      lastEnd = match.end;
    }

    // Add any remaining text after the last LaTeX expression
    if (lastEnd < line.length) {
      lineWidgets.add(
        pw.Text(
          line.substring(lastEnd),
          style: pw.TextStyle(
            fontSize: 12,
            font: stixTwoMathRegular!,
            fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
          ),
        ),
      );
    }

    // Return a row with all the widgets
    return pw.Row(
      children: lineWidgets,
      crossAxisAlignment: pw.CrossAxisAlignment.center,
    );
  }

// Convert text to superscript Unicode characters
String _convertToSuperscript(String text) {
  final Map<String, String> superscriptMap = {
    '0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴',
    '5': '⁵', '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹',
    '+': '⁺', '-': '⁻', '=': '⁼', '(': '⁽', ')': '⁾',
    'n': 'ⁿ', 'i': 'ⁱ', 'a': 'ᵃ', 'b': 'ᵇ', 'c': 'ᶜ',
    'd': 'ᵈ', 'e': 'ᵉ', 'f': 'ᶠ', 'g': 'ᵍ', 'h': 'ʰ',
    'j': 'ʲ', 'k': 'ᵏ', 'l': 'ˡ', 'm': 'ᵐ', 'o': 'ᵒ',
    'p': 'ᵖ', 'r': 'ʳ', 's': 'ˢ', 't': 'ᵗ', 'u': 'ᵘ',
    'v': 'ᵛ', 'w': 'ʷ', 'x': 'ˣ', 'y': 'ʸ', 'z': 'ᶻ',
  };

  String result = '';
  for (int i = 0; i < text.length; i++) {
    final char = text[i].toLowerCase();
    result += superscriptMap[char] ?? char;
  }
  return result;
}

// Convert text to subscript Unicode characters
String _convertToSubscript(String text) {
  final Map<String, String> subscriptMap = {
    '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
    '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉',
    '+': '₊', '-': '₋', '=': '₌', '(': '₍', ')': '₎',
    'a': 'ₐ', 'e': 'ₑ', 'h': 'ₕ', 'i': 'ᵢ', 'j': 'ⱼ',
    'k': 'ₖ', 'l': 'ₗ', 'm': 'ₘ', 'n': 'ₙ', 'o': 'ₒ',
    'p': 'ₚ', 'r': 'ᵣ', 's': 'ₛ', 't': 'ₜ', 'u': 'ᵤ',
    'v': 'ᵥ', 'x': 'ₓ',
  };

  String result = '';
  for (int i = 0; i < text.length; i++) {
    final char = text[i].toLowerCase();
    result += subscriptMap[char] ?? char;
  }
  return result;
}

List<pw.Widget> _parseLineToWidgets(String line) {
  line = line.replaceAllMapped(RegExp(r"'(.+?)'"), (match) => match.group(1)!);
  List<pw.Widget> widgets = [];
  final pattern = RegExp(
    r'(\*\*(.*?)\*\*|__(.*?)__)|(\*(.*?)\*|_(.*?)_)|(<sup>(.*?)</sup>|\^(.*?)(\s|$))|(<sub>(.*?)</sub>)|(~~(.*?)~~)|(`(.*?)`)',
    dotAll: true,
  );

  final matches = pattern.allMatches(line);

  for (final match in matches) {
    // Bold (both ** and __ syntax)
    if (match.group(1) != null) {
      final content = match.group(2) ?? match.group(3) ?? '';
      widgets.add(pw.Text(
        content,
        style: pw.TextStyle(
          fontWeight: pw.FontWeight.bold,
          font: notoSansBold,
        ),
      ));
    }
    // Italics (both * and _ syntax)
    else if (match.group(4) != null) {
      final content = match.group(5) ?? match.group(6) ?? '';
      widgets.add(pw.Text(
        content,
        style: pw.TextStyle(
          fontStyle: pw.FontStyle.italic,
          font: notoSansItalic,
        ),
      ));
    }
    // Superscript (both <sup> and ^ syntax)
    else if (match.group(7) != null) {
      final content = match.group(8) ?? match.group(9) ?? '';
      // Convert to Unicode superscript if possible
      String superscriptText = _convertToSuperscript(content);
      widgets.add(pw.Text(
        superscriptText,
        style: pw.TextStyle(
          fontSize: 10,
          font: stixTwoMathRegular!,
          fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
        ),
      ));
    }
    // Subscript (<sub> syntax)
    else if (match.group(11) != null) {
      final content = match.group(12) ?? '';
      // Convert to Unicode subscript if possible
      String subscriptText = _convertToSubscript(content);
      widgets.add(pw.Text(
        subscriptText,
        style: pw.TextStyle(
          fontSize: 10,
          font: stixTwoMathRegular!,
          fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
        ),
      ));
    }
    // Strikethrough
    else if (match.group(13) != null) {
      widgets.add(pw.Text(
        match.group(14)!,
        style: pw.TextStyle(
          decoration: pw.TextDecoration.lineThrough,
        ),
      ));
    }
    // Code/monospace
    else if (match.group(15) != null) {
      widgets.add(pw.Text(
        match.group(16)!,
        style: pw.TextStyle(
          font: jetBrainsMonoRegular,
        ),
      ));
    }
  }

  return widgets;
}

MarkdownStyleSheet _getMarkdownStyleSheet(ThemeData theme, Color textColor) {
  return MarkdownStyleSheet(
    p: GoogleFonts.notoSans(color: textColor, fontSize: 16),
    h1: GoogleFonts.notoSans(
        color: textColor, fontWeight: FontWeight.bold, fontSize: 24),
    h2: GoogleFonts.notoSans(
        color: textColor, fontWeight: FontWeight.bold, fontSize: 20),
    h3: GoogleFonts.notoSans(
        color: textColor, fontWeight: FontWeight.bold, fontSize: 18),
    strong: GoogleFonts.notoSans(
        color: textColor, fontWeight: FontWeight.w700),
    em: GoogleFonts.notoSans(color: textColor, fontStyle: FontStyle.italic),
    code: GoogleFonts.sourceCodePro(
        fontSize: 14, backgroundColor: theme.cardColor, color: textColor),
  );
}


// In class _DashboardPageState
// In class _DashboardPageState

// In class _DashboardPageState

// In class _DashboardPageState

Future<void> _downloadContent() async {
  if (_geminiOutput == null) return;

  try {
    String defaultTitle = '';
    switch (_processType) {
      case 'lesson_plan':
        defaultTitle = 'Lesson_Plan_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
        break;
      case 'worksheet':
        defaultTitle = 'Worksheet_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
        break;
      case 'workbook':
        defaultTitle = 'Workbook_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
        break;
      case 'homework_guide':
        defaultTitle = 'Homework_Guide_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
        break;
      case 'project_ideas':
        defaultTitle = 'Project_Ideas_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
        break;
      case 'case_studies':
        defaultTitle = 'Case_Studies_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
        break;
      case 'scheme_of_work':
        defaultTitle = 'Scheme_of_Work_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
        break;
      default:
        defaultTitle = '${_processType.replaceAll('_', ' ')}_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
    }

    final pdf = pw.Document();
    final pdfContent = _buildPdfContentWithPageBreaking(_geminiOutput!, _processType);

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(30),
        header: (context) {
          // Only show the header on pages 2 and onwards
          if (context.pageNumber == 1) {
            return pw.Container(); // Return an empty container for the first page
          }
          return pw.Container(
            alignment: pw.Alignment.centerLeft,
            margin: const pw.EdgeInsets.only(bottom: 20),
            child: pw.Text(
              'Refactr AI',
              style: pw.TextStyle(
                fontSize: 10,
                color: PdfColors.grey,
                font: notoSansRegular,
                fontFallback: [stixTwoMathRegular!, notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!]
              ),
            ),
          );
        },
        build: (context) => pdfContent,
        footer: (context) => pw.Container(
          margin: const pw.EdgeInsets.only(top: 20),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'Refactr AI',
                style: pw.TextStyle(
                  fontSize: 10,
                  color: PdfColors.grey,
                  font: notoSansRegular,
                   fontFallback: [stixTwoMathRegular!, notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!]
                ),
              ),
              pw.Text(
                'Page ${context.pageNumber}',
                style: pw.TextStyle(
                  fontSize: 10,
                  color: PdfColors.grey,
                  font: notoSansRegular,
                   fontFallback: [stixTwoMathRegular!, notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!]
                ),
              ),
            ],
          ),
        ),
      ),
    );

    final Uint8List pdfBytes = await pdf.save();

    if (kIsWeb) {
      final blob = html.Blob([pdfBytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download = '$defaultTitle.pdf'
        ..setAttribute('type', 'application/pdf');

      html.document.body!.children.add(anchor);
      anchor.click();

      Future.delayed(const Duration(seconds: 2), () {
        html.Url.revokeObjectUrl(url);
        html.document.body!.children.remove(anchor);
      });
    } else {
      if (io.Platform.isAndroid || io.Platform.isIOS) {
        if (!await _requestStoragePermission()) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Storage permission denied')),
          );
          return;
        }
      }

      final String? outputPath = await FilePicker.platform.saveFile(
        dialogTitle: 'Choose Download Location',
        fileName: '$defaultTitle.pdf',
        allowedExtensions: ['pdf'],
        type: FileType.custom,
        lockParentWindow: true,
      );

      if (outputPath != null) {
        final file = io.File(outputPath);
        await file.writeAsBytes(pdfBytes);

        if (io.Platform.isAndroid || io.Platform.isIOS) {
          try {
            await file.setLastModified(DateTime.now());
          } catch (e) {
            // Ignore permission errors for file metadata
          }
        }
      } else {
        return;
      }
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('PDF downloaded successfully! File is accessible by external apps.'),
          duration: Duration(seconds: 3),
        ),
      );
    }
  } catch (e) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Download failed: ${e.toString()}', overflow: TextOverflow.ellipsis)),
      );
    }
  }
}

  Future<void> _saveContent() async {
    if (_geminiOutput == null) return;

    try {
      // Generate a default title
      final defaultTitle = '${_processType.replaceAll('_', ' ').toUpperCase()} - ${DateTime.now().toString().substring(0, 16)}';

      // Show dialog to get title and optionally select course
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => _SaveContentDialog(
          defaultTitle: defaultTitle,
          isDarkMode: widget.isDarkMode,
          generalTextColor: generalTextColor,
        ),
      );

      if (result == null || result['title'] == null || result['title'].isEmpty) {
        return;
      }

      final String title = result['title'];
      final String? courseId = result['courseId'];

      // Save content metadata locally (no Supabase integration)
      await _saveContentLocally(title, _geminiOutput!, _processType, courseId);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Content saved successfully to local storage'),
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving content: ${e.toString()}', overflow: TextOverflow.ellipsis)),
      );
    }
  }

  Future<void> _saveContentLocally(String title, String content, String contentType, String? courseId) async {
    try {
      // Use the local storage service for consistency
      final localStorageService = LocalStorageService();
      await localStorageService.saveContent(
        title: title,
        content: content,
        contentType: contentType,
        courseId: courseId,
      );
    } catch (e) {
      throw Exception('Failed to save content locally: $e');
    }
  }

  Future<bool> _requestStoragePermission() async {
    if (io.Platform.isAndroid || io.Platform.isIOS) {
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        status = await Permission.storage.request();
        return status.isGranted;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  // Function to download improvement notes
  Future<void> _downloadImprovementNotes(String notes) async {
    if (notes.isEmpty) return;

    try {
      final defaultTitle = 'Improvement_Notes_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';

      // Create PDF document with improvement notes
      final pdf = pw.Document();

      // Clean the notes content (remove LaTeX)
      final cleanedNotes = removeLatex(notes);

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          build: (context) => [
            pw.Header(
              level: 1,
              child: pw.Text('Refactr AI - Improvement Notes',
                  style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                      font: notoSansBold,
                      fontFallback: [
                        stixTwoMathRegular!,
                        notoSansSymbols!,
                        bravura!,
                        jetBrainsMonoRegular!,
                        isocpRegular!,
                        symbola!
                      ])),
            ),
            pw.SizedBox(height: 20),
            ..._buildCleanedTextWidgets(cleanedNotes),
          ],
          footer: (context) => pw.Container(
            alignment: pw.Alignment.centerRight,
            margin: const pw.EdgeInsets.only(top: 10),
            child: pw.Text(
              'Page ${context.pageNumber}',
              style: pw.TextStyle(
                fontSize: 10,
                color: PdfColors.grey,
                font: notoSansRegular,
                fontFallback: [
                  stixTwoMathRegular!,
                  notoSansSymbols!,
                  bravura!,
                  jetBrainsMonoRegular!,
                  isocpRegular!,
                  symbola!
                ]
              ),
            ),
          ),
        ),
      );

      final Uint8List pdfBytes = await pdf.save();

      if (kIsWeb) {
        // Web download with proper MIME type
        final blob = html.Blob([pdfBytes], 'application/pdf');
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.document.createElement('a') as html.AnchorElement
          ..href = url
          ..style.display = 'none'
          ..download = '$defaultTitle.pdf'
          ..setAttribute('type', 'application/pdf');

        html.document.body!.children.add(anchor);
        anchor.click();

        Future.delayed(const Duration(seconds: 2), () {
          html.Url.revokeObjectUrl(url);
          html.document.body!.children.remove(anchor);
        });
      } else {
        // Mobile/Desktop download with file picker
        if (io.Platform.isAndroid || io.Platform.isIOS) {
          if (!await _requestStoragePermission()) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Storage permission denied')),
            );
            return;
          }
        }

        final String? outputPath = await FilePicker.platform.saveFile(
          dialogTitle: 'Save Improvement Notes',
          fileName: '$defaultTitle.pdf',
          allowedExtensions: ['pdf'],
          type: FileType.custom,
          lockParentWindow: true,
        );

        if (outputPath != null) {
          final file = io.File(outputPath);
          await file.writeAsBytes(pdfBytes);

          // Set proper file permissions for external app access
          if (io.Platform.isAndroid || io.Platform.isIOS) {
            try {
              await file.setLastModified(DateTime.now());
            } catch (e) {
              // Ignore permission errors for file metadata
            }
          }
        } else {
          return;
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Improvement notes downloaded successfully! File is accessible by external apps.'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Download failed: ${e.toString()}', overflow: TextOverflow.ellipsis)),
        );
      }
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_chatScrollController.hasClients) {
        _chatScrollController.animateTo(
          _chatScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendChatMessage() async {
    final message = _chatController.text.trim();
    if (message.isEmpty) return;

    if (mounted) {
      setState(() {
        _chatMessages.add(ChatMessage(message, true));
        _chatMessages.add(ChatMessage('Typing...', false, isTyping: true));
      });
      _scrollToBottom();
    }
    _chatController.clear();

    try {
      final response = await _chatSession.sendMessage(Content.text(message));
      if (mounted) {
        setState(() {
          _chatMessages.removeWhere((m) => m.isTyping);
          _chatMessages.add(ChatMessage(response.text ?? 'No response', false));
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _chatMessages.removeWhere((m) => m.isTyping);
          _chatMessages.add(ChatMessage('Sorry, an error occurred.', false));
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat error: ${e.toString()}', overflow: TextOverflow.ellipsis)),
        );
      }
    } finally {
      if (mounted) {
        _scrollToBottom();
      }
    }
  }

  void _initTts() {
    flutterTts = FlutterTts();
    _setAwaitSpeakCompletion();

    flutterTts.setCompletionHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
        // Update lesson playing state when TTS completes
        if (_lessonPlaying && _currentLessonStepIndex >= _lessonSteps.length - 1) {
          _lessonPlaying = false;
        }
      });
    });

    flutterTts.setCancelHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });
  }

  void _setAwaitSpeakCompletion() async {
    flutterTts.awaitSpeakCompletion(true);
  }

  Future<void> _speak(String text) async {
    if (text.isNotEmpty && !_isNarrationMuted) {
      await flutterTts.setVolume(volume);
      // Use the lesson speed to control the speech rate
      await flutterTts.setSpeechRate(_lessonSpeed * 0.5);
      await flutterTts.setPitch(pitch);
      await flutterTts.setLanguage(_ttsLanguage);

      String cleanedText = text
          .replaceAll(RegExp(r'<sup>(.*?)</sup>'), r'$1') // Keep superscript content
          .replaceAll(RegExp(r'<sub>(.*?)</sub>'), r'$1') // Keep subscript content
          .replaceAll(RegExp(r'(\w+)\^(\w+)'), r'$1 to the power of $2') // Handle exponents
          .replaceAll(RegExp(r'[*~`#-]'), '')
          .replaceAll(RegExp(r'[\n\r]'), ' ')
          .trim();

      if (ttsState == TtsState.playing) {
        var result = await flutterTts.pause();
        if (result == 1) setState(() => ttsState = TtsState.paused);
      } else {
        var result = await flutterTts.speak(cleanedText);
        if (result == 1) setState(() => ttsState = TtsState.playing);
      }
    }
  }

  Future<void> _stop() async {
    var result = await flutterTts.stop();
    if (result == 1) setState(() => ttsState = TtsState.stopped);
  }

  Future<void> _pause() async {
    if (ttsState == TtsState.playing) {
      var result = await flutterTts.pause();
      if (result == 1) setState(() => ttsState = TtsState.paused);
    }
  }

  Widget _buildTtsControls() {
    return Container(
      color: widget.isDarkMode ? Colors.grey[900] : Colors.grey[100],
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          IconButton(
            icon: Icon(
                isPlaying ? Icons.pause : Icons.play_arrow,
                color: generalTextColor),
            onPressed: () => isPlaying
                ? _pause()
                : _speak(_geminiOutput ?? 'No text to speak'),
          ),
          IconButton(
            icon: Icon(Icons.stop, color: generalTextColor),
            onPressed: _stop,
          ),
          Expanded(
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Theme.of(context).colorScheme.primary,
                inactiveTrackColor: Colors.grey,
                thumbColor: Theme.of(context).colorScheme.primary,
                overlayColor:
                Theme.of(context).colorScheme.primary.withOpacity(0.3),
                trackHeight: 4.0,
                thumbShape: RoundSliderThumbShape(enabledThumbRadius: 10.0),
                overlayShape: RoundSliderOverlayShape(overlayRadius: 16.0),
              ),
              child: Slider(
                value: rate,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: "Speed",
                activeColor: widget.isDarkMode ? Colors.white : Colors.black,
                inactiveColor: Colors.grey,
                onChanged: (double value) {
                  setState(() => rate = value);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;

    return Scaffold(
appBar: AppBar(
  backgroundColor: theme.colorScheme.surface,
  surfaceTintColor: Colors.transparent,
  title: Text(
    'Grasp It Faster',
    style: TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.bold,
      color: theme.colorScheme.onSurface,
    ),
  ),
  actions: [
    IconButton(
      icon: Icon(Icons.save_alt, color: theme.colorScheme.onSurface),
      tooltip: 'Saved Content',
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SavedContentPage(
              isDarkMode: widget.isDarkMode,
              toggleTheme: widget.toggleTheme,
            ),
          ),
        );
      },
    ),
    IconButton(
      icon: Icon(
        widget.isDarkMode ? Icons.light_mode : Icons.dark_mode,
        color: theme.colorScheme.onSurface,
      ),
      onPressed: widget.toggleTheme,
    ),
  ],
),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildFileSection(theme, generalTextColor,
                buttonTextColor, buttonBackground),
            const SizedBox(height: 20),
            _buildProcessingControls(theme, generalTextColor,
                buttonTextColor, buttonBackground),
            const SizedBox(height: 20),
            _buildContentDisplay(theme, generalTextColor),
          ],
        ),
      ),
      // Bottom navigation bar removed entirely
    );
  }

Widget _buildFileSection(ThemeData theme, Color generalTextColor,
    Color buttonTextColor, Color buttonBg) {

// Add this helper function to determine the file icon
Icon _getFileIcon(String fileName) {
  final extension = fileName.split('.').last.toLowerCase();
  final iconColor = generalTextColor.withOpacity(0.6);
  final iconSize = 18.0;
  switch (extension) {
    case 'pdf':
      return Icon(Icons.picture_as_pdf, color: iconColor, size: iconSize);
    case 'txt':
      return Icon(Icons.description, color: iconColor, size: iconSize);
    case 'doc':
    case 'docx':
      return Icon(Icons.article, color: iconColor, size: iconSize);
    case 'jpg':
    case 'jpeg':
    case 'png':
      return Icon(Icons.image, color: iconColor, size: iconSize);
    case 'mp3':
      return Icon(Icons.audiotrack, color: iconColor, size: iconSize);
    case 'ppt':
    case 'pptx':
      return Icon(Icons.slideshow, color: iconColor, size: iconSize);
    case 'xls':
    case 'xlsx':
      return Icon(Icons.table_chart, color: iconColor, size: iconSize);
    case 'csv':
      return Icon(Icons.grid_on, color: iconColor, size: iconSize);
    default:
      return Icon(Icons.insert_drive_file, color: iconColor, size: iconSize);
  }
}

  return Column(
    children: [
      Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select Files',
                style: GoogleFonts.notoSans(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: generalTextColor,
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                icon: Icon(Icons.cloud_upload, color: buttonTextColor),
                label: Text('Select Learning Material(s)',
                    style: GoogleFonts.notoSans(color: buttonTextColor)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: buttonBg,
                  padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                  minimumSize: const Size(double.infinity, 50),
                ),
                onPressed: _isUploading ? null : _pickFile,
              ),
              if (_pickedFiles.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Selected Files:',
                          style: GoogleFonts.notoSans(
                              color: generalTextColor.withOpacity(0.8))),
..._pickedFiles.map((fileRange) => Row(
  mainAxisAlignment: MainAxisAlignment.spaceBetween,
  children: [
    Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _getFileIcon(fileRange.file.name),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  fileRange.file.name,
                  style: GoogleFonts.notoSans(
                      color: generalTextColor.withOpacity(0.6),
                      fontSize: 14),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          if (fileRange.file.name.toLowerCase().endsWith('.pdf'))
            Row(
              children: [
                SizedBox(
                  width: 80,
                  child: TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: 'Start Page',
                      hintStyle: GoogleFonts.notoSans(fontSize: 12),
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    ),
                    style: GoogleFonts.notoSans(fontSize: 12, color: generalTextColor),
                    onChanged: (value) => fileRange.startPage = value.isEmpty ? null : int.tryParse(value),
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 80,
                  child: TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: 'End Page',
                      hintStyle: GoogleFonts.notoSans(fontSize: 12),
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    ),
                    style: GoogleFonts.notoSans(fontSize: 12, color: generalTextColor),
                    onChanged: (value) => fileRange.endPage = value.isEmpty ? null : int.tryParse(value),
                  ),
                ),
              ],
            ),
        ],
      ),
    ),
    IconButton(
      icon: Icon(Icons.delete, color: Colors.red),
      onPressed: () => _deleteFile(fileRange),
    ),
  ],
)).toList(),
                    ],
                  ),
                ),
              if (_isUploading)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: LinearProgressIndicator(value: _uploadProgress),
                ),
              if (_isUploading)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    'Uploading: ${(_uploadProgress * 100).toStringAsFixed(0)}%',
                    style: GoogleFonts.notoSans(color: generalTextColor),
                  ),
                ),
            ],
          ),
        ),
      ),
      const SizedBox(height: 16),
      Text(
        'Or',
        style: GoogleFonts.notoSans(
          color: generalTextColor,
          fontSize: 16,
        ),
      ),
      const SizedBox(height: 16),
      Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Enter Text',
                style: GoogleFonts.notoSans(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: generalTextColor,
                ),
              ),
              const SizedBox(height: 8),
              // LaTeX-aware text input with preview
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextField(
                    controller: _textInputController,
                    maxLines: 2,
                    decoration: InputDecoration(
                      hintText: 'Paste or type your text here...',
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                    ),
                    style: GoogleFonts.notoSans(color: generalTextColor),
                    onChanged: (value) {
                      // Trigger rebuild to update LaTeX preview
                      setState(() {});
                    },
                  ),
                  // LaTeX preview section
                  if (_textInputController.text.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 8),
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: widget.isDarkMode ? Colors.grey[800] : Colors.grey[100],
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: widget.isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Preview:',
                            style: GoogleFonts.notoSans(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: generalTextColor.withOpacity(0.7),
                            ),
                          ),
                          const SizedBox(height: 4),
                          _DashboardPageState.buildLatexContent(
                            _textInputController.text,
                            widget.isDarkMode,
                            14.0,
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    ],
  );
}
  
  List<DropdownMenuItem<String>> _buildCategorizedDropdownItems() {
    final items = <DropdownMenuItem<String>>[];
    final headerStyle = GoogleFonts.notoSans(
      color: Colors.grey.shade600,
      fontWeight: FontWeight.bold,
      fontSize: 12,
    );
    final itemStyle = GoogleFonts.notoSans(color: generalTextColor);

    // Helper to add a non-selectable header
    void addHeader(String text) {
      items.add(DropdownMenuItem<String>(
        enabled: false,
        child: Padding(
          padding: const EdgeInsets.only(top: 8.0, bottom: 4.0),
          child: Text(text.toUpperCase(), style: headerStyle),
        ),
      ));
    }

    // Helper to add a selectable item
    void addItem(String value, String text) {
      items.add(DropdownMenuItem(
        value: value,
        child: Text(text, style: itemStyle),
      ));
    }

    // LEARN Category
    addHeader('Learn');
    addItem('notes', 'Generate Notes');
    addItem('notes_qa', 'Generate Notes (Q&A)');
    addItem('cheatsheet', 'Create Cheatsheet');
    addItem('summary', 'Create Summary');
    if (_hasMP3) addItem('transcript', 'Create Transcript');
    if (_hasMP3) addItem('minutes', 'Create Meeting Minutes');
    addItem('interactive_lesson', 'Interactive Lesson');
    addItem('chat', 'AI Tutor / Chat with Content');
    addItem('action_item_extractor', 'Action-Item Extractor');

    // ASSESS Category
    addHeader('Assess');
    addItem('flashcards', 'Make Flashcards');
    addItem('quiz', 'Generate Quiz');
    addItem('quiz_fill_in_the_blank', 'Quiz (Fill-in-the-Blank)');
    addItem('exam', 'Exam');
    addItem('exam_free', 'Exam (Free Response)');
    addItem('exam_case', 'Exam (Case Question)');
    addItem('paper_grader', 'Paper Feedback');

    // CREATE & BRAINSTORM Category
    addHeader('Create & Brainstorm');
    addItem('project_ideas', 'Project Ideas');
    addItem('case_studies', 'Case Studies');
    addItem('experiment', 'Experiment/Lab');

    // TEACH Category
    addHeader('Teach');
    addItem('lesson_plan', 'Create Lesson Plan');
    addItem('worksheet', 'Worksheet');
    addItem('workbook', 'Generate Workbook');
    addItem('homework_guide', 'Homework Guide');
    addItem('discussion_prompts', 'Discussion Prompts');
    addItem('scheme_of_work', 'Scheme of Work');
    
    // CAREER PREPARATION Category
    addHeader('Career Preparation');
    addItem('resume_feedback', 'Resume/CV Feedback');
    addItem('cover_letter_feedback', 'Cover Letter Feedback');
    addItem('skills_gap_analysis', 'Skills Gap Analysis');
    addItem('interview_practice', 'Interview Practice Chatbot');

    return items;
  }

  Widget _buildProcessingControls(ThemeData theme, Color generalTextColor,
      Color buttonTextColor, Color buttonBg) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            DropdownButtonFormField<String>(
              value: _processType,
              decoration: InputDecoration(
                labelText: 'Processing Type',
                labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                border: const OutlineInputBorder(),
              ),
              isExpanded: true,
              items: _buildCategorizedDropdownItems(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _processType = value;
                    _geminiOutput = null;
                    _flashcards = [];
                    _quizQuestions = [];
                    _examQuestions = [];
                    _chatMessages = [];
                    _readingGradeLevel = null;
                    _difficultyLevel = null;
                    _quizTimeLimitMinutes = null;
                    _cancelQuizTimer();
                    _timeRemaining = Duration.zero;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            // Language dropdown
            DropdownButtonFormField<String>(
              value: _outputLanguage,
              decoration: InputDecoration(
                labelText: 'Output Language',
                labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                border: const OutlineInputBorder(),
              ),
              style: GoogleFonts.notoSans(color: generalTextColor),
              items: const [
                DropdownMenuItem(value: 'English', child: Text('English')),
                DropdownMenuItem(value: 'Chichewa', child: Text('Chichewa')),
                DropdownMenuItem(value: 'Chitumbuka', child: Text('Chitumbuka')),
                DropdownMenuItem(value: 'Swahili', child: Text('Swahili')),
                DropdownMenuItem(value: 'Shona', child: Text('Shona')),
                DropdownMenuItem(value: 'Zulu', child: Text('Zulu')),
              ],
              onChanged: (value) {
                setState(() {
                  _outputLanguage = value;
                });
              },
            ),
            const SizedBox(height: 16),
            if (_processType != 'quiz' && _processType != 'exam' && _processType != 'exam_free' && _processType != 'exam_case' && _processType != 'scheme_of_work' && _processType != 'worksheet' && _processType != 'workbook' && _processType != 'skills_gap_analysis')
              DropdownButtonFormField<String>(
                value: _readingGradeLevel,
                decoration: InputDecoration(
                  labelText: 'Reading Grade Level',
                  labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                  border: const OutlineInputBorder(),
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
                items: const [
                  DropdownMenuItem(value: 'Grade 1', child: Text('Grade 1')),
                  DropdownMenuItem(value: 'Grade 2', child: Text('Grade 2')),
                  DropdownMenuItem(value: 'Grade 3', child: Text('Grade 3')),
                  DropdownMenuItem(value: 'Grade 4', child: Text('Grade 4')),
                  DropdownMenuItem(value: 'Grade 5', child: Text('Grade 5')),
                  DropdownMenuItem(value: 'Grade 6', child: Text('Grade 6')),
                  DropdownMenuItem(value: 'Grade 7', child: Text('Grade 7')),
                  DropdownMenuItem(value: 'Grade 8', child: Text('Grade 8')),
                  DropdownMenuItem(value: 'Grade 9', child: Text('Grade 9')),
                  DropdownMenuItem(value: 'Grade 10', child: Text('Grade 10')),
                  DropdownMenuItem(value: 'Grade 11', child: Text('Grade 11')),
                  DropdownMenuItem(value: 'Grade 12', child: Text('Grade 12')),
                  DropdownMenuItem(value: 'College', child: Text('College')),
                  DropdownMenuItem(value: 'Professional', child: Text('Professional')),
                ],
                onChanged: (value) {
                  setState(() {
                    _readingGradeLevel = value;
                  });
                },
              ),
            if (_processType == 'scheme_of_work' || _processType == 'worksheet' || _processType == 'workbook')
              DropdownButtonFormField<String>(
                value: _numberOfDays,
                decoration: InputDecoration(
                  labelText: 'Number of Days',
                  labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                  border: const OutlineInputBorder(),
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
                items: const [
                  DropdownMenuItem(value: '1 Day', child: Text('1 Day')),
                  DropdownMenuItem(value: '2 Days', child: Text('2 Days')),
                  DropdownMenuItem(value: '3 Days', child: Text('3 Days')),
                  DropdownMenuItem(value: '5 Days', child: Text('5 Days')),
                  DropdownMenuItem(value: '1 Week', child: Text('1 Week')),
                  DropdownMenuItem(value: '2 Weeks', child: Text('2 Weeks')),
                  DropdownMenuItem(value: '3 Weeks', child: Text('3 Weeks')),
                  DropdownMenuItem(value: '1 Month', child: Text('1 Month')),
                  DropdownMenuItem(value: '2 Months', child: Text('2 Months')),
                  DropdownMenuItem(value: '3 Months', child: Text('3 Months')),
                  DropdownMenuItem(value: '6 Months', child: Text('6 Months')),
                  DropdownMenuItem(value: '1 Year', child: Text('1 Year')),
                ],
                onChanged: (value) {
                  setState(() {
                    _numberOfDays = value;
                  });
                },
              ),
            if (_processType == 'quiz' || _processType == 'exam' || _processType == 'exam_free' || _processType == 'exam_case')
              DropdownButtonFormField<String>(
                value: _difficultyLevel,
                decoration: InputDecoration(
                  labelText: 'Difficulty Level',
                  labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                  border: const OutlineInputBorder(),
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
                items: const [
                  DropdownMenuItem(value: 'Easy', child: Text('Easy')),
                  DropdownMenuItem(value: 'Normal', child: Text('Normal')),
                  DropdownMenuItem(
                      value: 'Intermediate', child: Text('Intermediate')),
                  DropdownMenuItem(value: 'Hard', child: Text('Hard')),
                  DropdownMenuItem(
                      value: 'Very Hard', child: Text('Very Hard')),
                ],
                onChanged: (value) {
                  setState(() {
                    _difficultyLevel = value;
                  });
                },
              ),
            if (_processType == 'quiz')
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: 'Quiz Time Limit (minutes)',
                          labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                        ),
                        onChanged: (value) => setState(() => _quizTimeLimitMinutes = int.tryParse(value)),
                      ),
                    ),
                  ],
                ),
              ),

            if (_processType == 'skills_gap_analysis')
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Column(
                  children: [
                    TextFormField(
                      controller: _careerController,
                      decoration: InputDecoration(
                        labelText: 'Desired Career/Job Title',
                        labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                        border: const OutlineInputBorder(),
                      ),
                      style: GoogleFonts.notoSans(color: generalTextColor),
                      onChanged: (value) => setState(() => _desiredCareer = value),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _seniorityLevel,
                      decoration: InputDecoration(
                        labelText: 'Seniority Level',
                        labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                        border: const OutlineInputBorder(),
                      ),
                      style: GoogleFonts.notoSans(color: generalTextColor),
                      items: const [
                        DropdownMenuItem(value: 'Junior/Entry Level', child: Text('Junior/Entry Level')),
                        DropdownMenuItem(value: 'Mid-level', child: Text('Mid-level')),
                        DropdownMenuItem(value: 'Senior', child: Text('Senior')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _seniorityLevel = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
              
            SizedBox(height: 16), // Added consistent spacing here
            ElevatedButton.icon(
              icon: _isProcessing
                  ? const SizedBox()
                  : Icon(Icons.memory, color: buttonTextColor),
              label: Text(_isProcessing ? 'Processing...' : 'Process with AI',
                  style: GoogleFonts.notoSans(color: buttonTextColor)),
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBg,
                padding: const EdgeInsets.symmetric(vertical: 16),
                minimumSize: const Size(double.infinity, 50),
              ),
				onPressed: _isProcessing ? null : _processInput,
            ),
            if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(value: _processingProgress),
              ),
            if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'Processing: ${(_processingProgress * 100).toStringAsFixed(0)}%',
                  style: GoogleFonts.notoSans(color: generalTextColor),
                ),
              ),
          ],
        ),
      ),
    );
  }

// Renders a styled table from Markdown text
Widget _buildTableFromMarkdown(String markdown, ThemeData theme, Color generalTextColor) {
    final lines = markdown.split('\n').where((line) {
        final trimmedLine = line.trim();
        // Filter out empty lines and markdown table separator lines
        return trimmedLine.isNotEmpty && 
               !trimmedLine.startsWith('|--') && 
               !trimmedLine.startsWith(':--');
    }).toList();
    
    if (lines.isEmpty) return const SizedBox.shrink();

    final List<TableRow> rows = [];
    int? columnCount;

    // Process the header row
    if (lines.isNotEmpty) {
        final headerCellsContent = lines[0]
            .split('|')
            .map((cell) => cell.trim())
            .where((cell) => cell.isNotEmpty)
            .toList();
        
        columnCount = headerCellsContent.length;

        final headerCells = headerCellsContent.map((cell) {
            return TableCell(
                verticalAlignment: TableCellVerticalAlignment.middle,
                child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                        cell,
                        style: GoogleFonts.notoSans(
                            fontWeight: FontWeight.bold,
                            color: generalTextColor,
                            fontSize: _lessonFontSize > 0 ? _lessonFontSize : 16.0
                        ),
                        textAlign: TextAlign.center,
                    ),
                ),
            );
        }).toList();

        rows.add(TableRow(
            decoration: BoxDecoration(
                color: widget.isDarkMode ? Colors.grey[800] : Colors.grey[200],
            ),
            children: headerCells,
        ));
    }

    // Process the data rows
    if (lines.length > 1) {
        for (int i = 1; i < lines.length; i++) {
            final dataCellsContent = lines[i]
                .split('|')
                .map((cell) => cell.trim())
                .where((cell) => cell.isNotEmpty)
                .toList();

            // Pad or truncate the row to match the header's column count for consistency
            while (dataCellsContent.length < (columnCount ?? 0)) {
                dataCellsContent.add(''); 
            }
            if (dataCellsContent.length > (columnCount ?? 0)) {
                dataCellsContent.removeRange(columnCount!, dataCellsContent.length);
            }

            final dataCells = dataCellsContent.map((cell) {
                return TableCell(
                    verticalAlignment: TableCellVerticalAlignment.middle,
                    child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        // Use the LaTeX renderer for cell content to support complex text
                        child: _DashboardPageState.buildLatexContent(
                            cell,
                            widget.isDarkMode,
                            _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                        )
                    ),
                );
            }).toList();

            if (dataCells.isNotEmpty) {
                rows.add(TableRow(children: dataCells));
            }
        }
    }
    
    if (rows.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Table(
          border: TableBorder.all(
              color: widget.isDarkMode ? Colors.grey[700]! : Colors.grey[400]!,
              width: 1,
          ),
          defaultColumnWidth: const IntrinsicColumnWidth(),
          children: rows,
        ),
      ),
    );
}

// Parses content and builds a list of widgets, handling both text and tables
Widget _buildContentWithTables(String? content, ThemeData theme, Color generalTextColor) {
    if (content == null || content.isEmpty) {
        return Text('No content generated', style: GoogleFonts.notoSans(color: generalTextColor));
    }

    // This regex finds the table blocks wrapped with the custom [TABLE_START] and [TABLE_END] tags
    final tableRegex = RegExp(r'\[TABLE_START\]([\s\S]*?)\[TABLE_END\]', dotAll: true);
    
    final List<Widget> widgets = [];
    int lastEnd = 0;

    for (final match in tableRegex.allMatches(content)) {
        // Add the text segment before the table
        if (match.start > lastEnd) {
            final textSegment = content.substring(lastEnd, match.start);
            widgets.add(
                _DashboardPageState.buildLatexContent(
                    textSegment, 
                    widget.isDarkMode, 
                    _lessonFontSize > 0 ? _lessonFontSize : 16.0
                )
            );
        }

        // Extract the markdown table content from the match
        final tableMarkdown = match.group(1)?.trim();
        if (tableMarkdown != null && tableMarkdown.isNotEmpty) {
            // Build and add the table widget
            widgets.add(_buildTableFromMarkdown(tableMarkdown, theme, generalTextColor));
        }

        lastEnd = match.end;
    }

    // Add any remaining text after the last table
    if (lastEnd < content.length) {
        final remainingText = content.substring(lastEnd);
        widgets.add(
            _DashboardPageState.buildLatexContent(
                remainingText, 
                widget.isDarkMode, 
                _lessonFontSize > 0 ? _lessonFontSize : 16.0
            )
        );
    }

    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widgets,
    );
}

/// Renders content with a prominent title extracted from the first line.
Widget _buildTitledContent(ThemeData theme, Color generalTextColor) {
  if (_geminiOutput == null || _geminiOutput!.trim().isEmpty) {
    return Text('No content generated', style: GoogleFonts.notoSans(color: generalTextColor));
  }

  // Split content to find the first non-empty line for the title
  final lines = _geminiOutput!.split('\n');
  int firstLineIndex = lines.indexWhere((line) => line.trim().isNotEmpty);

  // If no content or only whitespace, fallback to the standard renderer
  if (firstLineIndex == -1) {
    return _buildContentWithTables(_geminiOutput, theme, generalTextColor);
  }

  final title = lines[firstLineIndex].trim();
  final restOfContent = lines.sublist(firstLineIndex + 1).join('\n');

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // Render the main topic title
      Padding(
        padding: const EdgeInsets.only(bottom: 12.0),
        child: Text(
          title.replaceAll('**', ''), // Clean up bold markdown
          style: GoogleFonts.notoSans(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: generalTextColor,
          ),
          textAlign: TextAlign.start,
        ),
      ),
      // Render the rest of the content using the standard table-aware builder
      _buildContentWithTables(restOfContent, theme, generalTextColor),
    ],
  );
}

/// Special renderer for Workbooks that replaces underscore lines with a 1px container.
Widget _buildWorkbookContent(ThemeData theme, Color generalTextColor) {
  if (_geminiOutput == null || _geminiOutput!.trim().isEmpty) {
    return Text('No content generated', style: GoogleFonts.notoSans(color: generalTextColor));
  }
  
  // Logic to extract title and body
  final lines = _geminiOutput!.split('\n');
  int firstLineIndex = lines.indexWhere((line) => line.trim().isNotEmpty);

  if (firstLineIndex == -1) {
    return const SizedBox.shrink(); // No valid content
  }

  final title = lines[firstLineIndex].trim().replaceAll('**', '');
  final restOfContent = lines.sublist(firstLineIndex + 1).join('\n');
  
  // Logic to build widgets with 1px lines for answers
  final parts = restOfContent.split(RegExp(r'_{5,}')); // Split by 5+ underscores
  List<Widget> widgets = [];
  
  for (int i = 0; i < parts.length; i++) {
    if (parts[i].trim().isNotEmpty) {
      widgets.add(
        _DashboardPageState.buildLatexContent(
          parts[i],
          widget.isDarkMode,
          _lessonFontSize > 0 ? _lessonFontSize : 16.0,
        ),
      );
    }
    
    // If this is not the last part, it means there was a line here.
    // Add our visual 1px line.
    if (i < parts.length - 1) {
      widgets.add(
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Container(
            height: 1,
            color: generalTextColor,
          ),
        ),
      );
    }
  }
  
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // Render the main topic title
      Padding(
        padding: const EdgeInsets.only(bottom: 12.0),
        child: Text(
          title,
          style: GoogleFonts.notoSans(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: generalTextColor,
          ),
          textAlign: TextAlign.start,
        ),
      ),
      // Render the workbook content with answer lines
      ...widgets,
    ],
  );
}


List<Map<String, String>> parseQuizFillInTheBlank(String content) {
  final List<Map<String, String>> quizItems = [];
  final blocks = content.split('\n\n'); // Split by blank lines between questions
  final questionPattern = RegExp(r'^(\d+\.\s*.*?)\nAnswer:\s*(.*)', multiLine: true);

  for (final block in blocks) {
    final match = questionPattern.firstMatch(block.trim());
    if (match != null) {
      final question = match.group(1)?.trim() ?? '';
      final answer = match.group(2)?.trim() ?? '';
      if (question.isNotEmpty && answer.isNotEmpty) {
        quizItems.add({'question': question, 'answer': answer});
      }
    }
  }
  return quizItems;
}

List<Map<String, String>> parseNotesQA(String content) {
  final List<Map<String, String>> notesItems = [];
  final lines = content.split('\n');
  for (int i = 0; i < lines.length - 1; i++) {
    final questionLine = lines[i].trim();
    final answerLine = lines[i + 1].trim();
    if (questionLine.startsWith('**Question:**') && answerLine.startsWith('**Answer:**')) {
      final question = questionLine.substring('**Question:**'.length).trim();
      final answer = answerLine.substring('**Answer:**'.length).trim();
      if (question.isNotEmpty && answer.isNotEmpty) {
        notesItems.add({'question': question, 'answer': answer});
      }
      i++; // Skip the answer line since we’ve processed it
    }
  }
  return notesItems;
}

// Replace the existing _buildContentDisplay method with this updated version:

Widget _buildContentDisplay(ThemeData theme, Color generalTextColor) {
  String titleText = 'Generated Content';
  bool showDownloadButton = true;
  bool showSaveButton = false;

  // Define titled process types and other configurations
  final titledProcessTypes = [
    'notes', 'notes_qa', 'cheatsheet', 'summary', 'minutes', 'lesson_plan',
    'worksheet', 'homework_guide', 'project_ideas', 'case_studies',
    'experiment', 'scheme_of_work', 'transcript', 'grammar',
    'paper_grader', 'data_analysis', 'resume_feedback', 'cover_letter_feedback',
    'action_item_extractor', 'quiz_fill_in_the_blank', 'discussion_prompts',
    'skills_gap_analysis'
  ];

  // Set titleText, showDownloadButton, and showSaveButton based on _processType
  switch (_processType) {
    case 'notes':
      titleText = 'Notes';
      showDownloadButton = true;
      showSaveButton = false;
      break;
    case 'notes_qa':
      titleText = 'Notes (Q&A)';
      showDownloadButton = true;
      showSaveButton = false;
      break;
    case 'quiz_fill_in_the_blank':
      titleText = 'Quiz (Fill-in)';
      showDownloadButton = true;
      break;
    default:
      final formattedType = _processType.replaceAll('_', ' ');
      titleText = formattedType[0].toUpperCase() + formattedType.substring(1);
      showDownloadButton = true;
  }

  if (_processType == 'flashcards') {
    return _buildFlashcardsView(theme, generalTextColor);
  } else if (_processType == 'quiz') {
    return _buildQuizView(theme, generalTextColor);
  } else if (_processType == 'exam' || _processType == 'exam_free' || _processType == 'exam_case') {
    return _buildExamView(theme, generalTextColor);
  } else if (_processType == 'chat' || _processType == 'interview_practice') {
    return _buildChatView(theme, generalTextColor);
  } else if (_processType == 'interactive_lesson') {
    return _buildInteractiveLessonView(theme, generalTextColor);
  }

  // Handle quiz_fill_in_the_blank with custom parsing and uniform UI
  if (_processType == 'quiz_fill_in_the_blank') {
    final quizItems = parseQuizFillInTheBlank(_geminiOutput ?? '');
    if (quizItems.isNotEmpty) {
      return Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(titleText,
                        style: GoogleFonts.notoSans(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: generalTextColor)),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.text_decrease, color: generalTextColor),
                        onPressed: () {
                          setState(() {
                            if (_lessonFontSize > 12) _lessonFontSize -= 2;
                          });
                        },
                        tooltip: 'Decrease font size',
                      ),
                      IconButton(
                        icon: Icon(Icons.text_increase, color: generalTextColor),
                        onPressed: () {
                          setState(() {
                            if (_lessonFontSize < 24) _lessonFontSize += 2;
                          });
                        },
                        tooltip: 'Increase font size',
                      ),
                      if (showDownloadButton)
                        IconButton(
                          icon: Icon(Icons.download, color: generalTextColor),
                          onPressed: () => _downloadContent(),
                          tooltip: 'Download Content',
                        ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: quizItems.map((item) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MarkdownBody(
                      data: item['question']!,
                      styleSheet: MarkdownStyleSheet(
                        p: GoogleFonts.notoSans(
                          fontSize: _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                          color: generalTextColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    MarkdownBody(
                      data: 'Answer: ${item['answer']!}',
                      styleSheet: MarkdownStyleSheet(
                        p: GoogleFonts.notoSans(
                          fontSize: _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                          color: generalTextColor,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                )).toList(),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (_isResponseComplete == false)
                    IconButton(
                      icon: Icon(Icons.play_arrow,
                          color: widget.isDarkMode ? Colors.white : Colors.black),
                      tooltip: 'Continue generating',
                      onPressed: _isProcessing ? null : () => _continueGenerating(),
                    )
                  else if (_geminiOutput != null && _geminiOutput!.isNotEmpty)
                    IconButton(
                      icon: const Icon(Icons.check_circle, color: Colors.green),
                      tooltip: 'Response Complete',
                      onPressed: null,
                    ),
                ],
              ),
            ],
          ),
        ),
      );
    }
  } 
  // Handle notes_qa with custom parsing and uniform UI
  else if (_processType == 'notes_qa') {
    final notesItems = parseNotesQA(_geminiOutput ?? '');
    if (notesItems.isNotEmpty) {
      return Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(titleText,
                        style: GoogleFonts.notoSans(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: generalTextColor)),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.text_decrease, color: generalTextColor),
                        onPressed: () {
                          setState(() {
                            if (_lessonFontSize > 12) _lessonFontSize -= 2;
                          });
                        },
                        tooltip: 'Decrease font size',
                      ),
                      IconButton(
                        icon: Icon(Icons.text_increase, color: generalTextColor),
                        onPressed: () {
                          setState(() {
                            if (_lessonFontSize < 24) _lessonFontSize += 2;
                          });
                        },
                        tooltip: 'Increase font size',
                      ),
                      if (showDownloadButton)
                        IconButton(
                          icon: Icon(Icons.download, color: generalTextColor),
                          onPressed: () => _downloadContent(),
                          tooltip: 'Download Content',
                        ),
                      if (showSaveButton)
                        IconButton(
                          icon: Icon(Icons.save, color: generalTextColor),
                          onPressed: () => _saveContent(),
                          tooltip: 'Save Content',
                        ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: notesItems.map((item) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MarkdownBody(
                      data: '**Question:** ${item['question']!}',
                      styleSheet: MarkdownStyleSheet(
                        p: GoogleFonts.notoSans(
                          fontSize: _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                          color: generalTextColor,
                        ),
                        strong: GoogleFonts.notoSans(
                          fontWeight: FontWeight.bold,
                          color: generalTextColor,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    MarkdownBody(
                      data: '**Answer:** ${item['answer']!}',
                      styleSheet: MarkdownStyleSheet(
                        p: GoogleFonts.notoSans(
                          fontSize: _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                          color: generalTextColor,
                        ),
                        strong: GoogleFonts.notoSans(
                          fontWeight: FontWeight.bold,
                          color: generalTextColor,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                )).toList(),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (_isResponseComplete == false)
                    IconButton(
                      icon: Icon(Icons.play_arrow,
                          color: widget.isDarkMode ? Colors.white : Colors.black),
                      tooltip: 'Continue generating',
                      onPressed: _isProcessing ? null : () => _continueGenerating(),
                    )
                  else if (_geminiOutput != null && _geminiOutput!.isNotEmpty)
                    IconButton(
                      icon: const Icon(Icons.check_circle, color: Colors.green),
                      tooltip: 'Response Complete',
                      onPressed: null,
                    ),
                ],
              ),
            ],
          ),
        ),
      );
    }
  }

  // Default rendering for other types or if custom parsing fails
  return Card(
    color: theme.colorScheme.surface,
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(titleText,
                    style: GoogleFonts.notoSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: generalTextColor)),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(Icons.text_decrease, color: generalTextColor),
                    onPressed: () {
                      setState(() {
                        if (_lessonFontSize > 12) _lessonFontSize -= 2;
                      });
                    },
                    tooltip: 'Decrease font size',
                  ),
                  IconButton(
                    icon: Icon(Icons.text_increase, color: generalTextColor),
                    onPressed: () {
                      setState(() {
                        if (_lessonFontSize < 24) _lessonFontSize += 2;
                      });
                    },
                    tooltip: 'Increase font size',
                  ),
                  if (showDownloadButton)
                    IconButton(
                      icon: Icon(Icons.download, color: generalTextColor),
                      onPressed: () => _downloadContent(),
                      tooltip: 'Download Content',
                    ),
                  if (showSaveButton)
                    IconButton(
                      icon: Icon(Icons.save, color: generalTextColor),
                      onPressed: () => _saveContent(),
                      tooltip: 'Save Content',
                    ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: widget.isDarkMode ? theme.cardColor : Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: widget.isDarkMode ? null : Border.all(color: Colors.grey.shade200),
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                SingleChildScrollView(
                  child: _processType == 'workbook'
                      ? _buildWorkbookContent(theme, generalTextColor)
                      : titledProcessTypes.contains(_processType)
                          ? _buildTitledContent(theme, generalTextColor)
                          : _buildContentWithTables(_geminiOutput, theme, generalTextColor),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (_processType == 'summary')
                      IconButton(
                        icon: Icon(Icons.copy, color: generalTextColor),
                        tooltip: 'Copy to Clipboard',
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: _geminiOutput ?? ''));
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Content copied to clipboard')),
                          );
                        },
                      )
                    else
                      const SizedBox.shrink(),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (_isResponseComplete == false)
                          IconButton(
                            icon: Icon(Icons.play_arrow,
                                color: widget.isDarkMode ? Colors.white : Colors.black),
                            tooltip: 'Continue generating',
                            onPressed: _isProcessing ? null : () => _continueGenerating(),
                          )
                        else if (_geminiOutput != null && _geminiOutput!.isNotEmpty)
                          IconButton(
                            icon: const Icon(Icons.check_circle, color: Colors.green),
                            tooltip: 'Response Complete',
                            onPressed: null,
                          ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}






  Widget _buildInteractiveLessonView(ThemeData theme, Color generalTextColor) {
    TextStyle defaultStyle = GoogleFonts.notoSans(
      fontSize: _lessonFontSize,
      color: Colors.white,
      height: 1.5,
    );

    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Page ${_currentLessonStepIndex + 1}/${_lessonSteps.length}',
                  style: GoogleFonts.notoSans(
                    color: generalTextColor,
                    fontSize: 16,
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Font size controls
                    IconButton(
                      icon: Icon(Icons.text_decrease, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize > 12) _lessonFontSize -= 2;
                        });
                      },
                      tooltip: 'Decrease font size',
                    ),
                    IconButton(
                      icon: Icon(Icons.text_increase, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize < 24) _lessonFontSize += 2;
                        });
                      },
                      tooltip: 'Increase font size',
                    ),
                    IconButton(
                      icon: Icon(
                        _isNarrationMuted ? Icons.volume_off : Icons.volume_up,
                        color: generalTextColor,
                      ),
                      onPressed: () {
                        setState(() {
                          _isNarrationMuted = !_isNarrationMuted;
                          if (_isNarrationMuted) _pauseTts();
                        });
                      },
                      tooltip: 'Toggle narration',
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(
              height: 300,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black, // Always pure black for better contrast in lessons
                  borderRadius: BorderRadius.circular(10),
                  // Add a subtle border
                  border: Border.all(color: Colors.grey.shade800, width: 1),
                ),
                padding: const EdgeInsets.all(20),
                child: SingleChildScrollView(
                  child: _DashboardPageState.buildLatexContent(
                    _displayText,
                    true, // Always dark mode for interactive lesson
                    _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 80,
              child: _buildLessonControls(theme),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLessonControls(ThemeData theme) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Previous button
            IconButton(
              icon: Icon(Icons.skip_previous, color: generalTextColor),
              onPressed: _goToPreviousStep,
              tooltip: 'Previous step',
            ),
            // Play/Pause button
            IconButton(
              icon: Icon(
                  _lessonPlaying ? Icons.pause : Icons.play_arrow,
                  color: generalTextColor),
              onPressed: _toggleLessonPlay,
              tooltip: _lessonPlaying ? 'Pause' : 'Play',
            ),
            // Next button
            IconButton(
              icon: Icon(Icons.skip_next, color: generalTextColor),
              onPressed: _goToNextStep,
              tooltip: 'Next step',
            ),
          ],
        ),
      ],
    );
  }

  void _goToPreviousStep() {
    if (_lessonSteps.isEmpty) return;

    _stopTts();
    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex > 0) {
        _currentLessonStepIndex--;
      }
      _lessonPlaying = true; // Autoplay on step change
      _displayText = '';
      _currentCharIndex = 0;
      _startLessonStepDisplay();
    });
  }

  void _goToNextStep() {
    if (_lessonSteps.isEmpty) return;

    _stopTts();
    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex < _lessonSteps.length - 1) {
        _currentLessonStepIndex++;
      }
      _lessonPlaying = true; // Autoplay on step change
      _displayText = '';
      _currentCharIndex = 0;
      _startLessonStepDisplay();
    });
  }

  void _stopLessonStepDisplay() {
    _stopTts();
    _stopTextAnimation();
  }

  void _stopTextAnimation() {
    _textAnimationTimer?.cancel();
    _isTextAnimationActive = false;
    _displayText = '';
    _currentCharIndex = 0;
  }

  void _goToNextStepForTts() {
    if (_lessonSteps.isEmpty) return;

    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex < _lessonSteps.length - 1) {
        _currentLessonStepIndex++;
        _displayText = '';
        _currentCharIndex = 0;
        _startLessonStepDisplay();
      } else {
        _lessonPlaying = false;
      }
    });
  }

  void _restartLesson() {
    setState(() {
      _currentLessonStepIndex = 0;
      _stopLessonStepDisplay();
      _startLessonStepDisplay();
    });
  }

  void _toggleLessonPlay() {
    setState(() {
      _lessonPlaying = !_lessonPlaying;
      if (_lessonPlaying) {
        _startLessonStepDisplay();
      } else {
        _pauseLessonStepDisplay();
      }
    });
  }

  void _pauseLessonStepDisplay() {
    _textAnimationTimer?.cancel();
    _pause();
    setState(() {
      _isTextAnimationActive = false;
    });
  }

  // Data analysis view removed but Excel and CSV support maintained

  void _startLessonStepDisplay() {
    _textAnimationTimer?.cancel();
    _currentCharIndex = 0;
    _displayText = '';
    _isTextAnimationActive = true;

    final fullText = _lessonSteps[_currentLessonStepIndex]
        .replaceAll(RegExp(r'\[.*?\]'), '')
        .trim();

    if (_lessonPlaying) {
      _speak(_lessonSteps[_currentLessonStepIndex]);

      // Use _lessonSpeed to control animation speed (faster speed = shorter duration)
      _textAnimationTimer = Timer.periodic(Duration(milliseconds: (30 / _lessonSpeed).round()), (timer) {
        if (_currentCharIndex < fullText.length) {
          setState(() {
            _displayText += fullText[_currentCharIndex];
            _currentCharIndex++;
          });
        } else {
          timer.cancel();
          _isTextAnimationActive = false;

          // Auto-advance to next step after a delay if not the last step
          if (_currentLessonStepIndex < _lessonSteps.length - 1) {
            // Use _lessonSpeed to control auto-advance delay (faster speed = shorter delay)
            Future.delayed(Duration(milliseconds: (3000 / _lessonSpeed).round()), () {
              if (mounted && _lessonPlaying) {
                _goToNextStep();
              }
            });
          }
        }
      });
    }
  }

  void _startTtsForCurrentStep() {
    if (_lessonSteps.isNotEmpty &&
        _currentLessonStepIndex < _lessonSteps.length &&
        _lessonPlaying) {
      _speak(_lessonSteps[_currentLessonStepIndex]);
    }
  }

  void _stopTts() {
    flutterTts.stop();
    setState(() => _lessonPlaying = false);
  }

  void _pauseTts() {
    flutterTts.pause();
    setState(() => _lessonPlaying = false);
  }

  Widget _buildFlashcardsView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 400,
              child: PageView.builder(
                itemCount: _flashcards.length,
                itemBuilder: (context, index) => FlashcardWidget(
                  flashcard: _flashcards[index],
                  textColor: generalTextColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatView(ThemeData theme, Color generalTextColor) {
    final isDark = widget.isDarkMode;
    final iconColor = isDark ? Colors.white70 : Colors.black87;

    return Card(
      color: theme.colorScheme.surface,
      elevation: 2,
      clipBehavior: Clip.antiAlias,
      child: SizedBox(
        height: 500, // Give the chat view a fixed height to contain it
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                controller: _chatScrollController,
                padding: const EdgeInsets.symmetric(vertical: 10.0),
                itemCount: _chatMessages.length,
                itemBuilder: (context, index) {
                  return NewChatBubble(
                    message: _chatMessages[index],
                    isDarkMode: isDark,
                    theme: theme,
                  );
                },
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
              decoration: BoxDecoration(
                color: theme.scaffoldBackgroundColor,
                border: Border(top: BorderSide(color: theme.dividerColor, width: 0.5)),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _chatController,
                      decoration: InputDecoration(
                        hintText: 'Ask a question...',
                        hintStyle: TextStyle(color: theme.hintColor.withOpacity(0.6)),
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 12.0),
                      ),
                      style: GoogleFonts.notoSans(color: generalTextColor),
                      onSubmitted: (_) => _sendChatMessage(),
                      textCapitalization: TextCapitalization.sentences,
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.send, color: _chatController.text.trim().isEmpty ? theme.disabledColor : iconColor),
                    tooltip: "Send",
                    onPressed: _chatController.text.trim().isEmpty ? null : _sendChatMessage,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool get _hasMP3 => _pickedFiles.any((f) => f.file.name.toLowerCase().endsWith('.mp3'));
}

class FileWithPageRange {
  PlatformFile file;
  int? startPage;
  int? endPage;

  FileWithPageRange({required this.file, this.startPage, this.endPage});
}

class Flashcard {
  final String question;
  final String answer;

  Flashcard({required this.question, required this.answer});
}

class QuizQuestion {
  final String question;
  final List<String> options;
  final int? correctAnswerIndex;

  QuizQuestion({
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
  });
}

class ExamQuestion {
  final String question;
  final List<String> options;
  final String correctAnswer;

  ExamQuestion({
    required this.question,
    required this.options,
    required this.correctAnswer,
  });
}

// Save Content Dialog Widget
class _SaveContentDialog extends StatefulWidget {
  final String defaultTitle;
  final bool isDarkMode;
  final Color generalTextColor;

  const _SaveContentDialog({
    required this.defaultTitle,
    required this.isDarkMode,
    required this.generalTextColor,
  });

  @override
  State<_SaveContentDialog> createState() => _SaveContentDialogState();
}

class _SaveContentDialogState extends State<_SaveContentDialog> {
  late TextEditingController titleController;
  String? selectedCourseId;
  List<Map<String, dynamic>> courses = [];

  @override
  void initState() {
    super.initState();
    titleController = TextEditingController(text: widget.defaultTitle);
    _loadCourses();
  }

  @override
  void dispose() {
    titleController.dispose();
    super.dispose();
  }

  Future<void> _loadCourses() async {
    try {
      final localStorageService = LocalStorageService();
      final coursesList = await localStorageService.getCourses();
      setState(() {
        courses = coursesList.map((course) => course.toJson()).toList();
      });
    } catch (e) {
      // If courses can't be loaded, start with empty list
      setState(() {
        courses = [];
      });
    }
  }

  Future<void> _createNewCourse() async {
    final TextEditingController courseController = TextEditingController();

    final courseName = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Create New Course',
          style: TextStyle(color: widget.generalTextColor)),
        content: TextField(
          controller: courseController,
          decoration: InputDecoration(
            hintText: 'Enter course name',
            hintStyle: TextStyle(color: widget.generalTextColor.withOpacity(0.6)),
          ),
          style: TextStyle(color: widget.generalTextColor),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel',
              style: TextStyle(color: widget.generalTextColor)),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, courseController.text),
            child: Text('Create',
              style: TextStyle(color: widget.generalTextColor)),
          ),
        ],
      ),
    );

    if (courseName != null && courseName.isNotEmpty) {
      try {
        final localStorageService = LocalStorageService();
        final newCourse = await localStorageService.createCourse(courseName);

        setState(() {
          courses.add(newCourse.toJson());
          selectedCourseId = newCourse.id;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Course "$courseName" created')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error creating course: $e', overflow: TextOverflow.ellipsis)),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Save Content',
        style: TextStyle(color: widget.generalTextColor)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: titleController,
            decoration: InputDecoration(
              labelText: 'Title',
              labelStyle: TextStyle(color: widget.generalTextColor),
            ),
            style: TextStyle(color: widget.generalTextColor),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: selectedCourseId,
                  decoration: InputDecoration(
                    labelText: 'Course (Optional)',
                    labelStyle: TextStyle(color: widget.generalTextColor),
                  ),
                  dropdownColor: widget.isDarkMode ? Colors.grey[800] : Colors.white,
                  style: TextStyle(color: widget.generalTextColor),
                  items: [
                    DropdownMenuItem<String>(
                      value: null,
                      child: Text('No Course',
                        style: TextStyle(color: widget.generalTextColor)),
                    ),
                    ...courses.map((course) => DropdownMenuItem<String>(
                      value: course['id'],
                      child: Text(course['name'],
                        style: TextStyle(color: widget.generalTextColor)),
                    )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedCourseId = value;
                    });
                  },
                ),
              ),
              IconButton(
                icon: Icon(Icons.add, color: widget.generalTextColor),
                onPressed: _createNewCourse,
                tooltip: 'Create New Course',
              ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Cancel', style: TextStyle(color: widget.generalTextColor)),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context, {
            'title': titleController.text,
            'courseId': selectedCourseId,
          }),
          child: Text('Save', style: TextStyle(color: widget.generalTextColor)),
        ),
      ],
    );
  }
}

class QuizResult {
  final String question;
  final String userAnswer;
  final String correctAnswer;
  final bool isCorrect;

  QuizResult({
    required this.question,
    required this.userAnswer,
    required this.correctAnswer,
    required this.isCorrect,
  });
}

class ChatMessage {
  final String text;
  final bool isUser;
  final bool isTyping;

  ChatMessage(this.text, this.isUser, {this.isTyping = false});
}

class FlashcardWidget extends StatelessWidget {
  final Flashcard flashcard;
  final Color textColor;

  const FlashcardWidget({
    Key? key,
    required this.flashcard,
    required this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return FlipCard(
      front: Card(
        color: isDarkMode ? theme.cardColor : Colors.white, // Use white in light mode for better LaTeX rendering
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Center(
              child: _DashboardPageState.buildLatexContent(
                flashcard.question,
                isDarkMode,
                20.0,
              ),
            ),
          ),
        ),
      ),
      back: Card(
        color: isDarkMode ? theme.colorScheme.surface : Colors.white, // Use white in light mode for better LaTeX rendering
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Center(
              child: _DashboardPageState.buildLatexContent(
                flashcard.answer.replaceAll(RegExp(r'^::\w*\s*|^:\s*'), ''), // Clean answer text and remove leading colon
                isDarkMode,
                16.0,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class NewChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;
  final ThemeData theme;
  const NewChatBubble({
    Key? key,
    required this.message,
    required this.isDarkMode,
    required this.theme,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (message.isTyping) {
      return Align(
        alignment: Alignment.centerLeft,
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
          decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant.withOpacity(0.8),
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4.0),
                  topRight: Radius.circular(18.0),
                  bottomLeft: Radius.circular(18.0),
                  bottomRight: Radius.circular(18.0))),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(message.text,
                  style: TextStyle(
                      color: theme.colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                      fontSize: 14)),
            ],
          ),
        ),
      );
    }
    final Color userBubbleColor = isDarkMode ? Colors.grey[800]! : Colors.grey[300]!;
    final Color aiBubbleColor = theme.colorScheme.surfaceVariant;
    final Color userTextColor = theme.colorScheme.onSurface;
    final Color aiTextColor = theme.colorScheme.onSurfaceVariant;
    final bubbleColor = message.isUser ? userBubbleColor : aiBubbleColor;
    final borderRadius = BorderRadius.only(
        topLeft: Radius.circular(message.isUser ? 18.0 : 4.0),
        topRight: Radius.circular(message.isUser ? 4.0 : 18.0),
        bottomLeft: const Radius.circular(18.0),
        bottomRight: const Radius.circular(18.0));

    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 5.0, horizontal: 8.0),
        padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 10.0),
        constraints:
            BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.8),
        decoration: BoxDecoration(
            color: bubbleColor,
            borderRadius: borderRadius,
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withOpacity(0.06),
                  blurRadius: 3,
                  offset: const Offset(1, 2))
            ]),
        child: _DashboardPageState.buildLatexContent(message.text, isDarkMode, 15.0),
      ),
    );
  }
}

class ExamResultsDialog extends StatelessWidget {
  final VoidCallback onGenerateNotes;
  final List<QuizQuestion> incorrectQuestions;
  final List<QuizQuestion> questions;
  final Color textColor;
  final Map<int, int?> userAnswers;
  final String? weaknessNotes;

  const ExamResultsDialog({
    Key? key,
    required this.questions,
    required this.textColor,
    required this.incorrectQuestions,
    required this.onGenerateNotes,
    required this.userAnswers,
    this.weaknessNotes,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    int correctCount = 0;
    for (int i = 0; i < questions.length; i++) {
      if (userAnswers[i] == questions[i].correctAnswerIndex) {
        correctCount++;
      }
    }
    double totalQuestions = questions.length.toDouble();

    return AlertDialog(
      title: Text('Practice Exam Answer Key', style: GoogleFonts.notoSans(color: textColor)),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Score: ${(correctCount / totalQuestions * 100).toStringAsFixed(1)}%',
                style: TextStyle(color: _getScoreColor(correctCount / totalQuestions), fontSize: 24)),
            const SizedBox(height: 20),
            Text('Answer Key:',
                style: GoogleFonts.notoSans(
                    fontSize: 20,
                    fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),
            ...questions.asMap().entries.map((entry) {
              int index = entry.key;
              QuizQuestion question = entry.value;
              String correctAnswer = question.correctAnswerIndex != null && question.correctAnswerIndex! < question.options.length
                  ? question.options[question.correctAnswerIndex!]
                  : 'Unknown';
              String answerLetter = question.correctAnswerIndex != null ? String.fromCharCode('A'.codeUnitAt(0) + question.correctAnswerIndex!) : '?';
              bool isCorrect = userAnswers[index] == question.correctAnswerIndex;
              String userAnswerText = userAnswers[index] != null && userAnswers[index]! < question.options.length
                  ? question.options[userAnswers[index]!] : 'Not Answered';
              String userAnswerLetter = userAnswers[index] != null ? String.fromCharCode('A'.codeUnitAt(0) + userAnswers[index]!) : ' ';


              return ListTile(
                title: Text('${index + 1}. ${question.question}',
                    style: GoogleFonts.notoSans(fontWeight: FontWeight.bold)),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Correct answer: $answerLetter) $correctAnswer',
                        style: GoogleFonts.notoSans(color: Colors.green)),
                    Text('Your answer: $userAnswerLetter) $userAnswerText',
                        style: GoogleFonts.notoSans(color: isCorrect ? Colors.green : Colors.red)),
                    const Divider(),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
      actions: [
        if (weaknessNotes == null)
          TextButton(
            onPressed: onGenerateNotes,
            child: Text('Generate Improvement Notes', style: GoogleFonts.notoSans(color: Colors.blue)),
          )
        else
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextButton(
                onPressed: () => _downloadWeaknessNotes(context),
                child: Text('Download Notes as PDF', style: GoogleFonts.notoSans(color: Colors.blue)),
              ),
            ],
          ),
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Close', style: GoogleFonts.notoSans(color: textColor)),
        ),
      ],
    );
  }

  

  
  
  Future<void> _downloadWeaknessNotes(BuildContext context) async {
    if (weaknessNotes == null) return;

    try {
      // Create PDF document
      final pdf = pw.Document();

      // Add content to PDF
      pdf.addPage(
        pw.MultiPage(
          pageFormat: pdf_package.PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          header: (pw.Context context) {
            return pw.Header(
              level: 0,
              child: pw.Text(
                'Refactr AI - Quiz Improvement Notes',
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            );
          },
          footer: (pw.Context context) {
            return pw.Footer(
              trailing: pw.Text(
                'Page ${context.pageNumber} of ${context.pagesCount}',
                style: const pw.TextStyle(
                  fontSize: 12,
                ),
              ),
            );
          },
          build: (pw.Context context) => [
            pw.Paragraph(
              text: weaknessNotes!,
              style: const pw.TextStyle(
                fontSize: 14,
              ),
            ),
          ],
        ),
      );

      // Get directory for saving file
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'Quiz_Improvement_Notes_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final filePath = path.join(directory.path, fileName);
      final file = File(filePath);

      // Save PDF to file
      await file.writeAsBytes(await pdf.save());

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Notes saved to: $filePath')),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving notes: $e', overflow: TextOverflow.ellipsis)),
        );
      }
    }
  }
}

Color _getScoreColor(double percentage) {
  if (percentage >= 0.9) return Colors.green;
  if (percentage >= 0.7) return Colors.orange;
  return Colors.red;
}
