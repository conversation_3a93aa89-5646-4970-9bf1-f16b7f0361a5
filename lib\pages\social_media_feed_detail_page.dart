import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class SocialMediaFeedDetailPage extends StatefulWidget {
  final Map<String, dynamic> socialMediaFeed;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const SocialMediaFeedDetailPage({
    Key? key,
    required this.socialMediaFeed,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<SocialMediaFeedDetailPage> createState() => _SocialMediaFeedDetailPageState();
}

class _SocialMediaFeedDetailPageState extends State<SocialMediaFeedDetailPage> {
  late RealtimeChannel _socialMediaFeedRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _socialMediaFeedRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _socialMediaFeedRealtimeChannel = Supabase.instance.client
        .channel('social_media_feed_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'socialmediafeeds',
      callback: (payload) async {
        if (payload.newRecord != null && payload.newRecord!['id'] == widget.socialMediaFeed['id']) {
          print("Realtime update received for social media feed detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshSocialMediaFeed();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshSocialMediaFeed() async {
    try {
      final response = await Supabase.instance.client
          .from('socialmediafeeds')
          .select('*')
          .eq('id', widget.socialMediaFeed['id'])
          .single();

      if (mounted) {
        setState(() {
          widget.socialMediaFeed.clear();
          widget.socialMediaFeed.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing social media feed: $e");
    }
  }

  Future<void> _launchURL(String url) async {
    if (url.isEmpty) return;
    
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }
    
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch link')),
      );
    }
  }

  IconData _getPlatformIcon(String platform) {
    switch (platform.toLowerCase()) {
      case 'facebook':
        return FontAwesomeIcons.facebook;
      case 'twitter':
      case 'x':
        return FontAwesomeIcons.twitter;
      case 'instagram':
        return FontAwesomeIcons.instagram;
      case 'linkedin':
        return FontAwesomeIcons.linkedin;
      case 'youtube':
        return FontAwesomeIcons.youtube;
      case 'tiktok':
        return FontAwesomeIcons.tiktok;
      case 'snapchat':
        return FontAwesomeIcons.snapchat;
      case 'pinterest':
        return FontAwesomeIcons.pinterest;
      case 'reddit':
        return FontAwesomeIcons.reddit;
      case 'whatsapp':
        return FontAwesomeIcons.whatsapp;
      case 'telegram':
        return FontAwesomeIcons.telegram;
      case 'discord':
        return FontAwesomeIcons.discord;
      case 'github':
        return FontAwesomeIcons.github;
      case 'medium':
        return FontAwesomeIcons.medium;
      default:
        return FontAwesomeIcons.globe;
    }
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value, {bool canCopy = false, VoidCallback? onTap}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    final bool isClickable = onTap != null;

    Widget content = Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: theme.colorScheme.onSurface,
          size: 20,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      value.toString(),
                      style: TextStyle(
                        color: isClickable ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                        decoration: isClickable ? TextDecoration.underline : TextDecoration.none,
                      ),
                    ),
                  ),
                  if (canCopy)
                    SizedBox(
                      width: 30,
                      height: 30,
                      child: IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy $title',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ],
    );

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: isClickable
          ? InkWell(
              onTap: onTap,
              child: content,
            )
          : content,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = widget.isDarkMode;

    final String fullname = widget.socialMediaFeed['fullname'] ?? 'Unknown';
    final String platform = widget.socialMediaFeed['platform'] ?? '';
    final String link = widget.socialMediaFeed['link'] ?? '';
    final String description = widget.socialMediaFeed['description'] ?? '';
    final String qrCode = widget.socialMediaFeed['qrcode'] ?? '';

    final bool hasLink = link.isNotEmpty;
    final bool hasDescription = description.isNotEmpty;
    final bool hasQRCode = qrCode.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CircleAvatar(
                                radius: 30,
                                backgroundColor: currentIsDarkMode
                                    ? Colors.white.withOpacity(0.1)
                                    : Colors.black.withOpacity(0.1),
                                child: FaIcon(
                                  _getPlatformIcon(platform),
                                  size: 30,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      fullname,
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    if (platform.isNotEmpty)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: Text(
                                          platform,
                                          style: TextStyle(
                                            color: theme.colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          if (hasLink)
                            _buildDetailRow(
                              theme,
                              Icons.link,
                              'Link',
                              link,
                              canCopy: true,
                              onTap: hasLink ? () => _launchURL(link) : null,
                            ),
                          if (hasDescription) ...[
                            const SizedBox(height: 16),
                            Text(
                              'Description',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              description,
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                          if (hasQRCode) ...[
                            const SizedBox(height: 24),
                            Text(
                              'QR Code',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Center(
                              child: Image.network(
                                qrCode,
                                height: 200,
                                width: 200,
                                loadingBuilder: (context, child, loadingProgress) {
                                  if (loadingProgress == null) return child;
                                  return Container(
                                    height: 200,
                                    width: 200,
                                    color: theme.colorScheme.surfaceVariant,
                                    child: const Center(child: CircularProgressIndicator()),
                                  );
                                },
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    height: 200,
                                    width: 200,
                                    color: theme.colorScheme.surfaceVariant,
                                    child: Center(
                                      child: Icon(
                                        Icons.error_outline,
                                        size: 40,
                                        color: theme.colorScheme.error,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                            const SizedBox(height: 8),
                            if (hasLink)
                              Center(
                                child: TextButton(
                                  onPressed: () => _launchURL(link),
                                  child: Text(
                                    'Open Link',
                                    style: TextStyle(
                                      color: theme.colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: hasLink
          ? Container(
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      icon: FaIcon(
                        _getPlatformIcon(platform),
                        color: theme.colorScheme.onSurface,
                      ),
                      onPressed: () => _launchURL(link),
                      tooltip: 'Open $platform',
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.content_copy,
                        color: theme.colorScheme.onSurface,
                      ),
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: link));
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Link copied to clipboard'),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      },
                      tooltip: 'Copy link',
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.open_in_new,
                        color: theme.colorScheme.onSurface,
                      ),
                      onPressed: () => _launchURL(link),
                      tooltip: 'Open in browser',
                    ),
                  ],
                ),
              ),
            )
          : null,
    );
  }
}