// room_equipment_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'room_equipment_detail_page.dart';

class RoomEquipmentPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedRoomEquipment;
  final bool isFromDetailPage;
  final String? roomFilter;
  final String? buildingFilter;

  const RoomEquipmentPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedRoomEquipment,
    this.isFromDetailPage = false,
    this.roomFilter,
    this.buildingFilter,
  }) : super(key: key);

  @override
  State<RoomEquipmentPage> createState() => _RoomEquipmentPageState();
}

class _RoomEquipmentPageState extends State<RoomEquipmentPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('room_equipment_list');
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _roomEquipment = [];
  List<Map<String, dynamic>> _filteredEquipment = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;
  bool _isSearching = false;
  String _searchQuery = '';

  // Filter state
  String? _currentRoomFilter;
  String? _currentBuildingFilter;
  List<Map<String, dynamic>> _locationData = [];
  List<String> _availableBuildings = [];

  @override
  void initState() {
    super.initState();
    print("RoomEquipmentPage initState called");
    _currentRoomFilter = widget.roomFilter;
    _currentBuildingFilter = widget.buildingFilter;
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
    _loadFilterOptions();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void didUpdateWidget(covariant RoomEquipmentPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.collegeNameForTable != widget.collegeNameForTable || 
        oldWidget.roomFilter != widget.roomFilter || 
        oldWidget.buildingFilter != widget.buildingFilter) {
      _currentRoomFilter = widget.roomFilter;
      _currentBuildingFilter = widget.buildingFilter;
      _loadInitialData();
    }
    print("RoomEquipmentPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("RoomEquipmentPage didChangeDependencies called");
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _filterEquipment();
    });
  }

  void _filterEquipment() {
    if (_searchQuery.isEmpty) {
      _filteredEquipment = List.from(_roomEquipment);
    } else {
      _filteredEquipment = _roomEquipment.where((equipment) {
        final fullname = (equipment['fullname'] ?? '').toLowerCase();
        final room = (equipment['room'] ?? '').toLowerCase();
        final building = (equipment['building'] ?? '').toLowerCase();
        final status = (equipment['status'] ?? '').toLowerCase();
        
        return fullname.contains(_searchQuery) ||
               room.contains(_searchQuery) ||
               building.contains(_searchQuery) ||
               status.contains(_searchQuery);
      }).toList();
    }
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
        _searchQuery = '';
        _filterEquipment();
      }
    });
  }

  Future<void> _loadFilterOptions() async {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_roomequipment';
    
    try {
      final response = await Supabase.instance.client
          .from(tableName)
          .select('room, building');

      if (mounted) {
        setState(() {
          // Get unique pairs of (building, room)
          final seen = <String>{};
          final uniqueLocationData = response
              .where((item) {
                final building = item['building'] as String?;
                final room = item['room'] as String?;
                if (building == null || building.isEmpty || room == null || room.isEmpty) {
                  return false;
                }
                final key = '$building|$room';
                return seen.add(key);
              })
              .map((item) => {'building': item['building'], 'room': item['room']})
              .toList();
          
          _locationData = uniqueLocationData;

          // Derive unique, sorted buildings from the data
          _availableBuildings = _locationData
              .map((item) => item['building'] as String)
              .toSet()
              .toList()
              ..sort();
        });
      }
    } catch (error) {
      print("Error loading filter options: $error");
    }
  }

  void _showFilterModal() {
    String? tempRoomFilter = _currentRoomFilter;
    String? tempBuildingFilter = _currentBuildingFilter;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: StatefulBuilder(
            builder: (BuildContext context, StateSetter setModalState) {
              final theme = Theme.of(context);
              final isDarkMode = theme.brightness == Brightness.dark;

              // Dynamically determine available rooms for the modal
              List<String> modalAvailableRooms;
              if (tempBuildingFilter == null) {
                // If no building is selected, show all unique rooms
                modalAvailableRooms = _locationData
                    .map((data) => data['room'] as String)
                    .toSet()
                    .toList()
                    ..sort();
              } else {
                // If a building is selected, show only rooms in that building
                modalAvailableRooms = _locationData
                    .where((data) => data['building'] == tempBuildingFilter)
                    .map((data) => data['room'] as String)
                    .toSet()
                    .toList()
                    ..sort();
              }
              
              return Container(
                width: MediaQuery.of(context).size.width * 0.85,
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.6,
                ),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Text(
                        'Filter Equipment',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                    
                    Flexible(
                      // FIX: Wrapped the filter options in a SingleChildScrollView
                      // to prevent pixel overflow errors on smaller screens.
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Building',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                              const SizedBox(height: 12),
                              
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(color: theme.colorScheme.outline),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<String>(
                                    value: tempBuildingFilter,
                                    isExpanded: true,
                                    hint: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 12),
                                      child: Text(
                                        'Select Building',
                                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    items: [
                                      DropdownMenuItem<String>(
                                        value: null,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 12),
                                          child: Text(
                                            'All Buildings',
                                            style: TextStyle(color: theme.colorScheme.onSurface),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ),
                                      ..._availableBuildings.map((building) => DropdownMenuItem<String>(
                                        value: building,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 12),
                                          child: Text(
                                            building,
                                            style: TextStyle(color: theme.colorScheme.onSurface),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      )),
                                    ],
                                    onChanged: (String? newValue) {
                                      setModalState(() {
                                        tempBuildingFilter = newValue;
                                        // When building changes, reset room to avoid inconsistency.
                                        tempRoomFilter = null;
                                      });
                                    },
                                  ),
                                ),
                              ),
                              
                              const SizedBox(height: 24),
                              
                              Text(
                                'Room',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                              const SizedBox(height: 12),
                              
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(color: theme.colorScheme.outline),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<String>(
                                    value: tempRoomFilter,
                                    isExpanded: true,
                                    hint: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 12),
                                      child: Text(
                                        'Select Room',
                                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    items: [
                                      DropdownMenuItem<String>(
                                        value: null,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 12),
                                          child: Text(
                                            'All Rooms',
                                            style: TextStyle(color: theme.colorScheme.onSurface),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ),
                                      ...modalAvailableRooms.map((room) => DropdownMenuItem<String>(
                                        value: room,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 12),
                                          child: Text(
                                            room,
                                            style: TextStyle(color: theme.colorScheme.onSurface),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      )),
                                    ],
                                    onChanged: (String? newValue) {
                                      setModalState(() {
                                        tempRoomFilter = newValue;
                                      });
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                setModalState(() {
                                  tempRoomFilter = null;
                                  tempBuildingFilter = null;
                                });
                              },
                              style: OutlinedButton.styleFrom(
                                foregroundColor: theme.colorScheme.onSurface,
                                side: BorderSide(color: theme.colorScheme.outline),
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: const Text('Clear All'),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                setState(() {
                                  _currentRoomFilter = tempRoomFilter;
                                  _currentBuildingFilter = tempBuildingFilter;
                                  _page = 0;
                                  _hasMore = true;
                                });
                                Navigator.pop(context);
                                _loadRoomEquipmentFromSupabase(initialLoad: true);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: isDarkMode ? Colors.white : Colors.black,
                                foregroundColor: isDarkMode ? Colors.black : Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: const Text('Apply'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedRoomEquipment != null && widget.preloadedRoomEquipment!.isNotEmpty) {
      print("Preloaded room equipment found, using them.");
      setState(() {
        _roomEquipment = List<Map<String, dynamic>>.from(widget.preloadedRoomEquipment!);
        _roomEquipment.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _filteredEquipment = List.from(_roomEquipment);
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded room equipment or empty list, loading from database.");
      await _loadRoomEquipmentFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_roomequipment';
    
    try {
      var query = Supabase.instance.client.from(tableName).select('id');
      if (_currentRoomFilter != null) {
        query = query.eq('room', _currentRoomFilter!);
      }
      if (_currentBuildingFilter != null) {
        query = query.eq('building', _currentBuildingFilter!);
      }
      
      final response = await query
          .order('fullname', ascending: true)
          .range(_roomEquipment.length, _roomEquipment.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadRoomEquipmentFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadRoomEquipmentFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_roomequipment';

    try {
      int startRange = initialLoad ? 0 : _roomEquipment.length;
      int endRange = startRange + _pageSize - 1;

      var query = Supabase.instance.client.from(tableName).select('*');
      if (_currentRoomFilter != null) {
        query = query.eq('room', _currentRoomFilter!);
      }
      if (_currentBuildingFilter != null) {
        query = query.eq('building', _currentBuildingFilter!);
      }

      final response = await query
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      if (mounted) {
        setState(() {
          final newEquipment = List<Map<String, dynamic>>.from(response);
          if (initialLoad) {
            _roomEquipment = newEquipment;
          } else {
            _roomEquipment.addAll(newEquipment);
          }
          _roomEquipment.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _filteredEquipment = List.from(_roomEquipment);
          _filterEquipment();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching room equipment: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_roomequipment';
    _realtimeChannel = Supabase.instance.client
        .channel('roomequipment_realtime')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: tableName,
      callback: (payload) async {
        if (!mounted) return;
        await _loadRoomEquipmentFromSupabase(initialLoad: true);
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    print("RoomEquipmentPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed && !_isLoadingMore && _hasMore && _scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 100) {
      _loadMoreEquipment();
    }
  }

  Future<void> _loadMoreEquipment() async {
    if (!_isLoadingMore && _hasMore && _searchQuery.isEmpty) {
      print("Loading more room equipment...");
      await _loadRoomEquipmentFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _showOfflineSnackbar() {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _navigateToDetail(BuildContext context, Map<String, dynamic> equipment) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RoomEquipmentDetailPage(
            equipment: equipment,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForTable: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("RoomEquipmentPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    String title = 'Room Equipment';
    if (_currentRoomFilter != null) {
      title = 'Equipment in ${_currentRoomFilter}';
    } else if (_currentBuildingFilter != null) {
      title = 'Equipment in ${_currentBuildingFilter}';
    }

    final equipmentToShow = _searchQuery.isEmpty ? _roomEquipment : _filteredEquipment;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: _isSearching
            ? TextField(
                controller: _searchController,
                autofocus: true,
                decoration: InputDecoration(
                  hintText: 'Search equipment...',
                  hintStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                  border: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                ),
                style: TextStyle(
                  fontSize: 16,
                  color: theme.colorScheme.onSurface,
                ),
              )
            : Text(
                title,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
        actions: [
          IconButton(
            icon: Icon(
              _isSearching ? Icons.close : Icons.search,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _toggleSearch,
          ),
          IconButton(
            icon: Icon(
              Icons.filter_list,
              color: (_currentRoomFilter != null || _currentBuildingFilter != null)
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface,
            ),
            onPressed: _showFilterModal,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadRoomEquipmentFromSupabase(initialLoad: true);
              },
              child: equipmentToShow.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: Center(
                              child: Text(
                                _searchQuery.isNotEmpty 
                                    ? 'No equipment found matching "$_searchQuery"'
                                    : 'No equipment available.',
                              ),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: equipmentToShow.length + (_hasMore && _searchQuery.isEmpty ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < equipmentToShow.length) {
                          final equipment = equipmentToShow[index];
                          return _buildEquipmentCard(equipment, theme, currentIsDarkMode);
                        } else if (_hasMore && _searchQuery.isEmpty) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEquipmentCard(
    Map<String, dynamic> equipment,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = equipment['fullname'] ?? 'Unknown';
    final String room = equipment['room'] ?? '';
    final String building = equipment['building'] ?? '';
    final String status = equipment['status'] ?? '';

    // Format location as "Building Room" (e.g., "Kauke Hall 305")
    String location = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      location = '$building $room';
    } else if (building.isNotEmpty) {
      location = building;
    } else if (room.isNotEmpty) {
      location = room;
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, equipment),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.chair,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (location.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          location,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (status.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          "Status: $status",
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}