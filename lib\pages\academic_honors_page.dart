import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'academic_honor_detail_page.dart';
import 'login_page.dart';

class AcademicHonorsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedAcademicHonors;
  final bool isFromDetailPage;

  const AcademicHonorsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedAcademicHonors,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _AcademicHonorsPageState createState() => _AcademicHonorsPageState();
}

class _AcademicHonorsPageState extends State<AcademicHonorsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('academic_honors_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _academicHonors = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("AcademicHonorsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant AcademicHonorsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("AcademicHonorsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("AcademicHonorsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedAcademicHonors != null && widget.preloadedAcademicHonors!.isNotEmpty) {
      print("Preloaded academic honors found, using them.");
      setState(() {
        _academicHonors = List<Map<String, dynamic>>.from(widget.preloadedAcademicHonors!);
        _academicHonors.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedAcademicHonors!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      // Check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded academic honors or empty list, loading from database.");
      await _loadAcademicHonorsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final academicHonorsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academichonors';
    
    try {
      final response = await Supabase.instance.client
          .from(academicHonorsTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_academicHonors.length, _academicHonors.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadAcademicHonorsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadAcademicHonorsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final academicHonorsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academichonors';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _academicHonors.length;
        endRange = _academicHonors.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(academicHonorsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _academicHonors = List<Map<String, dynamic>>.from(response);
          } else {
            _academicHonors.addAll(List<Map<String, dynamic>>.from(response));
          }
          _academicHonors.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching academic honors: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final academicHonorsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academichonors';
    _realtimeChannel = Supabase.instance.client
        .channel('academic_honors')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: academicHonorsTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newAcademicHonorId = payload.newRecord['id'];
          final newAcademicHonorResponse = await Supabase.instance.client
              .from(academicHonorsTableName)
              .select('*')
              .eq('id', newAcademicHonorId)
              .single();
          if (mounted) {
            Map<String, dynamic> newAcademicHonor = Map.from(newAcademicHonorResponse);
            setState(() {
              _academicHonors.add(newAcademicHonor);
              _academicHonors.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedAcademicHonorId = payload.newRecord['id'];
          final updatedAcademicHonorResponse = await Supabase.instance.client
              .from(academicHonorsTableName)
              .select('*')
              .eq('id', updatedAcademicHonorId)
              .single();
          if (mounted) {
            final updatedAcademicHonor = Map<String, dynamic>.from(updatedAcademicHonorResponse);
            setState(() {
              _academicHonors = _academicHonors.map((academicHonor) {
                return academicHonor['id'] == updatedAcademicHonor['id'] ? updatedAcademicHonor : academicHonor;
              }).toList();
              _academicHonors.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedAcademicHonorId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _academicHonors.removeWhere((academicHonor) => academicHonor['id'] == deletedAcademicHonorId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("AcademicHonorsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreAcademicHonors();
    }
  }

  Future<void> _loadMoreAcademicHonors() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more academic honors...");
      await _loadAcademicHonorsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> academicHonor) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AcademicHonorDetailPage(
            honor: academicHonor,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("AcademicHonorsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Academic Honors',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadAcademicHonorsFromSupabase(initialLoad: true);
              },
              child: _academicHonors.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No academic honors available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _academicHonors.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _academicHonors.length) {
                          final academicHonor = _academicHonors[index];
                          return VisibilityDetector(
                            key: Key('academic_honor_${academicHonor['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              // Academic honors don't have images, so no image loading logic needed
                            },
                            child: _buildAcademicHonorCard(academicHonor, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAcademicHonorCard(
    Map<String, dynamic> academicHonor,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = academicHonor['fullname'] ?? 'Unknown';
    final String major = academicHonor['major'] ?? '';
    final String about = academicHonor['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, academicHonor),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.military_tech,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (major.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Major: $major',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}