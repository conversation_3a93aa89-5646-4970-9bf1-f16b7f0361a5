// news_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

import 'login_page.dart';
import 'news_detail_page.dart';

class NewsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedNews;
  final bool isFromDetailPage;

  const NewsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedNews,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<NewsPage> createState() => _NewsPageState();
}

class _NewsPageState extends State<NewsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('news_list');
  List<Map<String, dynamic>> _news = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("NewsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant NewsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("NewsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("NewsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedNews != null && widget.preloadedNews!.isNotEmpty) {
      print("Preloaded news found, using them.");
      setState(() {
        _news = List<Map<String, dynamic>>.from(widget.preloadedNews!);
        _news.forEach((newsItem) {
          newsItem['_isImageLoading'] = false;
        });
        _sortNews();
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded news or empty list, loading from database.");
      await _loadNewsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final newsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_news';
    
    try {
      final response = await Supabase.instance.client
          .from(newsTableName)
          .select('id')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(_news.length, _news.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadNewsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadNewsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final newsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_news';

    try {
      int startRange = initialLoad ? 0 : _news.length;
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(newsTableName)
          .select('*')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(startRange, endRange);

      final updatedNews =
          await _updateNewsImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _news = updatedNews;
          } else {
            _news.addAll(updatedNews);
          }
          _news.forEach((newsItem) {
            newsItem['_isImageLoading'] = false;
          });
          _sortNews();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
      _cacheNews(_news);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching news: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateNewsImageUrls(
      List<Map<String, dynamic>> news) async {
    List<Future<void>> futures = [];
    for (final newsItem in news) {
      if (newsItem['image_url'] == null ||
          newsItem['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(newsItem));
      }
    }
    await Future.wait(futures);
    return news;
  }
  
  void _setupRealtime() {
    final newsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_news';
    _realtimeChannel = Supabase.instance.client
        .channel('news')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: newsTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newNewsId = payload.newRecord['id'];
          final newNewsResponse = await Supabase.instance.client
              .from(newsTableName)
              .select('*')
              .eq('id', newNewsId)
              .single();
          if (mounted) {
            Map<String, dynamic> newNews = Map.from(newNewsResponse);
            final updatedNews = await _updateNewsImageUrls([newNews]);
            setState(() {
              _news.add(updatedNews.first);
              updatedNews.first['_isImageLoading'] = false;
              _sortNews();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedNewsId = payload.newRecord['id'];
          final updatedNewsResponse = await Supabase.instance.client
              .from(newsTableName)
              .select('*')
              .eq('id', updatedNewsId)
              .single();
          if (mounted) {
            final updatedNews = Map<String, dynamic>.from(updatedNewsResponse);
            setState(() {
              _news = _news.map((newsItem) {
                return newsItem['id'] == updatedNews['id'] ? updatedNews : newsItem;
              }).toList();
              _sortNews();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedNewsId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _news.removeWhere((newsItem) => newsItem['id'] == deletedNewsId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("NewsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreNews();
    }
  }

  Future<void> _loadMoreNews() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more news...");
      await _loadNewsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _sortNews() {
    _news.sort((a, b) {
      final aYear = a['year'] ?? 0;
      final bYear = b['year'] ?? 0;
      if (aYear != bYear) return bYear.compareTo(aYear);
      
      final aMonth = a['month'] ?? 0;
      final bMonth = b['month'] ?? 0;
      if (aMonth != bMonth) return bMonth.compareTo(aMonth);
      
      final aDay = a['day'] ?? 0;
      final bDay = b['day'] ?? 0;
      return bDay.compareTo(aDay);
    });
  }

  Future<void> _cacheNews(List<Map<String, dynamic>> news) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String newsJson = jsonEncode(news);
      await prefs.setString(
          'news_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          newsJson);
    } catch (e) {
      print('Error caching news: $e');
    }
  }
  
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> newsItem) async {
    if (newsItem['_isImageLoading'] == true) return;
    if (newsItem['image_url'] != null && newsItem['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => newsItem['_isImageLoading'] = true);

    final fullname = newsItem['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeNewsBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/news';

    try {
      final file = await Supabase.instance.client.storage.from(collegeNewsBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeNewsBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        newsItem['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        newsItem['_isImageLoading'] = false;
      });
    } else {
      newsItem['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> newsItem) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => NewsDetailPage(
            news: newsItem,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  String _formatDate(Map<String, dynamic> newsItem) {
    final day = newsItem['day'] as int?;
    final month = newsItem['month'] as int?;
    final year = newsItem['year'] as int?;
    
    if (day == null || month == null || year == null) {
      return '';
    }
    
    try {
      final date = DateTime(year, month, day);
      return DateFormat('MMM d, yyyy').format(date);
    } catch (e) {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    print("NewsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'News',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadNewsFromSupabase(initialLoad: true);
              },
              child: _news.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No news available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _news.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _news.length) {
                          final newsItem = _news[index];
                          return VisibilityDetector(
                            key: Key('news_${newsItem['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (newsItem['image_url'] == null ||
                                      newsItem['image_url'] == 'assets/placeholder_image.png') &&
                                  !newsItem['_isImageLoading']) {
                                _fetchImageUrl(newsItem);
                              }
                            },
                            child: _buildNewsCard(newsItem, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNewsCard(
    Map<String, dynamic> newsItem,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = newsItem['fullname'] ?? 'Unknown';
    final String publisher = newsItem['publisher'] ?? '';
    final String dateStr = _formatDate(newsItem);
    final String imageUrl = newsItem['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, newsItem),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.article,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.article,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.article,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (dateStr.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          dateStr,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (publisher.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          publisher,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                           maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}