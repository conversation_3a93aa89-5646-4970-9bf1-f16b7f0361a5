import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'local_lodging_detail_page.dart';
import 'login_page.dart';

class LocalLodgingPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedLocalLodging;
  final bool isFromDetailPage;

  const LocalLodgingPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedLocalLodging,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _LocalLodgingPageState createState() => _LocalLodgingPageState();
}

class _LocalLodgingPageState extends State<LocalLodgingPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('local_lodging_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _localLodging = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("LocalLodgingPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant LocalLodgingPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("LocalLodgingPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("LocalLodgingPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedLocalLodging != null && widget.preloadedLocalLodging!.isNotEmpty) {
      print("Preloaded local lodging found, using them.");
      setState(() {
        _localLodging = List<Map<String, dynamic>>.from(widget.preloadedLocalLodging!);
        _localLodging.forEach((lodging) {
          lodging['_isImageLoading'] = false;
        });
        _localLodging.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedLocalLodging!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      // Don't load more from database if we have preloaded data
      // But check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded local lodging or empty list, loading from database.");
      await _loadLocalLodgingFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final localLodgingTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_locallodging';
    
    try {
      final response = await Supabase.instance.client
          .from(localLodgingTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_localLodging.length, _localLodging.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadLocalLodgingFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadLocalLodgingFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final localLodgingTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_locallodging';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _localLodging.length;
        endRange = _localLodging.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(localLodgingTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedLocalLodging =
          await _updateLocalLodgingImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _localLodging = updatedLocalLodging;
          } else {
            _localLodging.addAll(updatedLocalLodging);
          }
          _localLodging.forEach((lodging) {
            lodging['_isImageLoading'] = false;
          });
          _localLodging.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching local lodging: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateLocalLodgingImageUrls(
      List<Map<String, dynamic>> localLodging) async {
    List<Future<void>> futures = [];
    for (final lodging in localLodging) {
      if (lodging['image_url'] == null ||
          lodging['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(lodging));
      }
    }
    await Future.wait(futures);
    return localLodging;
  }

  void _setupRealtime() {
    final localLodgingTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_locallodging';
    _realtimeChannel = Supabase.instance.client
        .channel('local_lodging')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: localLodgingTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newLodgingId = payload.newRecord['id'];
          final newLodgingResponse = await Supabase.instance.client
              .from(localLodgingTableName)
              .select('*')
              .eq('id', newLodgingId)
              .single();
          if (mounted) {
            Map<String, dynamic> newLodging = Map.from(newLodgingResponse);
            final updatedLodging = await _updateLocalLodgingImageUrls([newLodging]);
            setState(() {
              _localLodging.add(updatedLodging.first);
              updatedLodging.first['_isImageLoading'] = false;
              _localLodging.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedLodgingId = payload.newRecord['id'];
          final updatedLodgingResponse = await Supabase.instance.client
              .from(localLodgingTableName)
              .select('*')
              .eq('id', updatedLodgingId)
              .single();
          if (mounted) {
            final updatedLodging = Map<String, dynamic>.from(updatedLodgingResponse);
            setState(() {
              _localLodging = _localLodging.map((lodging) {
                return lodging['id'] == updatedLodging['id'] ? updatedLodging : lodging;
              }).toList();
              _localLodging.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedLodgingId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _localLodging.removeWhere((lodging) => lodging['id'] == deletedLodgingId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("LocalLodgingPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreLocalLodging();
    }
  }

  Future<void> _loadMoreLocalLodging() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more local lodging...");
      await _loadLocalLodgingFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> lodging) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => LocalLodgingDetailPage(
            localLodging: lodging,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("LocalLodgingPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Local Lodging',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadLocalLodgingFromSupabase(initialLoad: true);
              },
              child: _localLodging.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No local lodging available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _localLodging.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _localLodging.length) {
                          final lodging = _localLodging[index];
                          return VisibilityDetector(
                            key: Key('lodging_${lodging['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (lodging['image_url'] == null ||
                                      lodging['image_url'] == 'assets/placeholder_image.png') &&
                                  !lodging['_isImageLoading']) {
                                _fetchImageUrl(lodging);
                              }
                            },
                            child: _buildLocalLodgingCard(lodging, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> lodging) async {
    if (lodging['_isImageLoading'] == true) {
      print('Image loading already in progress for ${lodging['fullname']}, skipping.');
      return;
    }
    if (lodging['image_url'] != null &&
        lodging['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${lodging['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      lodging['_isImageLoading'] = true;
    });

    final fullname = lodging['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeLocalLodgingBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/locallodging';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeLocalLodgingBucket');
    print('Image URL before fetch: ${lodging['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeLocalLodgingBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeLocalLodgingBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        lodging['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        lodging['_isImageLoading'] = false;
        print('Setting image_url for ${lodging['fullname']} to: ${lodging['image_url']}');
      });
    } else {
      lodging['_isImageLoading'] = false;
    }
  }

  Widget _buildLocalLodgingCard(
    Map<String, dynamic> lodging,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = lodging['fullname'] ?? 'Unknown';
    final String hours = lodging['hours'] ?? '';
    final String payment = lodging['payment'] ?? '';
    final String about = lodging['about'] ?? '';
    final String imageUrl = lodging['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, lodging),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.house,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.house,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.house,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (payment.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Payment: $payment',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          hours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}