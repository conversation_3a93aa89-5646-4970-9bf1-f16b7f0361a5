import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'shop_eatery_detail_page.dart';
import 'login_page.dart';

class ShopsEateriesPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedShopsEateries;
  final bool isFromDetailPage;

  const ShopsEateriesPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedShopsEateries,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _ShopsEateriesPageState createState() => _ShopsEateriesPageState();
}

class _ShopsEateriesPageState extends State<ShopsEateriesPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('shops_eateries_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _shopsEateries = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("ShopsEateriesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant ShopsEateriesPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("ShopsEateriesPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("ShopsEateriesPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedShopsEateries != null && widget.preloadedShopsEateries!.isNotEmpty) {
      print("Preloaded shops & eateries found, using them.");
      setState(() {
        _shopsEateries = List<Map<String, dynamic>>.from(widget.preloadedShopsEateries!);
        _shopsEateries.forEach((shopEatery) {
          shopEatery['_isImageLoading'] = false;
        });
        _shopsEateries.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedShopsEateries!.length == _pageSize;
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded shops & eateries or empty list, loading from database.");
      await _loadShopsEateriesFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final shopsEateriesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_shopsoreateries';
    
    try {
      final response = await Supabase.instance.client
          .from(shopsEateriesTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_shopsEateries.length, _shopsEateries.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  void _setupRealtime() {
    final shopsEateriesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_shopsoreateries';
    _realtimeChannel = Supabase.instance.client
        .channel('shops_eateries_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: shopsEateriesTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        print("Realtime update received for shops & eateries: ${payload.eventType}");
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newShopEateryId = payload.newRecord['id'];
          final newShopEateryResponse = await Supabase.instance.client
              .from(shopsEateriesTableName)
              .select('*')
              .eq('id', newShopEateryId)
              .single();
          if (mounted) {
            Map<String, dynamic> newShopEatery = Map.from(newShopEateryResponse);
            final updatedShopEatery = await _updateShopEateryImageUrls([newShopEatery]);
            setState(() {
              _shopsEateries.add(updatedShopEatery.first);
              updatedShopEatery.first['_isImageLoading'] = false;
              _shopsEateries.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedShopEateryId = payload.newRecord['id'];
          final updatedShopEateryResponse = await Supabase.instance.client
              .from(shopsEateriesTableName)
              .select('*')
              .eq('id', updatedShopEateryId)
              .single();
          if (mounted) {
            final updatedShopEatery = Map<String, dynamic>.from(updatedShopEateryResponse);
            setState(() {
              _shopsEateries = _shopsEateries.map((shopEatery) {
                return shopEatery['id'] == updatedShopEatery['id'] ? updatedShopEatery : shopEatery;
              }).toList();
              _shopsEateries.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedShopEateryId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _shopsEateries.removeWhere((shopEatery) => shopEatery['id'] == deletedShopEateryId);
            });
          }
        }
      },
    ).subscribe();
  }

  Future<void> _loadShopsEateriesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadShopsEateriesFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
        _page = 0;
        _shopsEateries = [];
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final shopsEateriesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_shopsoreateries';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
      } else {
        startRange = _shopsEateries.length;
        endRange = _shopsEateries.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(shopsEateriesTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedShopsEateries = await _updateShopEateryImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _shopsEateries = updatedShopsEateries;
          } else {
            _shopsEateries.addAll(updatedShopsEateries);
          }
          _shopsEateries.forEach((shopEatery) {
            shopEatery['_isImageLoading'] = false;
          });
          _shopsEateries.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching shops & eateries: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateShopEateryImageUrls(
      List<Map<String, dynamic>> shopsEateries) async {
    List<Future<void>> futures = [];
    for (final shopEatery in shopsEateries) {
      if (shopEatery['image_url'] == null ||
          shopEatery['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(shopEatery));
      }
    }
    await Future.wait(futures);
    return shopsEateries;
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreShopsEateries();
    }
  }

  Future<void> _loadMoreShopsEateries() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more shops & eateries...");
      await _loadShopsEateriesFromSupabase(initialLoad: false);
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("ShopsEateriesPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> shopEatery) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ShopEateryDetailPage(
            shopEatery: shopEatery,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> shopEatery) async {
    if (shopEatery['_isImageLoading'] == true) {
      print('Image loading already in progress for ${shopEatery['fullname']}, skipping.');
      return;
    }
    if (shopEatery['image_url'] != null &&
        shopEatery['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${shopEatery['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      shopEatery['_isImageLoading'] = true;
    });

    final fullname = shopEatery['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeShopEateryBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/shopsoreateries';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeShopEateryBucket');
    print('Image URL before fetch: ${shopEatery['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeShopEateryBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeShopEateryBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        shopEatery['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        shopEatery['_isImageLoading'] = false;
        print('Setting image_url for ${shopEatery['fullname']} to: ${shopEatery['image_url']}');
      });
    } else {
      shopEatery['_isImageLoading'] = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    print("ShopsEateriesPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Shops & Eateries',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadShopsEateriesFromSupabase(initialLoad: true);
              },
              child: _shopsEateries.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No shops or eateries available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _shopsEateries.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _shopsEateries.length) {
                          final shopEatery = _shopsEateries[index];
                          return VisibilityDetector(
                            key: Key('shop_eatery_${shopEatery['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (shopEatery['image_url'] == null ||
                                      shopEatery['image_url'] == 'assets/placeholder_image.png') &&
                                  !shopEatery['_isImageLoading']) {
                                _fetchImageUrl(shopEatery);
                              }
                            },
                            child: _buildShopEateryCard(shopEatery, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShopEateryCard(
    Map<String, dynamic> shopEatery,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = shopEatery['fullname'] ?? 'Unknown';
    final String type = shopEatery['type'] ?? '';
    final String building = shopEatery['building'] ?? '';
    final String room = shopEatery['room'] ?? '';
    final String hours = shopEatery['hours'] ?? '';
    final String payment = shopEatery['payment'] ?? '';
    final String imageUrl = shopEatery['image_url'] ?? '';
    
    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, shopEatery),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                  ? ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: imageUrl,
                        width: 48,
                        height: 48,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          width: 48,
                          height: 48,
                          color: theme.colorScheme.onSurface.withOpacity(0.1),
                          child: const Center(
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          width: 48,
                          height: 48,
                          color: theme.colorScheme.onSurface.withOpacity(0.1),
                          child: Icon(
                            Icons.store,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                    )
                  : Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.onSurface.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.store,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (type.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        type,
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                    if (locationText.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        locationText,
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                    if (hours.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Hours: $hours',
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                    if (payment.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Payment: $payment',
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface,
              ),
            ],
          ),
        ),
      ),
    );
  }
}