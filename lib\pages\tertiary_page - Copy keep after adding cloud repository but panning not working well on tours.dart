import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../main.dart';
import 'login_page.dart';
import 'tertiary_detail_page.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/cache_manager.dart';

// --- UPDATED: Import the new AI Cache Warmer service ---
import '../services/ai_cache_warmer.dart';

class TertiaryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String selectedCountry;

  const TertiaryPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.selectedCountry,
  }) : super(key: key);

  @override
  State<TertiaryPage> createState() => _TertiaryPageState();
}

class _TertiaryPageState extends State<TertiaryPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('tertiary_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _colleges = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<Map<String, dynamic>> _filteredColleges = [];
  Timer? _debounceTimer;

  // State variables for expanded filters
  String? _selectedState;
  String? _selectedCity;
  String? _selectedInstitutionType;
  String? _selectedOwnership;
  String? _selectedAffiliation;

  @override
  void initState() {
    super.initState();
    _loadPreloadedColleges();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // --- CORE CHANGE: Triggers the new AI Cache Warmer ---
  /// Navigates to the detail page AND triggers the background AI knowledge base caching.
  void _navigateToDetail(BuildContext context, Map<String, dynamic> college) {
    if (_isDisposed) return;

    // 1. TRIGGER THE AI CACHE WARMING PROCESS (FIRE-AND-FORGET)
    // This runs silently in the background. We DO NOT `await` it to ensure
    // navigation is immediate.
    AICacheWarmer.prewarmCacheForCollege(college).catchError((e) {
      // Catch errors silently so a background failure doesn't crash the app.
      // The error is already logged for debugging inside the warmer service.
      print("Background AI pre-warming failed silently: $e");
    });

    // 2. PROCEED WITH IMMEDIATE NAVIGATION
    final currentIsDarkMode = Theme.of(context).brightness == Brightness.dark;
    _checkTodayEventsAvailability(college).then((hasTodayEvents) {
      if (!_isDisposed) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TertiaryDetailPage(
              college: college,
              isDarkMode: currentIsDarkMode,
              toggleTheme: widget.toggleTheme,
              hasTodayEventsPreloaded: hasTodayEvents,
              isFromTertiaryPage: true,
            ),
          ),
        );
      }
    });
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (!_isDisposed) {
        setState(() {
          _searchQuery = _searchController.text;
          _filterColleges();
        });
      }
    });
  }

  void _filterColleges() {
    List<Map<String, dynamic>> sourceList = List.from(_colleges);

    if (_searchQuery.isNotEmpty) {
      sourceList = sourceList.where((college) {
        final fullName = (college['fullname'] as String? ?? '').toLowerCase();
        final city = (college['city_or_district'] as String? ?? '').toLowerCase();
        final state = (college['state_or_region'] as String? ?? '').toLowerCase();
        final country = (college['country'] as String? ?? '').toLowerCase();
        final searchQueryLower = _searchQuery.toLowerCase();
        return fullName.contains(searchQueryLower) ||
            city.contains(searchQueryLower) ||
            state.contains(searchQueryLower) ||
            country.contains(searchQueryLower);
      }).toList();
    }
    
    setState(() {
      _filteredColleges = sourceList;
    });
  }

  void _applyFiltersAndReload() {
    setState(() {
      _page = 0;
      _colleges.clear();
      _filteredColleges.clear();
      _hasMore = true;
    });
    _loadPreloadedColleges();
  }

  void _loadPreloadedColleges() {
    if (MyApp.preloadedColleges != null) {
      var filteredList = MyApp.preloadedColleges!
          .where((c) => c['country'] == widget.selectedCountry)
          .toList();

      if (_selectedState != null) {
        filteredList = filteredList.where((c) => c['state_or_region'] == _selectedState).toList();
      }
      if (_selectedCity != null) {
        filteredList = filteredList.where((c) => c['city_or_district'] == _selectedCity).toList();
      }
      if (_selectedInstitutionType != null) {
        filteredList = filteredList.where((c) => c['institutiontype'] == _selectedInstitutionType).toList();
      }
       if (_selectedOwnership != null) {
        filteredList = filteredList.where((c) => c['ownership'] == _selectedOwnership).toList();
      }
      if (_selectedAffiliation != null) {
        filteredList = filteredList.where((c) => c['religiousaffiliation'] == _selectedAffiliation).toList();
      }

      if (filteredList.isNotEmpty) {
        setState(() {
          _colleges = List<Map<String, dynamic>>.from(filteredList);
          _colleges.sort((a, b) => (a['fullname'] ?? '')
              .toLowerCase()
              .compareTo((b['fullname'] ?? '').toLowerCase()));
          _hasMore = filteredList.length >= _pageSize;
          _filterColleges();
        });
        for (var college in _colleges) {
          if (college['image_url'] == null ||
              college['image_url'] == 'assets/placeholder_image.png') {
            _fetchImageUrl(college);
          }
        }
      } else {
        _loadCollegesFromSupabase();
      }
    } else {
      _loadCollegesFromSupabase();
    }
  }

  Future<void> _loadCollegesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    if (!_isDisposed) {
      setState(() {
        _isLoading = true;
      });
    }

    final cacheKey = 'colleges_country_${widget.selectedCountry}_state_${_selectedState}_city_${_selectedCity}_type_${_selectedInstitutionType}_ownership_${_selectedOwnership}_affiliation_${_selectedAffiliation}_page_$_page';

    try {
      List<Map<String, dynamic>> response;
      bool isFromCache = false;

      var query = Supabase.instance.client
          .from('colleges')
          .select('*')
          .eq('country', widget.selectedCountry);

      if (_selectedState != null) query = query.eq('state_or_region', _selectedState!);
      if (_selectedCity != null) query = query.eq('city_or_district', _selectedCity!);
      if (_selectedInstitutionType != null) query = query.eq('institutiontype', _selectedInstitutionType!);
      if (_selectedOwnership != null) query = query.eq('ownership', _selectedOwnership!);
      if (_selectedAffiliation != null) query = query.eq('religiousaffiliation', _selectedAffiliation!);

      try {
        final startIndex = _page * _pageSize;
        final endIndex = startIndex + _pageSize - 1;

        response = await query.order('fullname', ascending: true).range(startIndex, endIndex);

        await CacheManager.cacheData(cacheKey, response);
      } catch (e) {
        final cachedData = await CacheManager.getCachedData(cacheKey);
        if (cachedData != null) {
          response = cachedData;
          isFromCache = true;
          print('Using cached colleges data for page $_page (network failed)');
        } else {
          throw e;
        }
      }

      final updatedColleges = await _updateCollegeImageUrls(response);

      if (!_isDisposed) {
        setState(() {
          if (initialLoad) {
            _colleges = updatedColleges;
          } else {
            _colleges.addAll(updatedColleges);
          }
          _isLoading = false;
          _hasMore = response.length == _pageSize;
          _filterColleges();
        });
      }

      if (isFromCache && initialLoad) {
        _refreshDataInBackground();
      }
    } catch (error) {
       if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') ||
            errorStr.contains('offline')) {
          errorMsg = "Offline. Using cached data if available.";

          final cachedData = await CacheManager.getCachedData(cacheKey, maxAgeMinutes: 43200);
          if (cachedData != null) {
            setState(() {
              if (initialLoad) {
                _colleges = cachedData;
              } else {
                _colleges.addAll(cachedData);
              }
              _isLoading = false;
              _hasMore = cachedData.length == _pageSize;
              _filterColleges();
            });

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                  content: Text("Offline. Using cached data."),
                  backgroundColor: Colors.orange),
            );
            return;
          }
        } else if (errorStr.contains('relation') &&
            errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
        } else {
          errorMsg = "Error fetching colleges: $error";
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMsg), backgroundColor: Colors.redAccent),
        );
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<void> _refreshDataInBackground() async {
    try {
      final startIndex = _page * _pageSize;
      final endIndex = startIndex + _pageSize - 1;

      var query = Supabase.instance.client
          .from('colleges')
          .select('*')
          .eq('country', widget.selectedCountry);

      if (_selectedState != null) query = query.eq('state_or_region', _selectedState!);
      if (_selectedCity != null) query = query.eq('city_or_district', _selectedCity!);
      if (_selectedInstitutionType != null) query = query.eq('institutiontype', _selectedInstitutionType!);
      if (_selectedOwnership != null) query = query.eq('ownership', _selectedOwnership!);
      if (_selectedAffiliation != null) query = query.eq('religiousaffiliation', _selectedAffiliation!);
      
      final List<Map<String, dynamic>> response = await query
          .order('fullname', ascending: true)
          .range(startIndex, endIndex);

      final cacheKey = 'colleges_country_${widget.selectedCountry}_state_${_selectedState}_city_${_selectedCity}_type_${_selectedInstitutionType}_ownership_${_selectedOwnership}_affiliation_${_selectedAffiliation}_page_$_page';
      await CacheManager.cacheData(cacheKey, response);

      if (!_isDisposed) {
        final updatedColleges = await _updateCollegeImageUrls(response);

        setState(() {
          _colleges = updatedColleges;
          _filterColleges();
        });
      }
    } catch (e) {
      print('Background refresh failed: $e');
    }
  }

  Future<List<Map<String, dynamic>>> _updateCollegeImageUrls(
      List<Map<String, dynamic>> colleges) async {
    List<Future<void>> futures = [];
    for (final college in colleges) {
      if (college['image_url'] == null ||
          college['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(college));
      }
    }
    await Future.wait(futures);
    return colleges;
  }

  void _setupRealtime() {
    _realtimeChannel = Supabase.instance.client
        .channel('colleges')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'colleges',
      callback: (payload) async {
        final record = payload.newRecord.isNotEmpty ? payload.newRecord : payload.oldRecord;
        
        bool countryMatch = record['country'] == widget.selectedCountry;
        bool stateMatch = _selectedState == null || record['state_or_region'] == _selectedState;
        bool cityMatch = _selectedCity == null || record['city_or_district'] == _selectedCity;
        bool typeMatch = _selectedInstitutionType == null || record['institutiontype'] == _selectedInstitutionType;
        bool ownershipMatch = _selectedOwnership == null || record['ownership'] == _selectedOwnership;
        bool affiliationMatch = _selectedAffiliation == null || record['religiousaffiliation'] == _selectedAffiliation;

        if (!_isDisposed && countryMatch && stateMatch && cityMatch && typeMatch && ownershipMatch && affiliationMatch) {
          print("Realtime event received matching filters, refreshing list.");
          _page = 0;
          _hasMore = true;
          _loadCollegesFromSupabase(initialLoad: true);
        }
      },
    ).subscribe();
  }

  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreColleges();
    }
  }

  Future<void> _loadMoreColleges() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadCollegesFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  Future<bool> _checkTodayEventsAvailability(Map<String, dynamic> college) async {
    final now = DateTime.now();
    final todayDay = DateFormat('dd').format(now);
    final todayMonth = DateFormat('MM').format(now);
    final todayYear = DateFormat('yyyy').format(now);

    final eventsTableName = '${college['fullname']
            .toString()
            .toLowerCase()
            .replaceAll(' ', '')}_events';

    try {
      final response = await Supabase.instance.client
          .from(eventsTableName)
          .select('id')
          .eq('startday', todayDay)
          .eq('startmonth', todayMonth)
          .eq('startyear', todayYear)
          .limit(1);

      if (response is List) {
        return response.isNotEmpty;
      }
      return false;
    } catch (error) {
      print("Info: Could not check for today's events for ${college['fullname']}. Table might not exist. Error: $error");
      return false;
    }
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> college) async {
    if (college['image_url'] != null &&
        college['image_url'] != 'assets/placeholder_image.png') {
      return;
    }

    final fullname = college['fullname'] as String? ?? '';
    final collegeId = college['id']?.toString() ?? '';
    final cacheKey = 'college_image_${collegeId}_${fullname.replaceAll(' ', '_')}';

    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedImageUrl = prefs.getString(cacheKey);

      if (cachedImageUrl != null && cachedImageUrl.isNotEmpty) {
        if (mounted) {
          setState(() {
            college['image_url'] = cachedImageUrl;
          });
        }
        return;
      }
    } catch (e) {
      print('Error retrieving cached image URL: $e');
    }

    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';

    try {
      imageUrl = Supabase.instance.client
          .storage
          .from('colleges')
          .getPublicUrl(imageNameWebp);

      if (imageUrl.isNotEmpty) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(cacheKey, imageUrl);
      }
    } catch (e) {
      print('Error fetching image from Supabase: $e');
    }

    if (mounted) {
      setState(() {
        college['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
      });
    }
  }

  Future<List<String>> _getDistinctValues(String columnName) async {
    try {
      final response = await Supabase.instance.client
          .from('colleges')
          .select(columnName)
          .eq('country', widget.selectedCountry)
          .withConverter<List<String>>(
              (data) => (data as List).map<String>((row) => row[columnName].toString()).toSet().toList()..sort());
      return response;
    } catch (e) {
      print("Error fetching distinct values for $columnName: $e");
      return [];
    }
  }

void _showFilterDialog() {
  final theme = Theme.of(context);

  // Temporary state holders, initialized with the current filters
  String? tempSelectedState = _selectedState;
  String? tempSelectedCity = _selectedCity;
  String? tempSelectedInstitutionType = _selectedInstitutionType;
  String? tempSelectedOwnership = _selectedOwnership;
  String? tempSelectedAffiliation = _selectedAffiliation;

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return StatefulBuilder(
        builder: (context, setDialogState) {
          return AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.0)),
            backgroundColor: theme.colorScheme.surface,
            title: Text('Apply Filters', style: TextStyle(color: theme.colorScheme.onSurface)),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  _buildFilterChip('State/Region', tempSelectedState, 'state_or_region', (newValue) {
                    setDialogState(() => tempSelectedState = newValue);
                  }),
                  _buildFilterChip('City/District', tempSelectedCity, 'city_or_district', (newValue) {
                    setDialogState(() => tempSelectedCity = newValue);
                  }),
                  _buildFilterChip('Institution Type', tempSelectedInstitutionType, 'institutiontype', (newValue) {
                    setDialogState(() => tempSelectedInstitutionType = newValue);
                  }),
                  _buildFilterChip('Ownership', tempSelectedOwnership, 'ownership', (newValue) {
                    setDialogState(() => tempSelectedOwnership = newValue);
                  }),
                  _buildFilterChip('Religious Affiliation', tempSelectedAffiliation, 'religiousaffiliation', (newValue) {
                    setDialogState(() => tempSelectedAffiliation = newValue);
                  }),
                ],
              ),
            ),
            actions: <Widget>[
              TextButton(
                child: Text('Clear', style: TextStyle(color: theme.colorScheme.onSurface)),
                onPressed: () {
                  // Clear the temporary state within the dialog
                  setDialogState(() {
                    tempSelectedState = null;
                    tempSelectedCity = null;
                    tempSelectedInstitutionType = null;
                    tempSelectedOwnership = null;
                    tempSelectedAffiliation = null;
                  });
                },
              ),
              TextButton(
                child: Text('Close', style: TextStyle(color: theme.colorScheme.onSurface)),
                onPressed: () {
                  Navigator.of(context).pop(); // Just close, discard temp changes
                },
              ),
              TextButton(
                child: Text('Apply', style: TextStyle(color: theme.colorScheme.onSurface, fontWeight: FontWeight.bold)),
                onPressed: () {
                  // Apply changes from temp state to the main page state
                  setState(() {
                    _selectedState = tempSelectedState;
                    _selectedCity = tempSelectedCity;
                    _selectedInstitutionType = tempSelectedInstitutionType;
                    _selectedOwnership = tempSelectedOwnership;
                    _selectedAffiliation = tempSelectedAffiliation;
                  });
                  Navigator.of(context).pop(); // Close the dialog
                  _applyFiltersAndReload(); // Reload the list with new filters
                },
              ),
            ],
          );
        },
      );
    },
  );
}

  Widget _buildFilterChip(String label, String? currentValue, String columnName, ValueChanged<String> onSelected) {
    final theme = Theme.of(context);
    final chipBackgroundColor = theme.brightness == Brightness.light ? Colors.white : Colors.grey[850];

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: ActionChip(
        avatar: Icon(Icons.arrow_drop_down, color: theme.colorScheme.onSurface),
        label: Text(
          currentValue ?? label,
          style: TextStyle(color: theme.colorScheme.onSurface),
        ),
        backgroundColor: chipBackgroundColor,
        side: BorderSide.none,
        onPressed: () async {
          final selectedValue = await _showOptionSelectionDialog(label, columnName);
          if (selectedValue != null) {
            onSelected(selectedValue);
          }
        },
      ),
    );
  }

  Future<String?> _showOptionSelectionDialog(String title, String columnName) async {
    final theme = Theme.of(context);
    final options = await _getDistinctValues(columnName);

    if (!mounted) return null;

    return showDialog<String>(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: theme.colorScheme.surface,
          title: Text('Select $title', style: TextStyle(color: theme.colorScheme.onSurface)),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: options.length,
              itemBuilder: (context, index) {
                return ListTile(
                  title: Text(options[index], style: TextStyle(color: theme.colorScheme.onSurface)),
                  onTap: () {
                    Navigator.of(context).pop(options[index]);
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () {
            if (_isSearching) {
              setState(() {
                _isSearching = false;
                _searchController.clear();
              });
            } else {
              Navigator.pop(context);
            }
          },
        ),
        title: _isSearching
            ? Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.background,
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: TextField(
                  controller: _searchController,
                  autofocus: true,
                  style: TextStyle(color: theme.colorScheme.onBackground),
                  decoration: InputDecoration(
                    hintText: 'Search colleges...',
                    hintStyle: TextStyle(
                      color: theme.colorScheme.onBackground.withOpacity(0.6),
                    ),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                  ),
                  cursorColor: theme.colorScheme.onBackground,
                ),
              )
            : Text(
                'Tertiary',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
        actions: [
          IconButton(
            icon: Icon(
              _isSearching ? Icons.close : Icons.search,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              });
            },
          ),
          IconButton(
            icon: Icon(Icons.more_vert, color: theme.colorScheme.onSurface),
            onPressed: _showFilterDialog,
            tooltip: 'Filter',
          ),
        ],
      ),
      body: RefreshIndicator(
        // THIS IS THE FIX: Set the color of the refresh icon
        color: theme.colorScheme.onSurface,
        onRefresh: () {
          _page = 0;
          _hasMore = true;
          return _loadCollegesFromSupabase(initialLoad: true);
        },
        child: (_searchQuery.isNotEmpty ? _filteredColleges : _colleges).isEmpty && _isLoading
            ? const Center(child: CircularProgressIndicator())
            : (_searchQuery.isNotEmpty ? _filteredColleges : _colleges).isEmpty && !_isLoading
                ? LayoutBuilder(
                    builder: (BuildContext context, BoxConstraints constraints) {
                      return SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: SizedBox(
                          height: constraints.maxHeight,
                          child: Center(
                            child: Text('No colleges found with the current filters.'),
                          ),
                        ),
                      );
                    },
                  )
                : ListView.builder(
                    key: _listKey,
                    controller: _scrollController,
                    shrinkWrap: true,
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(16),
                    itemCount: (_searchQuery.isNotEmpty ? _filteredColleges : _colleges).length +
                        (_hasMore && _searchQuery.isEmpty ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index < (_searchQuery.isNotEmpty ? _filteredColleges : _colleges).length) {
                        final university = (_searchQuery.isNotEmpty ? _filteredColleges : _colleges)[index];

                        final city = university['city_or_district'] ?? '';
                        final state = university['state_or_region'] ?? '';
                        final country = university['country'] ?? '';
                        final locationParts = [city, state, country]
                            .where((part) => part != null && part.isNotEmpty)
                            .toList();
                        final locationText = locationParts.join(', ');
                        final institutionType = university['institutiontype'] ?? 'N/A';

                        return VisibilityDetector(
                          key: Key('college_${university['id']}'),
                          onVisibilityChanged: (VisibilityInfo info) {
                            if (info.visibleFraction > 0.1 &&
                                (university['image_url'] == null ||
                                    university['image_url'] == 'assets/placeholder_image.png')) {
                              _fetchImageUrl(university);
                            }
                          },
                          child: Card(
                            color: theme.colorScheme.surface,
                            surfaceTintColor: Colors.transparent,
                            margin: const EdgeInsets.only(bottom: 16),
                            child: ListTile(
                              contentPadding: const EdgeInsets.all(16),
                              leading: ClipOval(
                                child: SizedBox(
                                  width: 40,
                                  height: 40,
                                  child: CachedNetworkImage(
                                    imageUrl: university['image_url'] ?? 'assets/placeholder_image.png',
                                    errorWidget: (context, url, error) => Image.asset('assets/placeholder_image.png'),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              title: Text(
                                university['fullname'] ?? 'Unnamed College',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onSurface,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                              subtitle: Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      locationText,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: theme.colorScheme.secondary,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                    ),
                                    const SizedBox(height: 2),
                                    Text(
                                      institutionType,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: theme.colorScheme.secondary?.withOpacity(0.8),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              onTap: () => _navigateToDetail(context, university),
                            ),
                          ),
                        );
                      } else if (_hasMore && _searchQuery.isEmpty) {
                        return const Center(
                            child: Padding(
                                padding: EdgeInsets.all(16),
                                child: CircularProgressIndicator()));
                      } else {
                        return Container();
                      }
                    },
                  ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}