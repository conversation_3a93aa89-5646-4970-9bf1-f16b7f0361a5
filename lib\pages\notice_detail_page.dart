import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

class NoticeDetailPage extends StatefulWidget {
  final Map<String, dynamic> notice;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const NoticeDetailPage({
    Key? key,
    required this.notice,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<NoticeDetailPage> createState() => _NoticeDetailPageState();
}

class _NoticeDetailPageState extends State<NoticeDetailPage> {
  late RealtimeChannel _noticeRealtimeChannel;
  String? _imageUrl;
  bool _isLoadingImage = false;

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _noticeRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.notice['image_url'];
      _isLoadingImage = false;
    });
  }

  void _setupRealtimeListener() {
    _noticeRealtimeChannel = Supabase.instance.client
        .channel('notice_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'notices',
      callback: (payload) async {
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.notice['id']) {
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshNotice();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshNotice() async {
    try {
      final response = await Supabase.instance.client
          .from('notices')
          .select('*')
          .eq('id', widget.notice['id'])
          .single();

      if (mounted) {
        setState(() {
          widget.notice.clear();
          widget.notice.addAll(response);
          _loadImageFromPreloadedData();
          _updateNoticeCache();
        });
      }
    } catch (e) {
      print("Error refreshing notice: $e");
    }
  }

  Future<void> _updateNoticeCache() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final cacheKey = 'notices';
    String? cachedNoticesJson = prefs.getString(cacheKey);

    if (cachedNoticesJson != null) {
      List<Map<String, dynamic>> cachedNotices = 
          (jsonDecode(cachedNoticesJson) as List<dynamic>).cast<Map<String, dynamic>>();

      for (int i = 0; i < cachedNotices.length; i++) {
        if (cachedNotices[i]['id'] == widget.notice['id']) {
          cachedNotices[i] = widget.notice;
          break;
        }
      }

      await prefs.setString(cacheKey, jsonEncode(cachedNotices));
    }
  }

  Future<void> _launchURL(String url) async {
    if (url.isEmpty) return;
    
    // Add https:// if not present
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }
    
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      _showErrorSnackbar('Could not launch link.');
    }
  }

  void _showErrorSnackbar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.redAccent,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    // Extract notice information
    final String fullname = widget.notice['fullname'] ?? 'Unknown';
    final String about = widget.notice['about'] ?? '';
    final String link = widget.notice['link'] ?? '';
    
    // Format date
    String dateText = '';
    if (widget.notice['day'] != null && widget.notice['month'] != null && widget.notice['year'] != null) {
      final day = widget.notice['day'] as int;
      final month = widget.notice['month'] as int;
      final year = widget.notice['year'] as int;
      
      try {
        final date = DateTime(year, month, day);
        dateText = DateFormat('MMMM d, yyyy').format(date);
      } catch (e) {
        print('Error formatting date: $e');
      }
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top image section
            _isLoadingImage
                ? Container(
                    height: 200,
                    color: theme.colorScheme.surfaceVariant,
                    child: const Center(child: CircularProgressIndicator()),
                  )
                : (_imageUrl != null && _imageUrl!.isNotEmpty)
                    ? CachedNetworkImage(
                        imageUrl: _imageUrl!,
                        height: 200,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          height: 200,
                          color: theme.colorScheme.surfaceVariant,
                          child: const Center(child: CircularProgressIndicator()),
                        ),
                        errorWidget: (context, url, error) => _buildPlaceholderImage(theme),
                      )
                    : _buildPlaceholderImage(theme),
            
            // Main content card
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with name and date
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode
                                ? Colors.white.withOpacity(0.1)
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.announcement,
                              size: 30,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  fullname,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                if (dateText.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      dateText,
                                      style: TextStyle(
                                        color: theme.colorScheme.onSurfaceVariant,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Link information
                      if (link.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.link, 
                          'Link', 
                          link, 
                          onTap: () => _launchURL(link),
                          canCopy: true,
                        ),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 24),
                        _buildSectionTitle(theme, Icons.info_outline, 'About this Notice'),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 14,
                            height: 1.5,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      // Bottom action button (only if link exists)
      bottomNavigationBar: link.isNotEmpty
          ? Container(
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.open_in_browser,
                        color: theme.colorScheme.onSurface,
                      ),
                      onPressed: () => _launchURL(link),
                      tooltip: 'Open link in browser',
                    ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildPlaceholderImage(ThemeData theme) {
    return Container(
      height: 200,
      color: theme.colorScheme.surfaceVariant,
      child: Center(
        child: Icon(
          Icons.announcement,
          size: 50,
          color: theme.colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    ThemeData theme, 
    IconData icon, 
    String title, 
    dynamic value, {
    bool canCopy = false, 
    VoidCallback? onTap,
  }) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    final bool isClickable = onTap != null;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurface,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                    fontSize: 13,
                  ),
                ),
                const SizedBox(height: 4),
                InkWell(
                  onTap: onTap,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          value.toString(),
                          style: TextStyle(
                            color: theme.colorScheme.onSurface,
                            decoration: isClickable 
                                ? TextDecoration.underline 
                                : TextDecoration.none,
                            fontSize: 15,
                          ),
                        ),
                      ),
                      if (canCopy)
                        SizedBox(
                          width: 30,
                          height: 30,
                          child: IconButton(
                            icon: Icon(
                              Icons.content_copy,
                              size: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                            onPressed: () {
                              Clipboard.setData(ClipboardData(text: value.toString()));
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('$title copied to clipboard'),
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            },
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(ThemeData theme, IconData icon, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, top: 8.0),
      child: Row(
        children: [
          Icon(
            icon, 
            color: theme.colorScheme.onSurface, 
            size: 20
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}