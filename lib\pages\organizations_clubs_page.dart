// organizations_clubs_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'organization_club_detail_page.dart';

class OrganizationsClubsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedOrgsClubs;
  final bool isFromDetailPage;

  const OrganizationsClubsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedOrgsClubs,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<OrganizationsClubsPage> createState() => _OrganizationsClubsPageState();
}

class _OrganizationsClubsPageState extends State<OrganizationsClubsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('orgs_clubs_list');
  List<Map<String, dynamic>> _orgsClubs = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("OrganizationsClubsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant OrganizationsClubsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("OrganizationsClubsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("OrganizationsClubsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedOrgsClubs != null && widget.preloadedOrgsClubs!.isNotEmpty) {
      print("Preloaded organizations/clubs found, using them.");
      setState(() {
        _orgsClubs = List<Map<String, dynamic>>.from(widget.preloadedOrgsClubs!);
        _orgsClubs.forEach((orgClub) {
          orgClub['_isImageLoading'] = false;
        });
        _orgsClubs.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded organizations/clubs or empty list, loading from database.");
      await _loadOrgsClubsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final orgsClubsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_orgsorclubs';
    
    try {
      final response = await Supabase.instance.client
          .from(orgsClubsTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_orgsClubs.length, _orgsClubs.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadOrgsClubsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadOrgsClubsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final orgsClubsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_orgsorclubs';

    try {
      int startRange = initialLoad ? 0 : _orgsClubs.length;
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(orgsClubsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedOrgsClubs =
          await _updateOrgClubImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _orgsClubs = updatedOrgsClubs;
          } else {
            _orgsClubs.addAll(updatedOrgsClubs);
          }
          _orgsClubs.forEach((orgClub) {
            orgClub['_isImageLoading'] = false;
          });
          _orgsClubs.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
      _cacheOrgsClubs(_orgsClubs);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching organizations/clubs: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateOrgClubImageUrls(
      List<Map<String, dynamic>> orgsClubs) async {
    List<Future<void>> futures = [];
    for (final orgClub in orgsClubs) {
      if (orgClub['image_url'] == null ||
          orgClub['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(orgClub));
      }
    }
    await Future.wait(futures);
    return orgsClubs;
  }
  
  void _setupRealtime() {
    final orgsClubsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_orgsorclubs';
    _realtimeChannel = Supabase.instance.client
        .channel('orgs_clubs')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: orgsClubsTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newOrgClubId = payload.newRecord['id'];
          final newOrgClubResponse = await Supabase.instance.client
              .from(orgsClubsTableName)
              .select('*')
              .eq('id', newOrgClubId)
              .single();
          if (mounted) {
            Map<String, dynamic> newOrgClub = Map.from(newOrgClubResponse);
            final updatedOrgClub = await _updateOrgClubImageUrls([newOrgClub]);
            setState(() {
              _orgsClubs.add(updatedOrgClub.first);
              updatedOrgClub.first['_isImageLoading'] = false;
              _orgsClubs.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedOrgClubId = payload.newRecord['id'];
          final updatedOrgClubResponse = await Supabase.instance.client
              .from(orgsClubsTableName)
              .select('*')
              .eq('id', updatedOrgClubId)
              .single();
          if (mounted) {
            final updatedOrgClub = Map<String, dynamic>.from(updatedOrgClubResponse);
            setState(() {
              _orgsClubs = _orgsClubs.map((orgClub) {
                return orgClub['id'] == updatedOrgClub['id'] ? updatedOrgClub : orgClub;
              }).toList();
              _orgsClubs.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedOrgClubId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _orgsClubs.removeWhere((orgClub) => orgClub['id'] == deletedOrgClubId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("OrganizationsClubsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreOrgsClubs();
    }
  }

  Future<void> _loadMoreOrgsClubs() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more organizations/clubs...");
      await _loadOrgsClubsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _cacheOrgsClubs(List<Map<String, dynamic>> orgsClubs) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String orgsClubsJson = jsonEncode(orgsClubs);
      await prefs.setString(
          'orgsorclubs_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          orgsClubsJson);
    } catch (e) {
      print('Error caching organizations/clubs: $e');
    }
  }
  
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> orgClub) async {
    if (orgClub['_isImageLoading'] == true) return;
    if (orgClub['image_url'] != null && orgClub['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => orgClub['_isImageLoading'] = true);

    final fullname = orgClub['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeOrgClubBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/orgsorclubs';

    try {
      final file = await Supabase.instance.client.storage.from(collegeOrgClubBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeOrgClubBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        orgClub['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        orgClub['_isImageLoading'] = false;
      });
    } else {
      orgClub['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> orgClub) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => OrganizationClubDetailPage(
            orgClub: orgClub,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("OrganizationsClubsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Organizations & Clubs',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadOrgsClubsFromSupabase(initialLoad: true);
              },
              child: _orgsClubs.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No organizations or clubs available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _orgsClubs.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _orgsClubs.length) {
                          final orgClub = _orgsClubs[index];
                          return VisibilityDetector(
                            key: Key('orgclub_${orgClub['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (orgClub['image_url'] == null ||
                                      orgClub['image_url'] == 'assets/placeholder_image.png') &&
                                  !orgClub['_isImageLoading']) {
                                _fetchImageUrl(orgClub);
                              }
                            },
                            child: _buildOrgClubCard(orgClub, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOrgClubCard(
    Map<String, dynamic> orgClub,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = orgClub['fullname'] ?? 'Unknown';
    final String about = orgClub['about'] ?? '';
    final String imageUrl = orgClub['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, orgClub),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.group,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.group,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.group,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}