// pages/directory_page.dart

import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'chatbot_page.dart'; // We will create this file next

class DirectoryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String selectedCountry;

  const DirectoryPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.selectedCountry,
  }) : super(key: key);

  @override
  State<DirectoryPage> createState() => _DirectoryPageState();
}

class _DirectoryPageState extends State<DirectoryPage> {
  bool _isLoading = true;
  String _error = '';
  // This map will hold the organizations grouped by their category.
  // e.g., {'Banking': [Org1, Org2], 'Retail': [Org3]}
  Map<String, List<Map<String, dynamic>>> _organizationsByCategory = {};

  @override
  void initState() {
    super.initState();
    _fetchOrganizations();
  }

  Future<void> _fetchOrganizations() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      // NOTE: Assumes a table named 'organizations' exists in your Supabase.
      // This table should have columns: 'name', 'category', 'country'.
      final response = await Supabase.instance.client
          .from('organizations')
          .select()
          .eq('country', widget.selectedCountry)
          .order('name', ascending: true);

      if (response is List) {
        final organizations = List<Map<String, dynamic>>.from(response);
        final groupedData = <String, List<Map<String, dynamic>>>{};

        for (final org in organizations) {
          final category = org['category'] as String? ?? 'Uncategorized';
          if (groupedData.containsKey(category)) {
            groupedData[category]!.add(org);
          } else {
            groupedData[category] = [org];
          }
        }
        
        // Sort categories alphabetically
        final sortedKeys = groupedData.keys.toList()..sort();
        _organizationsByCategory = {for (var k in sortedKeys) k: groupedData[k]!};

      } else {
        _error = 'Failed to load organizations. Unexpected response.';
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      print('Error fetching organizations: $_error');
    }

    if(mounted){
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.selectedCountry} Directory'),
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error.isNotEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Could not load directory.\n$_error\n\nPlease ensure a table named "organizations" exists in Supabase.',
            textAlign: TextAlign.center,
            style: TextStyle(color: theme.colorScheme.secondary),
          ),
        ),
      );
    }

    if (_organizationsByCategory.isEmpty) {
      return Center(
        child: Text(
          'No organizations found for ${widget.selectedCountry}',
          style: TextStyle(color: theme.colorScheme.secondary),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: _organizationsByCategory.keys.length,
      itemBuilder: (context, index) {
        final category = _organizationsByCategory.keys.elementAt(index);
        final organizations = _organizationsByCategory[category]!;

        return Card(
          margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
          color: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          child: ExpansionTile(
            title: Text(
              category,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
                color: theme.colorScheme.onSurface,
              ),
            ),
            children: organizations.map((org) {
              return ListTile(
                title: Text(org['name'] ?? 'No Name'),
                leading: Icon(Icons.business, color: theme.colorScheme.secondary),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ChatbotPage(
                        organization: org,
                        isDarkMode: widget.isDarkMode,
                      ),
                    ),
                  );
                },
              );
            }).toList(),
          ),
        );
      },
    );
  }
}