import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class SustainabilityDetailPage extends StatefulWidget {
  final Map<String, dynamic> sustainabilityInitiative;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const SustainabilityDetailPage({
    Key? key,
    required this.sustainabilityInitiative,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<SustainabilityDetailPage> createState() => _SustainabilityDetailPageState();
}

class _SustainabilityDetailPageState extends State<SustainabilityDetailPage> {
  late RealtimeChannel _sustainabilityRealtimeChannel;
  String? _imageUrl;
  bool _isLoadingImage = false;

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _sustainabilityRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.sustainabilityInitiative['image_url'];
      _isLoadingImage = false;
    });
  }

  void _setupRealtimeListener() {
    _sustainabilityRealtimeChannel = Supabase.instance.client
        .channel('sustainability_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'sustainability',
      callback: (payload) async {
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.sustainabilityInitiative['id']) {
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshSustainabilityInitiative();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshSustainabilityInitiative() async {
    try {
      final response = await Supabase.instance.client
          .from('sustainability')
          .select('*')
          .eq('id', widget.sustainabilityInitiative['id'])
          .single();

      if (mounted) {
        setState(() {
          widget.sustainabilityInitiative.clear();
          widget.sustainabilityInitiative.addAll(response);
          _loadImageFromPreloadedData();
          _updateSustainabilityCache();
        });
      }
    } catch (e) {
      print("Error refreshing sustainability initiative: $e");
    }
  }

  Future<void> _updateSustainabilityCache() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final cacheKey = 'sustainability_initiatives';
    String? cachedSustainabilityJson = prefs.getString(cacheKey);

    if (cachedSustainabilityJson != null) {
      List<Map<String, dynamic>> cachedSustainability = 
          (jsonDecode(cachedSustainabilityJson) as List<dynamic>).cast<Map<String, dynamic>>();

      for (int i = 0; i < cachedSustainability.length; i++) {
        if (cachedSustainability[i]['id'] == widget.sustainabilityInitiative['id']) {
          cachedSustainability[i] = widget.sustainabilityInitiative;
          break;
        }
      }

      await prefs.setString(cacheKey, jsonEncode(cachedSustainability));
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    // Extract initiative information
    final String fullname = widget.sustainabilityInitiative['fullname'] ?? 'Unknown';
    final String about = widget.sustainabilityInitiative['about'] ?? '';
    final String location = widget.sustainabilityInitiative['location'] ?? '';
    final String impact = widget.sustainabilityInitiative['impact'] ?? '';

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top image section
            _isLoadingImage
                ? Container(
                    height: 200,
                    color: theme.colorScheme.surfaceVariant,
                    child: const Center(child: CircularProgressIndicator()),
                  )
                : (_imageUrl != null && _imageUrl!.isNotEmpty)
                    ? CachedNetworkImage(
                        imageUrl: _imageUrl!,
                        height: 200,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          height: 200,
                          color: theme.colorScheme.surfaceVariant,
                          child: const Center(child: CircularProgressIndicator()),
                        ),
                        errorWidget: (context, url, error) => _buildPlaceholderImage(theme),
                      )
                    : _buildPlaceholderImage(theme),
            
            // Main content card
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with name and location
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode
                                ? Colors.white.withOpacity(0.1)
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.eco,
                              size: 30,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  fullname,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                if (location.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      location,
                                      style: TextStyle(
                                        color: theme.colorScheme.onSurfaceVariant,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Sustainability details
                      if (impact.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.assessment_outlined, 
                          'Impact', 
                          impact
                        ),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 24),
                        _buildSectionTitle(theme, Icons.info_outline, 'About this Initiative'),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 14,
                            height: 1.5,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage(ThemeData theme) {
    return Container(
      height: 200,
      color: theme.colorScheme.surfaceVariant,
      child: Center(
        child: Icon(
          Icons.eco,
          size: 50,
          color: theme.colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    ThemeData theme, 
    IconData icon, 
    String title, 
    dynamic value, {
    bool canCopy = false, 
    VoidCallback? onTap,
  }) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurface,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                    fontSize: 13,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value.toString(),
                  style: TextStyle(
                    color: theme.colorScheme.onSurface,
                    fontSize: 15,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(ThemeData theme, IconData icon, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, top: 8.0),
      child: Row(
        children: [
          Icon(
            icon, 
            color: theme.colorScheme.onSurface, 
            size: 20
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}