import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'login_page.dart';
import 'atm_detail_page.dart';
import 'tertiary_money_page.dart';

class ATMsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ATMsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<ATMsPage> createState() => _ATMsPageState();
}

class _ATMsPageState extends State<ATMsPage> 
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('atms_list');
  bool _isDisposed = false;
  List<ATM> _atms = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 10;
  bool _hasMore = true;
  bool _showMap = false;
  LatLng? _mapCenter;

  @override
  void initState() {
    super.initState();
    print("ATMsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant ATMsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("ATMsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("ATMsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    await _loadATMsFromSupabase(initialLoad: true);
  }

  Future<void> _loadATMsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadATMsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_atms';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _atms.length;
        endRange = _atms.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('building', ascending: true)
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final List<ATM> atms = List<Map<String, dynamic>>.from(response)
          .map((json) => ATM.fromJson(json))
          .toList();

      // Find map center (use first ATM with location or default to a central point)
      if (initialLoad && _mapCenter == null) {
        for (var atm in atms) {
          if (atm.hasLocation()) {
            _mapCenter = LatLng(atm.latitude!, atm.longitude!);
            break;
          }
        }
      }

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _atms = atms;
          } else {
            _atms.addAll(atms);
          }
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (e) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = e.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching ATMs: $e";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_atms';
    _realtimeChannel = Supabase.instance.client
        .channel('atms')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: tableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newAtmId = payload.newRecord['id'];
          final newAtmResponse = await Supabase.instance.client
              .from(tableName)
              .select('*')
              .eq('id', newAtmId)
              .single();
          if (mounted) {
            final newAtm = ATM.fromJson(Map.from(newAtmResponse));
            setState(() {
              _atms.add(newAtm);
              _atms.sort((a, b) {
                int buildingCompare = a.building.toLowerCase().compareTo(b.building.toLowerCase());
                if (buildingCompare != 0) return buildingCompare;
                return a.fullname.toLowerCase().compareTo(b.fullname.toLowerCase());
              });
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedAtmId = payload.newRecord['id'];
          final updatedAtmResponse = await Supabase.instance.client
              .from(tableName)
              .select('*')
              .eq('id', updatedAtmId)
              .single();
          if (mounted) {
            final updatedAtm = ATM.fromJson(Map<String, dynamic>.from(updatedAtmResponse));
            setState(() {
              _atms = _atms.map((atm) {
                return atm.id == updatedAtm.id ? updatedAtm : atm;
              }).toList();
              _atms.sort((a, b) {
                int buildingCompare = a.building.toLowerCase().compareTo(b.building.toLowerCase());
                if (buildingCompare != 0) return buildingCompare;
                return a.fullname.toLowerCase().compareTo(b.fullname.toLowerCase());
              });
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedAtmId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _atms.removeWhere((atm) => atm.id == deletedAtmId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("ATMsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreATMs();
    }
  }

  Future<void> _loadMoreATMs() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more ATMs...");
      await _loadATMsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, ATM atm) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ATMDetailPage(
            atm: atm,
            institutionName: widget.institutionName,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("ATMsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'ATMs',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _showMap ? Icons.list : Icons.map,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              setState(() {
                _showMap = !_showMap;
              });
            },
            tooltip: _showMap ? 'Show List' : 'Show Map',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadATMsFromSupabase(initialLoad: true);
              },
              child: _atms.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No ATMs available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : _showMap && _mapCenter != null
                      ? _buildMap(theme, currentIsDarkMode)
                      : _showMap
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.location_off,
                                    size: 64,
                                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No ATMs with location data available',
                                    style: TextStyle(
                                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  ElevatedButton(
                                    onPressed: () {
                                      setState(() {
                                        _showMap = false;
                                      });
                                    },
                                    child: const Text('Show List View'),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              key: _listKey,
                              controller: _scrollController,
                              shrinkWrap: true,
                              physics: const AlwaysScrollableScrollPhysics(),
                              padding: const EdgeInsets.all(16),
                              itemCount: _atms.length + (_hasMore ? 1 : 0),
                              itemBuilder: (context, index) {
                                if (index < _atms.length) {
                                  final atm = _atms[index];
                                  return VisibilityDetector(
                                    key: Key('atm_${atm.id}'),
                                    onVisibilityChanged: (VisibilityInfo info) {
                                      // No image loading logic needed for ATMs
                                    },
                                    child: _buildATMCard(atm, theme, currentIsDarkMode),
                                  );
                                } else if (_hasMore) {
                                  return const Center(
                                      child: Padding(
                                          padding: EdgeInsets.all(16),
                                          child: CircularProgressIndicator()));
                                } else {
                                  return Container();
                                }
                              },
                            ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildATMCard(ATM atm, ThemeData theme, bool isDarkMode) {
    String locationText = '';
    if (atm.building.isNotEmpty && atm.room.isNotEmpty) {
      locationText = '${atm.building}, Room ${atm.room}';
    } else if (atm.building.isNotEmpty) {
      locationText = atm.building;
    } else if (atm.room.isNotEmpty) {
      locationText = 'Room ${atm.room}';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, atm),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.atm,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      atm.fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (atm.about.isNotEmpty && atm.about != 'No description available')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          atm.about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMap(ThemeData theme, bool currentIsDarkMode) {
    // Filter ATMs with valid locations
    final atmsWithLocation = _atms.where((atm) => atm.hasLocation()).toList();
    
    return FlutterMap(
      options: MapOptions(
        initialCenter: _mapCenter!,
        initialZoom: 16.0,
      ),
      children: [
        TileLayer(
          urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
          userAgentPackageName: 'com.harmonizr.app',
        ),
        MarkerLayer(
          markers: atmsWithLocation.map((atm) {
            return Marker(
              point: LatLng(atm.latitude!, atm.longitude!),
              width: 40,
              height: 40,
              child: GestureDetector(
                onTap: () => _navigateToDetail(context, atm),
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.atm,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      child: Text(
                        atm.fullname,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}