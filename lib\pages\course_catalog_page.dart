import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'course_detail_page.dart';
import 'login_page.dart';

class CourseCatalogPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedCourseCatalog;
  final bool isFromDetailPage;

  const CourseCatalogPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedCourseCatalog,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _CourseCatalogPageState createState() => _CourseCatalogPageState();
}

class _CourseCatalogPageState extends State<CourseCatalogPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('course_catalog_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _courses = [];
  List<Map<String, dynamic>> _filteredCourses = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  
  // Filter variables
  String? _selectedMajor;
  int? _selectedYear;
  List<String> _availableMajors = [];
  List<int> _availableYears = [];
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    print("CourseCatalogPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant CourseCatalogPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("CourseCatalogPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("CourseCatalogPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedCourseCatalog != null && widget.preloadedCourseCatalog!.isNotEmpty) {
      print("Preloaded courses found, using them.");
      setState(() {
        _courses = List<Map<String, dynamic>>.from(widget.preloadedCourseCatalog!);
        _courses.sort((a, b) =>
            (a['modulecode'] ?? '').toLowerCase().compareTo((b['modulecode'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedCourseCatalog!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      _extractFilterOptions();
      _applyFilters();
      // Check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded courses or empty list, loading from database.");
      await _loadCoursesFromSupabase(initialLoad: true);
    }
  }

  void _extractFilterOptions() {
    Set<String> majors = {};
    Set<int> years = {};
    
    for (var course in _courses) {
      // Extract major/program from department field
      if (course['department'] != null && course['department'].toString().isNotEmpty) {
        majors.add(course['department'].toString());
      }
      
      // Extract year
      if (course['year'] != null) {
        years.add(course['year'] as int);
      }
    }
    
    setState(() {
      _availableMajors = majors.toList()..sort();
      _availableYears = years.toList()..sort();
    });
  }

  void _applyFilters() {
    List<Map<String, dynamic>> filtered = List.from(_courses);
    
    if (_selectedMajor != null) {
      filtered = filtered.where((course) => 
        course['department']?.toString() == _selectedMajor).toList();
    }
    
    if (_selectedYear != null) {
      filtered = filtered.where((course) => 
        course['year'] == _selectedYear).toList();
    }
    
    setState(() {
      _filteredCourses = filtered;
    });
  }

  void _clearFilters() {
    setState(() {
      _selectedMajor = null;
      _selectedYear = null;
    });
    _applyFilters();
  }

  Future<void> _checkForMoreData() async {
    final courseCatalogTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_coursecatalog';
    
    try {
      final response = await Supabase.instance.client
          .from(courseCatalogTableName)
          .select('id')
          .order('modulecode', ascending: true)
          .range(_courses.length, _courses.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  void _setupRealtime() {
    final courseCatalogTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_coursecatalog';
    _realtimeChannel = Supabase.instance.client
        .channel('course_catalog_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: courseCatalogTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        print("Realtime update received for course catalog: ${payload.eventType}");
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newCourseId = payload.newRecord['id'];
          final newCourseResponse = await Supabase.instance.client
              .from(courseCatalogTableName)
              .select('*')
              .eq('id', newCourseId)
              .single();
          if (mounted) {
            Map<String, dynamic> newCourse = Map.from(newCourseResponse);
            setState(() {
              _courses.add(newCourse);
              _courses.sort((a, b) =>
                  (a['modulecode'] ?? '').toLowerCase().compareTo((b['modulecode'] ?? '').toLowerCase()));
            });
            _extractFilterOptions();
            _applyFilters();
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedCourseId = payload.newRecord['id'];
          final updatedCourseResponse = await Supabase.instance.client
              .from(courseCatalogTableName)
              .select('*')
              .eq('id', updatedCourseId)
              .single();
          if (mounted) {
            final updatedCourse = Map<String, dynamic>.from(updatedCourseResponse);
            setState(() {
              _courses = _courses.map((course) {
                return course['id'] == updatedCourse['id'] ? updatedCourse : course;
              }).toList();
              _courses.sort((a, b) =>
                  (a['modulecode'] ?? '').toLowerCase().compareTo((b['modulecode'] ?? '').toLowerCase()));
            });
            _extractFilterOptions();
            _applyFilters();
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedCourseId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _courses.removeWhere((course) => course['id'] == deletedCourseId);
            });
            _extractFilterOptions();
            _applyFilters();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _loadCoursesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadCoursesFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final courseCatalogTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_coursecatalog';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _courses.length;
        endRange = _courses.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(courseCatalogTableName)
          .select('*')
          .order('modulecode', ascending: true)
          .range(startRange, endRange);

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _courses = List<Map<String, dynamic>>.from(response);
          } else {
            _courses.addAll(List<Map<String, dynamic>>.from(response));
          }
          _courses.sort((a, b) =>
              (a['modulecode'] ?? '').toLowerCase().compareTo((b['modulecode'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
        _extractFilterOptions();
        _applyFilters();
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching courses: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreCourses();
    }
  }

  Future<void> _loadMoreCourses() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more courses...");
      await _loadCoursesFromSupabase(initialLoad: false);
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("CourseCatalogPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> course) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CourseDetailPage(
            course: course,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  Widget _buildFilterSection(ThemeData theme) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: _showFilters ? null : 0,
      child: _showFilters ? Container(
        padding: const EdgeInsets.all(16),
        color: theme.colorScheme.surface,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Filters',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                if (_selectedMajor != null || _selectedYear != null)
                  TextButton(
                    onPressed: _clearFilters,
                    child: Text(
                      'Clear All',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            // Major/Program Filter
            Text(
              'Major/Program',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: theme.colorScheme.outline),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButton<String>(
                value: _selectedMajor,
                hint: Text(
                  'Select Major/Program',
                  style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                ),
                isExpanded: true,
                underline: const SizedBox.shrink(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedMajor = newValue;
                  });
                  _applyFilters();
                },
                items: _availableMajors.map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
            ),
            const SizedBox(height: 16),
            // Year Filter
            Text(
              'Year',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: theme.colorScheme.outline),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButton<int>(
                value: _selectedYear,
                hint: Text(
                  'Select Year',
                  style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                ),
                isExpanded: true,
                underline: const SizedBox.shrink(),
                onChanged: (int? newValue) {
                  setState(() {
                    _selectedYear = newValue;
                  });
                  _applyFilters();
                },
                items: _availableYears.map<DropdownMenuItem<int>>((int value) {
                  return DropdownMenuItem<int>(
                    value: value,
                    child: Text('Year $value'),
                  );
                }).toList(),
              ),
            ),
            const SizedBox(height: 16),
            // Active filters display
            if (_selectedMajor != null || _selectedYear != null)
              Wrap(
                spacing: 8,
                children: [
                  if (_selectedMajor != null)
                    Chip(
                      label: Text(_selectedMajor!),
                      deleteIcon: const Icon(Icons.close, size: 16),
                      onDeleted: () {
                        setState(() {
                          _selectedMajor = null;
                        });
                        _applyFilters();
                      },
                    ),
                  if (_selectedYear != null)
                    Chip(
                      label: Text('Year $_selectedYear'),
                      deleteIcon: const Icon(Icons.close, size: 16),
                      onDeleted: () {
                        setState(() {
                          _selectedYear = null;
                        });
                        _applyFilters();
                      },
                    ),
                ],
              ),
          ],
        ),
      ) : const SizedBox.shrink(),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("CourseCatalogPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final coursesToDisplay = _filteredCourses.isNotEmpty || _selectedMajor != null || _selectedYear != null
        ? _filteredCourses
        : _courses;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Course Catalog',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _showFilters ? Icons.filter_list : Icons.filter_list_outlined,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              setState(() {
                _showFilters = !_showFilters;
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterSection(theme),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                    color: theme.colorScheme.onSurface,
                    onRefresh: () async {
                      setState(() {
                        _page = 0;
                        _hasMore = true;
                      });
                      await _loadCoursesFromSupabase(initialLoad: true);
                    },
                    child: coursesToDisplay.isEmpty
                        ? LayoutBuilder(
                            builder: (BuildContext context, BoxConstraints constraints) {
                              return SingleChildScrollView(
                                physics: const AlwaysScrollableScrollPhysics(),
                                child: SizedBox(
                                  height: constraints.maxHeight,
                                  child: Center(
                                    child: Text(
                                      _selectedMajor != null || _selectedYear != null
                                          ? 'No courses match the selected filters.'
                                          : 'No courses available.',
                                    ),
                                  ),
                                ),
                              );
                            },
                          )
                        : ListView.builder(
                            key: _listKey,
                            controller: _scrollController,
                            shrinkWrap: true,
                            physics: const AlwaysScrollableScrollPhysics(),
                            padding: const EdgeInsets.all(16),
                            itemCount: coursesToDisplay.length + (_hasMore && (_selectedMajor == null && _selectedYear == null) ? 1 : 0),
                            itemBuilder: (context, index) {
                              if (index < coursesToDisplay.length) {
                                final course = coursesToDisplay[index];
                                return VisibilityDetector(
                                  key: Key('course_${course['id']}'),
                                  onVisibilityChanged: (VisibilityInfo info) {
                                    // Optional: Add any visibility-based loading logic here
                                  },
                                  child: _buildCourseCard(course, theme, currentIsDarkMode),
                                );
                              } else if (_hasMore && _selectedMajor == null && _selectedYear == null) {
                                return const Center(
                                    child: Padding(
                                        padding: EdgeInsets.all(16),
                                        child: CircularProgressIndicator()));
                              } else {
                                return Container();
                              }
                            },
                          ),
                  ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCourseCard(
    Map<String, dynamic> course,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String moduleCode = course['modulecode'] ?? '';
    final String moduleName = course['modulename'] ?? 'Unknown';
    final String department = course['department'] ?? '';
    final int? year = course['year'];
    final String term = course['term'] ?? '';
    final int? credits = course['credits'];
    final bool isCore = course['corecourse'] == true;
    final bool isElective = course['electivecourse'] == true;
    final bool isOffered = course['offered'] == true;

    String courseTitle = moduleCode.isNotEmpty ? '$moduleCode: $moduleName' : moduleName;

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, course),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.book,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            courseTitle,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                        if (!isOffered)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              'Not Offered',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                            ),
                          ),
                      ],
                    ),
                    if (department.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Department: $department',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Row(
                        children: [
                          if (year != null)
                            Text(
                              'Year $year',
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                          if (year != null && term.isNotEmpty)
                            Text(
                              ' • ',
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                          if (term.isNotEmpty)
                            Text(
                              term,
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                          if ((year != null || term.isNotEmpty) && credits != null)
                            Text(
                              ' • ',
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                          if (credits != null)
                            Text(
                              '$credits credits',
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                        ],
                      ),
                    ),
                    if (isCore || isElective)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Wrap(
                          spacing: 8,
                          children: [
                            if (isCore)
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.black,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'Core',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            if (isElective)
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.grey[600],
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'Elective',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}