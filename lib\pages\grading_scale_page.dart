import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'login_page.dart';

class GradingScalePage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedGradingScale;
  final bool isFromDetailPage;

  const GradingScalePage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedGradingScale,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _GradingScalePageState createState() => _GradingScalePageState();
}

class _GradingScalePageState extends State<GradingScalePage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('grading_scale_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _gradingScale = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  String _selectedProgramLevel = 'All Levels';
  List<String> _programLevels = ['All Levels'];

  @override
  void initState() {
    super.initState();
    print("GradingScalePage initState called");
    _loadInitialData();
    _setupRealtime();
  }

  @override
  void didUpdateWidget(covariant GradingScalePage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("GradingScalePage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("GradingScalePage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedGradingScale != null &&
        widget.preloadedGradingScale!.isNotEmpty) {
      print("Preloaded grading scale found, using it.");
      setState(() {
        _gradingScale = List.from(widget.preloadedGradingScale!);
        _isLoading = false;
      });
      _extractProgramLevels();
    } else {
      print("No preloaded grading scale or empty list, loading from database.");
      await _loadGradingScaleFromDatabase();
    }
  }

  void _extractProgramLevels() {
    Set<String> programLevelsSet = {'All Levels'};

    for (var grade in _gradingScale) {
      if (grade['programlevel'] != null && grade['programlevel'].toString().isNotEmpty) {
        programLevelsSet.add(grade['programlevel']);
      }
    }

    if (mounted) {
      setState(() {
        _programLevels = programLevelsSet.toList()..sort();
      });
    }
  }

  void _setupRealtime() {
    final gradingScaleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_gradingscale';
    _realtimeChannel = Supabase.instance.client
        .channel('grading_scale_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: gradingScaleTableName,
      callback: (payload) async {
        print("Realtime update received for grading scale: ${payload.eventType}");
        if (mounted) {
          await _loadGradingScaleFromDatabase();
        }
      },
    ).subscribe();
  }

  Future<void> _loadGradingScaleFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    print("_loadGradingScaleFromDatabase called");
    
    setState(() {
      _isLoading = true;
    });

    try {
      final gradingScaleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_gradingscale';
      
      // Fixed query logic for filtering
      PostgrestFilterBuilder query = Supabase.instance.client
          .from(gradingScaleTableName)
          .select('*');

      // Apply program level filter only if not "All Levels"
      if (_selectedProgramLevel != 'All Levels') {
        query = query.eq('programlevel', _selectedProgramLevel);
      }

      // Execute the query with ordering
      final response = await query.order('mark', ascending: false);

      if (mounted) {
        setState(() {
          _gradingScale = List<Map<String, dynamic>>.from(response);
          _isLoading = false;
        });

        _extractProgramLevels();
      }
    } catch (e) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = e.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error loading grading scale: $e";
          _showErrorSnackbar(errorMsg);
        }
        
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _filterByProgramLevel(String programLevel) {
    setState(() {
      _selectedProgramLevel = programLevel;
    });
    _loadGradingScaleFromDatabase();
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("GradingScalePage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    print("GradingScalePage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Grading Scale',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Program level filter
          if (_programLevels.length > 1)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: 'Program Level',
                  labelStyle: TextStyle(
                    color: currentIsDarkMode ? Colors.white : Colors.black,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  filled: true,
                  fillColor: theme.colorScheme.surface,
                ),
                value: _selectedProgramLevel,
                style: TextStyle(
                  color: currentIsDarkMode ? Colors.white : Colors.black,
                ),
                dropdownColor: theme.colorScheme.surface,
                items: _programLevels.map((String level) {
                  return DropdownMenuItem<String>(
                    value: level,
                    child: Text(
                      level,
                      style: TextStyle(
                        fontSize: 14,
                        color: currentIsDarkMode ? Colors.white : Colors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    _filterByProgramLevel(newValue);
                  }
                },
              ),
            ),

          // Grading scale content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                    color: theme.colorScheme.onSurface,
                    onRefresh: _loadGradingScaleFromDatabase,
                    child: _gradingScale.isEmpty
                        ? LayoutBuilder(
                            builder: (BuildContext context, BoxConstraints constraints) {
                              return SingleChildScrollView(
                                physics: const AlwaysScrollableScrollPhysics(),
                                child: SizedBox(
                                  height: constraints.maxHeight,
                                  child: const Center(
                                    child: Text('No grading scale found'),
                                  ),
                                ),
                              );
                            },
                          )
                        : VisibilityDetector(
                            key: const Key('grading_scale_visibility'),
                            onVisibilityChanged: (info) {
                              if (info.visibleFraction > 0 && _gradingScale.isEmpty && !_isLoading) {
                                _loadGradingScaleFromDatabase();
                              }
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Card(
                                color: theme.colorScheme.surface,
                                surfaceTintColor: Colors.transparent,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (_selectedProgramLevel != 'All Levels')
                                      Padding(
                                        padding: const EdgeInsets.all(16.0),
                                        child: Text(
                                          'Grading Scale for $_selectedProgramLevel',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: theme.colorScheme.onSurface,
                                          ),
                                        ),
                                      ),
                                    
                                    // Sticky header
                                    Container(
                                      color: Colors.black,
                                      child: Table(
                                        border: TableBorder.all(
                                          color: theme.colorScheme.outline.withOpacity(0.3),
                                          width: 1,
                                        ),
                                        columnWidths: const {
                                          0: FlexColumnWidth(1),
                                          1: FlexColumnWidth(1),
                                          2: FlexColumnWidth(2),
                                        },
                                        children: [
                                          TableRow(
                                            decoration: BoxDecoration(
                                              color: Colors.black,
                                            ),
                                            children: [
                                              _buildTableHeaderCell('Mark'),
                                              _buildTableHeaderCell('Grade'),
                                              _buildTableHeaderCell('Remark'),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    
                                    // Scrollable table content
                                    Expanded(
                                      child: SingleChildScrollView(
                                        controller: _scrollController,
                                        child: Table(
                                          border: TableBorder.all(
                                            color: theme.colorScheme.outline.withOpacity(0.3),
                                            width: 1,
                                          ),
                                          columnWidths: const {
                                            0: FlexColumnWidth(1),
                                            1: FlexColumnWidth(1),
                                            2: FlexColumnWidth(2),
                                          },
                                          children: _gradingScale.map((grade) {
                                            return TableRow(
                                              children: [
                                                _buildTableCell(theme, grade['mark']?.toString() ?? ''),
                                                _buildTableCell(theme, grade['lettergrade']?.toString() ?? ''),
                                                _buildTableCell(theme, grade['graderemark']?.toString() ?? ''),
                                              ],
                                            );
                                          }).toList(),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                  ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTableHeaderCell(String text) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Text(
        text,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white, // Always white text on black header
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTableCell(ThemeData theme, String text) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Text(
        text,
        style: TextStyle(
          color: theme.brightness == Brightness.dark ? Colors.white : Colors.black,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}