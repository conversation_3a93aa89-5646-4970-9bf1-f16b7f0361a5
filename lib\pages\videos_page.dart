// videos_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

import 'login_page.dart';
import 'video_detail_page.dart';

class VideosPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedVideos;
  final bool isFromDetailPage;

  const VideosPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedVideos,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<VideosPage> createState() => _VideosPageState();
}

class _VideosPageState extends State<VideosPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('videos_list');
  List<Map<String, dynamic>> _videos = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("VideosPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant VideosPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("VideosPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("VideosPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedVideos != null && widget.preloadedVideos!.isNotEmpty) {
      print("Preloaded videos found, using them.");
      setState(() {
        _videos = List<Map<String, dynamic>>.from(widget.preloadedVideos!);
        _videos.forEach((video) {
          video['_isImageLoading'] = false;
        });
        _sortVideos();
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded videos or empty list, loading from database.");
      await _loadVideosFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final videosTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_videos';
    
    try {
      final response = await Supabase.instance.client
          .from(videosTableName)
          .select('id')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(_videos.length, _videos.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadVideosFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadVideosFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final videosTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_videos';

    try {
      int startRange = initialLoad ? 0 : _videos.length;
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(videosTableName)
          .select('*')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(startRange, endRange);

      final updatedVideos =
          await _updateVideoImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _videos = updatedVideos;
          } else {
            _videos.addAll(updatedVideos);
          }
          _videos.forEach((video) {
            video['_isImageLoading'] = false;
          });
          _sortVideos();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
      _cacheVideos(_videos);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching videos: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateVideoImageUrls(
      List<Map<String, dynamic>> videos) async {
    List<Future<void>> futures = [];
    for (final video in videos) {
      if (video['image_url'] == null ||
          video['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(video));
      }
    }
    await Future.wait(futures);
    return videos;
  }
  
  void _setupRealtime() {
    final videosTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_videos';
    _realtimeChannel = Supabase.instance.client
        .channel('videos')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: videosTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newVideoId = payload.newRecord['id'];
          final newVideoResponse = await Supabase.instance.client
              .from(videosTableName)
              .select('*')
              .eq('id', newVideoId)
              .single();
          if (mounted) {
            Map<String, dynamic> newVideo = Map.from(newVideoResponse);
            final updatedVideo = await _updateVideoImageUrls([newVideo]);
            setState(() {
              _videos.add(updatedVideo.first);
              updatedVideo.first['_isImageLoading'] = false;
              _sortVideos();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedVideoId = payload.newRecord['id'];
          final updatedVideoResponse = await Supabase.instance.client
              .from(videosTableName)
              .select('*')
              .eq('id', updatedVideoId)
              .single();
          if (mounted) {
            final updatedVideo = Map<String, dynamic>.from(updatedVideoResponse);
            setState(() {
              _videos = _videos.map((video) {
                return video['id'] == updatedVideo['id'] ? updatedVideo : video;
              }).toList();
              _sortVideos();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedVideoId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _videos.removeWhere((video) => video['id'] == deletedVideoId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("VideosPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreVideos();
    }
  }

  Future<void> _loadMoreVideos() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more videos...");
      await _loadVideosFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _sortVideos() {
    _videos.sort((a, b) {
      final aYear = a['year'] ?? 0;
      final bYear = b['year'] ?? 0;
      if (aYear != bYear) return bYear.compareTo(aYear);
      
      final aMonth = a['month'] ?? 0;
      final bMonth = b['month'] ?? 0;
      if (aMonth != bMonth) return bMonth.compareTo(aMonth);
      
      final aDay = a['day'] ?? 0;
      final bDay = b['day'] ?? 0;
      return bDay.compareTo(aDay);
    });
  }

  Future<void> _cacheVideos(List<Map<String, dynamic>> videos) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String videosJson = jsonEncode(videos);
      await prefs.setString(
          'videos_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          videosJson);
    } catch (e) {
      print('Error caching videos: $e');
    }
  }
  
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> video) async {
    if (video['_isImageLoading'] == true) return;
    if (video['image_url'] != null && video['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => video['_isImageLoading'] = true);

    final fullname = video['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeVideoBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/videos';

    try {
      final file = await Supabase.instance.client.storage.from(collegeVideoBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeVideoBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        video['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        video['_isImageLoading'] = false;
      });
    } else {
      video['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> video) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoDetailPage(
            video: video,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  String _formatDate(Map<String, dynamic> video) {
    final day = video['day'] as int?;
    final month = video['month'] as int?;
    final year = video['year'] as int?;
    
    if (day == null || month == null || year == null) {
      return '';
    }
    
    try {
      final date = DateTime(year, month, day);
      return DateFormat('MMM d, yyyy').format(date);
    } catch (e) {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    print("VideosPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Videos',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadVideosFromSupabase(initialLoad: true);
              },
              child: _videos.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No videos available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _videos.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _videos.length) {
                          final video = _videos[index];
                          return VisibilityDetector(
                            key: Key('video_${video['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (video['image_url'] == null ||
                                      video['image_url'] == 'assets/placeholder_image.png') &&
                                  !video['_isImageLoading']) {
                                _fetchImageUrl(video);
                              }
                            },
                            child: _buildVideoCard(video, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVideoCard(
    Map<String, dynamic> video,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = video['fullname'] ?? 'Unknown';
    final String platform = video['platform'] ?? '';
    final String dateStr = _formatDate(video);
    final String imageUrl = video['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, video),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.videocam,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.videocam,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.videocam,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (dateStr.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          dateStr,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (platform.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          platform,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                           maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}