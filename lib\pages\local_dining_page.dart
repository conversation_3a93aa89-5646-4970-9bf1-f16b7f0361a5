import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'local_dining_detail_page.dart';
import 'login_page.dart';

class LocalDiningPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedLocalDining;
  final bool isFromDetailPage;

  const LocalDiningPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedLocalDining,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _LocalDiningPageState createState() => _LocalDiningPageState();
}

class _LocalDiningPageState extends State<LocalDiningPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('local_dining_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _localDining = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("LocalDiningPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant LocalDiningPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("LocalDiningPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("LocalDiningPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedLocalDining != null && widget.preloadedLocalDining!.isNotEmpty) {
      print("Preloaded local dining found, using them.");
      setState(() {
        _localDining = List<Map<String, dynamic>>.from(widget.preloadedLocalDining!);
        _localDining.forEach((dining) {
          dining['_isImageLoading'] = false;
        });
        _localDining.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedLocalDining!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      // Don't load more from database if we have preloaded data
      // But check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded local dining or empty list, loading from database.");
      await _loadLocalDiningFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final localDiningTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_localareadining';
    
    try {
      final response = await Supabase.instance.client
          .from(localDiningTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_localDining.length, _localDining.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadLocalDiningFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadLocalDiningFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final localDiningTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_localareadining';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _localDining.length;
        endRange = _localDining.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(localDiningTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedLocalDining =
          await _updateLocalDiningImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _localDining = updatedLocalDining;
          } else {
            _localDining.addAll(updatedLocalDining);
          }
          _localDining.forEach((dining) {
            dining['_isImageLoading'] = false;
          });
          _localDining.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching local dining: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateLocalDiningImageUrls(
      List<Map<String, dynamic>> localDining) async {
    List<Future<void>> futures = [];
    for (final dining in localDining) {
      if (dining['img_link'] == null || dining['img_link'] == '') {
        futures.add(_fetchImageUrl(dining));
      }
    }
    await Future.wait(futures);
    return localDining;
  }

  void _setupRealtime() {
    final localDiningTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_localareadining';
    _realtimeChannel = Supabase.instance.client
        .channel('local_dining_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: localDiningTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newDiningId = payload.newRecord['id'];
          final newDiningResponse = await Supabase.instance.client
              .from(localDiningTableName)
              .select('*')
              .eq('id', newDiningId)
              .single();
          if (mounted) {
            Map<String, dynamic> newDining = Map.from(newDiningResponse);
            final updatedDining = await _updateLocalDiningImageUrls([newDining]);
            setState(() {
              _localDining.add(updatedDining.first);
              updatedDining.first['_isImageLoading'] = false;
              _localDining.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedDiningId = payload.newRecord['id'];
          final updatedDiningResponse = await Supabase.instance.client
              .from(localDiningTableName)
              .select('*')
              .eq('id', updatedDiningId)
              .single();
          if (mounted) {
            final updatedDining = Map<String, dynamic>.from(updatedDiningResponse);
            setState(() {
              _localDining = _localDining.map((dining) {
                return dining['id'] == updatedDining['id'] ? updatedDining : dining;
              }).toList();
              _localDining.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedDiningId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _localDining.removeWhere((dining) => dining['id'] == deletedDiningId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("LocalDiningPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreLocalDining();
    }
  }

  Future<void> _loadMoreLocalDining() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more local dining...");
      await _loadLocalDiningFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> dining) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => LocalDiningDetailPage(
            localDining: dining,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("LocalDiningPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Local Dining',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadLocalDiningFromSupabase(initialLoad: true);
              },
              child: _localDining.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No local dining options available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _localDining.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _localDining.length) {
                          final dining = _localDining[index];
                          return VisibilityDetector(
                            key: Key('dining_${dining['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (dining['img_link'] == null || dining['img_link'] == '') &&
                                  !dining['_isImageLoading']) {
                                _fetchImageUrl(dining);
                              }
                            },
                            child: _buildLocalDiningCard(dining, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> dining) async {
    if (dining['_isImageLoading'] == true) {
      print('Image loading already in progress for ${dining['fullname']}, skipping.');
      return;
    }
    if (dining['img_link'] != null && dining['img_link'] != '') {
      print('Image URL already set for ${dining['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      dining['_isImageLoading'] = true;
    });

    final fullname = dining['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeLocalDiningBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/localareadining';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeLocalDiningBucket');
    print('Image URL before fetch: ${dining['img_link']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeLocalDiningBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeLocalDiningBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        dining['img_link'] = imageUrl.isNotEmpty ? imageUrl : '';
        dining['_isImageLoading'] = false;
        print('Setting img_link for ${dining['fullname']} to: ${dining['img_link']}');
      });
    } else {
      dining['_isImageLoading'] = false;
    }
  }

  Widget _buildLocalDiningCard(
    Map<String, dynamic> dining,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = dining['fullname'] ?? 'Unknown';
    final String hours = dining['hours'] ?? '';
    final String payment = dining['payment'] ?? '';
    final String imgLink = dining['img_link'] ?? '';
    final String about = dining['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, dining),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imgLink.isNotEmpty
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imgLink,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.local_dining,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.local_dining,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.local_dining,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          hours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (payment.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Payment: $payment',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}