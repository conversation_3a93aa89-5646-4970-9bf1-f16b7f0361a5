// payment_keydates_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';

class PaymentKeyDatesPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const PaymentKeyDatesPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  _PaymentKeyDatesPageState createState() => _PaymentKeyDatesPageState();
}

class _PaymentKeyDatesPageState extends State<PaymentKeyDatesPage> 
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('payment_keydates_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _keyDates = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  final List<String> _monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  @override
  void initState() {
    super.initState();
    print("PaymentKeyDatesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant PaymentKeyDatesPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("PaymentKeyDatesPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("PaymentKeyDatesPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    await _loadKeyDatesFromCache();
    await _loadKeyDatesFromSupabase(initialLoad: true);
  }

  void _setupRealtime() {
    final eventsTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_events';
    print("Setting up realtime for table: $eventsTableName");
    _realtimeChannel = Supabase.instance.client
        .channel('payment_keydates')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: eventsTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        print("Realtime update received for payment key dates: ${payload.eventType}");
        
        // Check if the event is a payment key date
        final bool isPaymentKeyDate = payload.newRecord['paymentkeydate'] == true || 
                                     payload.newRecord['paymentkeydate'] == 'TRUE' ||
                                     payload.newRecord['paymentkeydate'] == 'true';
        
        if (payload.eventType == PostgresChangeEvent.insert && isPaymentKeyDate) {
          final newEventId = payload.newRecord['id'];
          try {
            final newEventResponse = await Supabase.instance.client
                .from(eventsTableName)
                .select('*')
                .eq('id', newEventId)
                .or('paymentkeydate.eq.true,paymentkeydate.eq.TRUE')
                .single();
            if (mounted) {
              Map<String, dynamic> newEvent = Map.from(newEventResponse);
              setState(() {
                _keyDates.add(newEvent);
                _sortKeyDates();
              });
            }
          } catch (e) {
            print("Error fetching new payment key date: $e");
          }
        } else if (payload.eventType == PostgresChangeEvent.update && isPaymentKeyDate) {
          final updatedEventId = payload.newRecord['id'];
          try {
            final updatedEventResponse = await Supabase.instance.client
                .from(eventsTableName)
                .select('*')
                .eq('id', updatedEventId)
                .or('paymentkeydate.eq.true,paymentkeydate.eq.TRUE')
                .single();
            if (mounted) {
              final updatedEvent = Map<String, dynamic>.from(updatedEventResponse);
              setState(() {
                _keyDates = _keyDates.map((event) {
                  return event['id'] == updatedEvent['id'] ? updatedEvent : event;
                }).toList();
                _sortKeyDates();
              });
            }
          } catch (e) {
            print("Error fetching updated payment key date: $e");
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedEventId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _keyDates.removeWhere((event) => event['id'] == deletedEventId);
            });
          }
        }
      },
    ).subscribe();
  }

  void _sortKeyDates() {
    _keyDates.sort((a, b) {
      final int yearA = a['year'] ?? 0;
      final int yearB = b['year'] ?? 0;
      final int monthA = a['month'] ?? 0;
      final int monthB = b['month'] ?? 0;
      final int dayA = a['day'] ?? 0;
      final int dayB = b['day'] ?? 0;
      
      if (yearA != yearB) return yearA.compareTo(yearB);
      if (monthA != monthB) return monthA.compareTo(monthB);
      return dayA.compareTo(dayB);
    });
  }

  Future<void> _loadKeyDatesFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '');
      final cacheKey = 'payment_keydates_${collegeNameForTable}';
      final cachedData = prefs.getString(cacheKey);
      
      print("Loading from cache with key: $cacheKey");

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> keyDates = decodedData.cast<Map<String, dynamic>>();
        
        if (mounted && keyDates.isNotEmpty) {
          setState(() {
            _keyDates = keyDates;
            _sortKeyDates();
          });
          print("Loaded ${keyDates.length} payment key dates from cache");
        }
      } else {
        print("No cached data found for key: $cacheKey");
      }
    } catch (e) {
      print("Error loading payment key dates from cache: $e");
    }
  }

  Future<void> _loadKeyDatesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadKeyDatesFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final eventsTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_events';
    print("Querying table: $eventsTableName for payment key dates");

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _keyDates.length;
        endRange = _keyDates.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange from table: $eventsTableName");

      // Query with OR condition to handle both boolean true and string 'TRUE'
      final response = await Supabase.instance.client
          .from(eventsTableName)
          .select('*')
          .or('paymentkeydate.eq.true,paymentkeydate.eq.TRUE')
          .order('year', ascending: true)
          .order('month', ascending: true)
          .order('day', ascending: true)
          .range(startRange, endRange);

      print("Query response received: ${response.length} records");

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _keyDates = List<Map<String, dynamic>>.from(response);
          } else {
            _keyDates.addAll(List<Map<String, dynamic>>.from(response));
          }
          _sortKeyDates();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });

        print("Updated _keyDates with ${_keyDates.length} total records");
        
        // Cache the data
        _cacheKeyDates(_keyDates);
      }
    } catch (error) {
      print("Error in _loadKeyDatesFromSupabase: $error");
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Events table for this institution hasn't been created yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching payment key dates: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<void> _cacheKeyDates(List<Map<String, dynamic>> events) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '');
      final cacheKey = 'payment_keydates_${collegeNameForTable}';
      await prefs.setString(cacheKey, jsonEncode(events));
      print("Cached ${events.length} payment key dates with key: $cacheKey");
    } catch (e) {
      print("Error caching payment key dates: $e");
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("PaymentKeyDatesPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreKeyDates();
    }
  }

  Future<void> _loadMoreKeyDates() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more key dates...");
      await _loadKeyDatesFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  String _formatDate(Map<String, dynamic> event) {
    final int? day = event['day'];
    final int? month = event['month'];
    final int? year = event['year'];
    
    if (day != null && month != null && year != null) {
      final DateTime date = DateTime(year, month, day);
      return DateFormat('MMMM d, y').format(date);
    } else if (month != null && year != null) {
      return '${_monthNames[month - 1]} $year';
    } else if (year != null) {
      return year.toString();
    }
    return 'Date not specified';
  }

  String _formatTime(Map<String, dynamic> event) {
    final String startTime = event['starttime'] ?? '';
    final String endTime = event['endtime'] ?? '';
    
    if (startTime.isNotEmpty && endTime.isNotEmpty) {
      return '$startTime - $endTime';
    } else if (startTime.isNotEmpty) {
      return 'Starts at $startTime';
    } else if (endTime.isNotEmpty) {
      return 'Ends at $endTime';
    }
    return '';
  }

  bool _isUpcoming(Map<String, dynamic> event) {
    final int? day = event['day'];
    final int? month = event['month'];
    final int? year = event['year'];
    
    if (day != null && month != null && year != null) {
      final DateTime eventDate = DateTime(year, month, day);
      final DateTime now = DateTime.now();
      return eventDate.isAfter(now) || 
             (eventDate.year == now.year && eventDate.month == now.month && eventDate.day == now.day);
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    print("PaymentKeyDatesPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Payment Key Dates',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadKeyDatesFromSupabase(initialLoad: true);
              },
              child: _keyDates.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No payment key dates available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _keyDates.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _keyDates.length) {
                          final keyDate = _keyDates[index];
                          return VisibilityDetector(
                            key: Key('keydate_${keyDate['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              // This is kept for consistency with helpdesks page structure
                              // but payment key dates don't have images to load
                            },
                            child: _buildKeyDateCard(keyDate, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildKeyDateCard(
    Map<String, dynamic> event,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = event['fullname'] ?? 'Unnamed Event';
    final String about = event['about'] ?? '';
    final String venue = event['venue'] ?? '';
    final String formattedDate = _formatDate(event);
    final String formattedTime = _formatTime(event);
    final bool isUpcoming = _isUpcoming(event);

    String locationText = '';
    if (venue.isNotEmpty) {
      locationText = venue;
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          // Future: Navigate to detail page if needed
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isUpcoming 
                    ? theme.colorScheme.primary.withOpacity(0.1)
                    : theme.colorScheme.surfaceVariant,
                child: Icon(
                  Icons.attach_money,
                  color: isUpcoming 
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        formattedDate,
                        style: TextStyle(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    if (formattedTime.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 2),
                        child: Text(
                          formattedTime,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 2),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}