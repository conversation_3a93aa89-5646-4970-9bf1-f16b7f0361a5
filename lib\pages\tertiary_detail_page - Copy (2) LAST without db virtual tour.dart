import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_map_cancellable_tile_provider/flutter_map_cancellable_tile_provider.dart';
import 'package:geolocator/geolocator.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:panorama/panorama.dart';
import 'package:flutter_tts/flutter_tts.dart';

import 'tertiary_info_page.dart';
import 'tertiary_start_page.dart';
import 'tertiary_updates_page.dart';
import 'tertiary_path_page.dart';
import 'tertiary_people_page.dart';
import 'tertiary_lodging_page.dart';
import 'tertiary_shop_eat_page.dart';
import 'tertiary_transport_page.dart';
import 'tertiary_core_page.dart';
import 'tertiary_academics_page.dart';
import 'tertiary_programs_page.dart';
import 'tertiary_athletics_groups_page.dart';
import 'tertiary_media_page.dart';
import 'tertiary_startups_page.dart';
import 'tertiary_projects_publications_page.dart';
import 'tertiary_buildings_spaces_page.dart';
import 'tertiary_calendar_page.dart';
import 'building_detail_page.dart';
import 'tertiary_feedback_page.dart';
import 'tertiary_timeline_page.dart';
import 'tertiary_connectivity_page.dart';
import 'tertiary_giving_page.dart';
import 'tertiary_today_page.dart';
import '../main.dart';
import 'tertiary_rentals_page.dart';
import 'tertiary_jobs_page.dart';
import 'tertiary_services_page.dart';
import 'tertiary_money_page.dart';
import 'tertiary_health_page.dart';
import 'ai_agent_page.dart';
import 'event_detail_page.dart';
import '../services/ai_cache_warmer.dart'; 
import '../services/knowledge_base_indexer.dart'; // --- NEW: IMPORT THE INDEXER ---

// Helper class for the unified calendar view
class _UnifiedCalendarItem {
  final String type; // 'event', 'academic', 'weekly', 'class'
  final String title;
  final String timeString;
  final String location;
  final Map<String, dynamic> sourceData;

  _UnifiedCalendarItem({
    required this.type,
    required this.title,
    required this.timeString,
    required this.location,
    required this.sourceData,
  });
}

class TertiaryDetailPage extends StatefulWidget {
  final Map<String, dynamic> college;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool hasTodayEventsPreloaded;
  final bool isFromTertiaryPage;

  const TertiaryDetailPage({
    Key? key,
    required this.college,
    required this.isDarkMode,
    required this.toggleTheme,
    this.hasTodayEventsPreloaded = false,
    this.isFromTertiaryPage = false,
  }) : super(key: key);

  @override
  State<TertiaryDetailPage> createState() => _TertiaryDetailPageState();
}

class _TertiaryDetailPageState extends State<TertiaryDetailPage>
    with TickerProviderStateMixin {
  // --- Ad and General State ---
  RewardedAd? _rewardedAd;
  bool _isAdLoaded = false;
  final String _adUnitId = 'ca-app-pub-3940256099942544/**********';
  late TabController _tabController;
  final List<Widget> _tabs = [];
  bool _isLoadingTabs = true;
  bool _hasLocationData = false;
  bool _hasVirtualTour = false;
  bool _hasAcademicData = false;

  // --- Events Tab State ---
  late bool _hasTodayEvents;
  List<Map<String, dynamic>>? _preloadedHelpdesks;
  List<Map<String, dynamic>>? _preloadedTodayEvents;
  final List<RealtimeChannel> _realtimeChannels = [];
  late CalendarFormat _calendarFormat;
  late DateTime _focusedDay;
  late DateTime _selectedDay;
  late Map<DateTime, List<_UnifiedCalendarItem>> _calendarData;
  late List<_UnifiedCalendarItem> _selectedItems;
  bool _isCalendarLoading = true;

  // --- Academic Calendar Tab State ---
  List<Map<String, dynamic>> _academicCalendarItems = [];

  // --- Map Tab State ---
  final TextEditingController _mapSearchController = TextEditingController();
  final MapController _mapController = MapController();
  final FocusNode _mapSearchFocusNode = FocusNode();
  String _mapSelectedFilter = 'All';
  bool _mapIsSatelliteView = false;
  bool _mapIsLoading = true;
  List<Map<String, dynamic>> _mapBuildings = [];
  List<Map<String, dynamic>> _mapHousing = [];
  List<Map<String, dynamic>> _mapAllLocations = [];
  List<Map<String, dynamic>> _mapFilteredLocations = [];
  List<Map<String, dynamic>> _mapLocationsWithCoordinates = [];
  List<Map<String, dynamic>> _mapSearchSuggestions = [];
  bool _mapShowSuggestions = false;
  Map<String, dynamic>? _mapSelectedLocation;
  LatLng _mapCenter = LatLng(-13.9626, 33.7741); // Default center
  LatLng? _mapUserLocation;
  bool _mapIsLoadingLocation = false;
  late final RealtimeChannel _mapBuildingsChannel;
  late final RealtimeChannel _mapHousingChannel;
  bool _mapInitialized = false;
  List<LatLng> _mapPathPoints = [];
  bool _shouldShowMapFilters = false;
  final List<String> _mapFilters = [
    'All', 'Academic Buildings', 'Residences', 'Dining', 'Athletics',
    'Parking', 'Libraries', 'Study Spaces', 'Housing',
  ];

  // --- Virtual Tour State ---
  int _currentSceneIndex = 0;
  bool _isMuted = false;
  final FlutterTts _flutterTts = FlutterTts();
  bool _isTtsPlaying = false;
  Key _panoramaKey = UniqueKey();
  bool _isPanoramaVisible = true;
  // TODO: Fetch this data from Supabase for each college
  final List<Map<String, String>> _scenes = [
    {
      "title": "Campus Entrance",
      "imageUrl": "https://i.imgur.com/ggt9dUI.jpeg",
      "description": "Welcome to the grand Campus Entrance! This is the main gateway to our beautiful and vibrant campus.",
    },
    {
      "title": "Library Plaza",
      "imageUrl": "https://i.imgur.com/XDJXvwJ.jpeg",
      "description": "This is the Library Plaza, a central hub for students. A perfect place to study, relax, or meet with friends.",
    },
    {
      "title": "Science Building",
      "imageUrl": "https://i.imgur.com/CnCMZLm.jpeg",
      "description": "Step inside the impressive Science Building, home to our state-of-the-art labs and research facilities.",
    },
    {
      "title": "Student Union",
      "imageUrl": "https://i.imgur.com/lAHTGib.jpeg",
      "description": "Welcome to the bustling Student Union, the heart of campus life. You'll find dining, event spaces, and student services here.",
    },
  ];


  @override
  void initState() {
    super.initState();
    _initializePage();
  }
  
  Future<void> _initializePage() async {
    // --- UPDATED: THIS IS THE FIX ---
    // Trigger the indexing process in the background. We don't await it
    // so the UI loads immediately. This ensures any new documents are
    // processed and made available to the AI.
    KnowledgeBaseIndexer.startIndexingIfNeeded(context, widget.college);
    // --- END OF UPDATE ---

    final lat = widget.college['latitude'];
    final lng = widget.college['longitude'];
    _hasLocationData = lat != null && lng != null && (lat != 0.0 || lng != 0.0);
    _hasVirtualTour = _scenes.isNotEmpty;
    _hasAcademicData = await _checkForAcademicData();

    _setupTabs();
    _initializeTts();
    
    if(mounted) {
      setState(() {
        _isLoadingTabs = false;
      });
    }

    _loadRewardedAd();
    _prefetchInfoPageData();
    _preloadAndCacheHelpdesks();

    _hasTodayEvents = widget.hasTodayEventsPreloaded;
    _loadPreloadTodayEvents();
    _setupAllRealtimeListeners();
    _calendarFormat = CalendarFormat.week;
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
    _calendarData = {};
    _selectedItems = [];
    _fetchAllCalendarData();

    if (_hasLocationData) {
      _loadMapData();
      _setupMapRealtimeListeners();
      _getUserLocation();
    }
  }

  @override
  void dispose() {
    // Invalidate the AI cache for this specific college when the user leaves this page.
    // This ensures that the next time they visit, they get fresh data.
    if (widget.college['id'] != null) {
      AICacheWarmer.invalidateCacheForCollege(widget.college['id'].toString());
    }

    _rewardedAd?.dispose();
    for (var channel in _realtimeChannels) {
      channel.unsubscribe();
    }
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _mapSearchController.dispose();
    _mapSearchFocusNode.dispose();
    _flutterTts.stop();
    
    if (_hasLocationData) {
        _mapBuildingsChannel.unsubscribe();
        _mapHousingChannel.unsubscribe();
    }
    super.dispose();
  }

  Future<void> _initializeTts() async {
    await _flutterTts.awaitSpeakCompletion(true);
    _flutterTts.setStartHandler(() {
        if(mounted) setState(() { _isTtsPlaying = true; });
    });
    _flutterTts.setCompletionHandler(() {
        if(mounted) setState(() { _isTtsPlaying = false; });
    });
    _flutterTts.setErrorHandler((msg) {
        if(mounted) setState(() { _isTtsPlaying = false; });
        print("TTS Error: $msg");
    });

    _tabController.addListener(_handleTabChange);
  }

  void _handleTabChange() {
    final virtualTourTabIndex = _getVirtualTourTabIndex();
    if (virtualTourTabIndex == -1) return;

    if (_tabController.indexIsChanging) {
      _flutterTts.stop();
      return;
    }
    
    if (_tabController.index == virtualTourTabIndex && !_tabController.indexIsChanging) {
      _refreshPanorama();
      Future.delayed(const Duration(milliseconds: 300), _speakSceneDescription);
    }
  }

  int _getVirtualTourTabIndex() {
    int index = 0;
    index++; 
    if (_hasAcademicData) index++;
    if (_hasLocationData) index++;
    if (_hasVirtualTour) {
        return index;
    }
    return -1;
  }
  
  Future<bool> _checkForAcademicData() async {
    try {
      final tableName = '${widget.college['fullname'].toLowerCase().replaceAll(' ', '')}_academiccalendar';
      final response = await Supabase.instance.client.from(tableName).select('id').limit(1);
      return response.isNotEmpty;
    } catch (e) {
      print('Could not check for academic data, assuming none. Error: $e');
      return false;
    }
  }

  void _setupTabs() {
    _tabs.clear();
    _tabs.add(const Tab(icon: Icon(Icons.access_time)));
    
    if (_hasAcademicData) {
      _tabs.add(const Tab(icon: Icon(Icons.timelapse)));
    }

    if (_hasLocationData) {
      _tabs.add(const Tab(icon: Icon(Icons.map_outlined)));
    }
    
    if (_hasVirtualTour) {
      _tabs.add(const Tab(icon: Icon(Icons.threesixty)));
    }

    _tabs.add(const Tab(icon: Icon(Icons.apps)));

    _tabController = TabController(length: _tabs.length, vsync: this);
  }
  
  //<editor-fold desc="Data Fetching and State Management (Unchanged)">
  Future<void> _fetchAllCalendarData() async {
    if (!mounted) return;
    setState(() { _isCalendarLoading = true; });

    final institutionName = widget.college['fullname'].toLowerCase().replaceAll(' ', '');

    try {
      final List<Future<List<Map<String, dynamic>>>> futures = [
        _fetchTableData(institutionName, 'events'),
      ];
      if (_hasAcademicData) {
        futures.add(_fetchTableData(institutionName, 'academiccalendar'));
      }
      futures.add(_fetchTableData(institutionName, 'weeklyschedule'));
      futures.add(_fetchTableData(institutionName, 'classschedules'));

      final results = await Future.wait(futures);

      int currentIndex = 0;
      final events = results[currentIndex++];
      final academicCalendarRaw = _hasAcademicData ? results[currentIndex++] : <Map<String, dynamic>>[];
      final weeklySchedules = results[currentIndex++];
      final classSchedules = results[currentIndex++];


      final Map<DateTime, List<_UnifiedCalendarItem>> newCalendarData = {};
      _processItems(newCalendarData, events, 'event');
      _processItems(newCalendarData, weeklySchedules, 'weekly');
      _processItems(newCalendarData, classSchedules, 'class');
      
      final sortedAcademicItems = _sortAcademicItems(academicCalendarRaw);

      if (mounted) {
        setState(() {
          _calendarData = newCalendarData;
          _academicCalendarItems = sortedAcademicItems;
          _selectedItems = _getItemsForDay(_selectedDay);
          _isCalendarLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching comprehensive calendar data: $e');
      if (mounted) {
        setState(() {
          _calendarData = {};
          _academicCalendarItems = [];
          _selectedItems = [];
          _isCalendarLoading = false;
        });
      }
    }
  }

  List<Map<String, dynamic>> _sortAcademicItems(List<Map<String, dynamic>> items) {
    items.sort((a, b) {
      try {
        final aDate = DateTime(
          int.parse(a['startyear'].toString()),
          int.parse(a['startmonth'].toString()),
          int.parse(a['startday'].toString())
        );
        final bDate = DateTime(
          int.parse(b['startyear'].toString()),
          int.parse(b['startmonth'].toString()),
          int.parse(b['startday'].toString())
        );
        return aDate.compareTo(bDate);
      } catch (e) {
        return 0;
      }
    });
    return items;
  }
  
  Future<List<Map<String, dynamic>>> _fetchTableData(String institutionName, String type) async {
    final tableName = '${institutionName}_$type';
    try {
      final response = await Supabase.instance.client.from(tableName).select('*');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('Info: Could not fetch data from table $tableName. It might not exist. Error: $e');
      return [];
    }
  }

  void _processItems(Map<DateTime, List<_UnifiedCalendarItem>> dataMap, List<Map<String, dynamic>> items, String type) {
    for (var item in items) {
      try {
        final startDay = int.tryParse(item['startday']?.toString() ?? '');
        final startMonth = int.tryParse(item['startmonth']?.toString() ?? '');
        final startYear = int.tryParse(item['startyear']?.toString() ?? '');

        if (startDay == null || startMonth == null || startYear == null) continue;

        final startDate = DateTime(startYear, startMonth, startDay);
        
        final endDay = int.tryParse(item['endday']?.toString() ?? '');
        final endMonth = int.tryParse(item['endmonth']?.toString() ?? '');
        final endYear = int.tryParse(item['endyear']?.toString() ?? '');

        final endDate = (endDay != null && endMonth != null && endYear != null)
            ? DateTime(endYear, endMonth, endDay)
            : startDate;

        DateTime currentDate = startDate;
        while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
          final dayKey = DateTime.utc(currentDate.year, currentDate.month, currentDate.day);

          bool shouldAdd = true;
          if (type == 'weekly' || type == 'class' || type == 'event') {
             if (item['_mon'] != null || item['_tue'] != null || item['_wed'] != null || item['_thur'] != null || item['_fri'] != null || item['_sat'] != null || item['_sun'] != null) {
                shouldAdd = false;
                switch (currentDate.weekday) {
                  case DateTime.monday: shouldAdd = item['_mon'] == true; break;
                  case DateTime.tuesday: shouldAdd = item['_tue'] == true; break;
                  case DateTime.wednesday: shouldAdd = item['_wed'] == true; break;
                  case DateTime.thursday: shouldAdd = item['_thur'] == true; break;
                  case DateTime.friday: shouldAdd = item['_fri'] == true; break;
                  case DateTime.saturday: shouldAdd = item['_sat'] == true; break;
                  case DateTime.sunday: shouldAdd = item['_sun'] == true; break;
                }
             }
          }

          if (shouldAdd) {
            if (dataMap[dayKey] == null) dataMap[dayKey] = [];
            dataMap[dayKey]!.add(_createUnifiedItem(item, type));
          }
          currentDate = currentDate.add(const Duration(days: 1));
        }
      } catch (e) {
        print("Could not process item of type '$type': ${item['fullname']}. Error: $e");
      }
    }
  }
  
  _UnifiedCalendarItem _createUnifiedItem(Map<String, dynamic> item, String type) {
      String startTime = item['starttime'] ?? '';
      String endTime = item['endtime'] ?? '';
      String timeText = '';
      if (startTime.isNotEmpty && endTime.isNotEmpty) {
        timeText = '$startTime - $endTime';
      } else if (startTime.isNotEmpty) {
        timeText = 'Starts at $startTime';
      } else if (endTime.isNotEmpty) {
        timeText = 'Ends at $endTime';
      }
      
      String locationText = item['location'] ?? item['building'] ?? '';
      if(item['room'] != null && item['room'].isNotEmpty) {
        locationText += ', Room ${item['room']}';
      }

      return _UnifiedCalendarItem(
          type: type,
          title: item['fullname'] ?? 'Untitled',
          timeString: timeText,
          location: locationText,
          sourceData: item,
      );
  }

  void _setupAllRealtimeListeners() {
    final institutionName = widget.college['fullname'].toLowerCase().replaceAll(' ', '');
    final tableTypes = ['events', 'academiccalendar', 'weeklyschedule', 'classschedules'];
    
    for (var type in tableTypes) {
      final tableName = '${institutionName}_$type';
      try {
        final channel = Supabase.instance.client.realtime
          .channel('public:$tableName')
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: tableName,
            callback: (payload) {
              print('Realtime update for $tableName: ${payload.eventType}');
              if (mounted) {
                _fetchAllCalendarData();
              }
            },
          ).subscribe();
        _realtimeChannels.add(channel);
      } catch (e) {
        print("Could not subscribe to realtime changes for $tableName. It may not exist. Error: $e");
      }
    }
  }

  List<_UnifiedCalendarItem> _getItemsForDay(DateTime day) {
    final normalizedDay = DateTime.utc(day.year, day.month, day.day);
    final items = _calendarData[normalizedDay] ?? [];
    items.sort((a, b) {
      final startTimeA = a.sourceData['starttime'] as String? ?? '23:59';
      final startTimeB = b.sourceData['starttime'] as String? ?? '23:59';
      return startTimeA.compareTo(startTimeB);
    });
    return items;
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (!isSameDay(_selectedDay, selectedDay)) {
      setState(() {
        _selectedDay = selectedDay;
        _focusedDay = focusedDay;
        _selectedItems = _getItemsForDay(selectedDay);
      });
    }
  }
  //</editor-fold>
  
  //<editor-fold desc="Ad, Navigation, Map, etc (Unchanged)">
  Future<void> _prefetchInfoPageData() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('about_${widget.college['id']}');
    await prefs.remove('vision_${widget.college['id']}');
    await prefs.remove('mission_${widget.college['id']}');
    await prefs.remove('address_${widget.college['id']}');
    await prefs.remove('daysnhours_${widget.college['id']}');
    await prefs.remove('postaladdress_${widget.college['id']}');
    await prefs.remove('corevalues_${widget.college['id']}');
    await prefs.remove('motto_${widget.college['id']}');
    await prefs.remove('goals_${widget.college['id']}');
    await prefs.remove('mandate_${widget.college['id']}');
    await prefs.remove('founded_${widget.college['id']}');
    await prefs.remove('accreditation_${widget.college['id']}');
    await prefs.remove('freewifi_${widget.college['id']}');
    await prefs.remove('objectives_${widget.college['id']}');
    await prefs.remove('aims_${widget.college['id']}');
    await prefs.remove('pledge_${widget.college['id']}');
    await prefs.remove('statementoffaith_${widget.college['id']}');
    await prefs.remove('religiousaffiliation_${widget.college['id']}');
    await prefs.remove('whychooseus_${widget.college['id']}');
    await prefs.remove('institutiontype_${widget.college['id']}');
    await prefs.remove('campussetting_${widget.college['id']}');
    await prefs.remove('highestqualificationoffered_${widget.college['id']}');
    await prefs.remove('studentpopulation_${widget.college['id']}');
    await prefs.remove('academicyearcalendar_${widget.college['id']}');
    await prefs.remove('website_${widget.college['id']}');

    Map<String, dynamic> updatedData =
        await fetchUpdatedCollegeData(widget.college['id']);

    await prefs.setString('about_${widget.college['id']}', updatedData['about'] ?? '');
    await prefs.setString(
        'vision_${widget.college['id']}', updatedData['vision'] ?? '');
    await prefs.setString(
        'mission_${widget.college['id']}', updatedData['mission'] ?? '');
    await prefs.setString(
        'address_${widget.college['id']}', updatedData['address'] ?? '');
    await prefs.setString(
        'daysnhours_${widget.college['id']}', updatedData['daysnhours'] ?? '');
    await prefs.setString(
        'postaladdress_${widget.college['id']}', updatedData['postaladdress'] ?? '');
    await prefs.setString(
        'corevalues_${widget.college['id']}', updatedData['corevalues'] ?? '');
    await prefs.setString('motto_${widget.college['id']}', updatedData['motto'] ?? '');
    await prefs.setString('goals_${widget.college['id']}', updatedData['goals'] ?? '');
    await prefs.setString(
        'mandate_${widget.college['id']}', updatedData['mandate'] ?? '');
    await prefs.setString(
        'founded_${widget.college['id']}', updatedData['founded'] ?? '');
    await prefs.setString(
        'accreditation_${widget.college['id']}', updatedData['accreditation'] ?? '');
    await prefs.setString(
        'freewifi_${widget.college['id']}', updatedData['freewifi']?.toString() ?? '');
    await prefs.setString(
        'objectives_${widget.college['id']}', updatedData['objectives'] ?? '');
    await prefs.setString('aims_${widget.college['id']}', updatedData['aims'] ?? '');
    await prefs.setString(
        'pledge_${widget.college['id']}', updatedData['pledge'] ?? '');
    await prefs.setString('statementoffaith_${widget.college['id']}',
        updatedData['statementoffaith'] ?? '');
    await prefs.setString('religiousaffiliation_${widget.college['id']}',
        updatedData['religiousaffiliation'] ?? '');
    await prefs.setString(
        'whychooseus_${widget.college['id']}', updatedData['whychooseus'] ?? '');
    await prefs.setString('institutiontype_${widget.college['id']}',
        updatedData['institutiontype'] ?? '');
    await prefs.setString(
        'campussetting_${widget.college['id']}', updatedData['campussetting'] ?? '');
    await prefs.setString(
        'highestqualificationoffered_${widget.college['id']}',
        updatedData['highestqualificationoffered'] ?? '');
    await prefs.setString('studentpopulation_${widget.college['id']}',
        updatedData['studentpopulation'] ?? '');
    await prefs.setString('academicyearcalendar_${widget.college['id']}',
        updatedData['academicyearcalendar'] ?? '');
    await prefs.setString(
        'website_${widget.college['id']}', updatedData['website'] ?? '');

    print("Data prefetched and cached for TertiaryInfoPage.");
  }

  Future<Map<String, dynamic>> fetchUpdatedCollegeData(int collegeId) async {
    await Future.delayed(Duration(seconds: 2));

    Map<String, dynamic> currentData = widget.college;

    return {
      'about': currentData['about'],
      'vision': currentData['vision'],
      'mission': currentData['mission'],
      'address': currentData['address'],
      'daysnhours': currentData['daysnhours'],
      'postaladdress': currentData['postaladdress'],
      'corevalues': currentData['corevalues'],
      'motto': currentData['motto'],
      'goals': currentData['goals'],
      'mandate': currentData['mandate'],
      'founded': currentData['founded'],
      'accreditation': currentData['accreditation'],
      'freewifi': currentData['freewifi'],
      'objectives': currentData['objectives'],
      'aims': currentData['aims'],
      'pledge': currentData['pledge'],
      'statementoffaith': currentData['statementoffaith'],
      'religiousaffiliation': currentData['religiousaffiliation'],
      'whychooseus': currentData['whychooseus'],
      'institutiontype': currentData['institutiontype'],
      'campussetting': currentData['campussetting'],
      'highestqualificationoffered': currentData['highestqualificationoffered'],
      'studentpopulation': currentData['studentpopulation'],
      'academicyearcalendar': currentData['academicyearcalendar'],
      'website': currentData['website'],
    };
  }

  Future<List<Map<String, dynamic>>> _preloadTodaysEvents() async {
    final now = DateTime.now();
    final todayDay = DateFormat('dd').format(now);
    final todayMonth = DateFormat('MM').format(now);
    final todayYear = DateFormat('yyyy').format(now);

    final eventsTableName = '${widget.college['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}_events';

    try {
      final response = await Supabase.instance.client
          .from(eventsTableName)
          .select('*')
          .eq('startday', todayDay)
          .eq('startmonth', todayMonth)
          .eq('startyear', todayYear)
          .order('starttime', ascending: true);

      if (response == null) {
        return [];
      }

      List<Map<String, dynamic>> eventsData = List<Map<String, dynamic>>
          .from(response as List<dynamic>);

      List<Map<String, dynamic>> filteredEvents = [];
      for (var event in eventsData) {
        if (!_isEventOver(event, now)) {
          filteredEvents.add(event);
        }
      }

      List<Future<void>> futures = [];
      for (final event in filteredEvents) {
        futures.add(_fetchEventImageUrlForPreload(event));
      }
      await Future.wait(futures);

      filteredEvents.sort((a, b) {
        final startTimeA = _parseTimeOfDayForPreload(a['starttime'] ?? '00:00') ?? TimeOfDay.now();
        final startTimeB = _parseTimeOfDayForPreload(b['starttime'] ?? '00:00') ?? TimeOfDay.now();
        if (startTimeA.hour != startTimeB.hour) {
          return startTimeA.hour.compareTo(startTimeB.hour);
        }
        return startTimeA.minute.compareTo(startTimeB.minute);
      });

      String collegeNameForEvents = widget.college['fullname'] ?? '';
      await _cacheTodayEvents(collegeNameForEvents, filteredEvents);

      return filteredEvents;
    } catch (error) {
      print('Error preloading today events: $error');
      return [];
    }
  }

  Future<void> _clearTodayEventsCache(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('today_events_${collegeName.toLowerCase().replaceAll(' ', '')}');
    print('Today\'s events cache cleared for $collegeName.');
  }

  Future<void> _cacheTodayEvents(String collegeName, List<Map<String, dynamic>> events) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String eventsJson = jsonEncode(events);
    await prefs.setString('today_events_${collegeName.toLowerCase().replaceAll(' ', '')}', eventsJson);
    print('Today\'s events cached for $collegeName.');
  }

  Future<List<Map<String, dynamic>>?> _getCachedTodayEvents(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? eventsJson = prefs.getString('today_events_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (eventsJson != null) {
      List<dynamic> decodedList = jsonDecode(eventsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  bool _isEventOver(Map<String, dynamic> event, DateTime now) {
    final eventStartTime = event['starttime'] as String? ?? '00:00';
    final eventEndTime = event['endtime'] as String? ?? '23:59';
    final eventDay = int.tryParse(event['startday'] as String? ?? '0') ?? 0;
    final eventMonthStr = event['startmonth'] as String? ?? '0';
    final eventYear = int.tryParse(event['startyear'] as String? ?? '0') ?? 0;

    int monthNumber = int.tryParse(eventMonthStr) ?? 0;

    if (eventDay == 0 || monthNumber == 0 || eventYear == 0) return false;

    final startDate = DateTime(eventYear, monthNumber, eventDay);

    TimeOfDay? endTimeOfDay;
    try {
      endTimeOfDay = _parseTimeOfDayForPreload(eventEndTime);
    } catch (e) {
      print("Error parsing endtime: $eventEndTime for event ${event['fullname']}");
      return false;
    }

    if (endTimeOfDay != null) {
      final eventEndDate = DateTime(startDate.year, startDate.month, startDate.day, endTimeOfDay.hour, endTimeOfDay.minute);
      print("Event End Date: $eventEndDate, Current Time: $now");
      return now.isAfter(eventEndDate.add(const Duration(minutes: 1)));
    }
    return false;
  }

  TimeOfDay? _parseTimeOfDayForPreload(String timeString) {
    try {
      DateTime parsedTime12H = DateFormat('h:mm a').parse(timeString.trim());
      return TimeOfDay.fromDateTime(parsedTime12H);
    } catch (e1) {
      try {
        DateTime parsedTime24H = DateFormat('HH:mm').parse(timeString.trim());
        return TimeOfDay.fromDateTime(parsedTime24H);
      } catch (e2) {
        print("Error parsing time string (preload): $timeString, errors: 12H: $e1, 24H: $e2");
        return null;
      }
    }
  }

  Future<void> _fetchEventImageUrlForPreload(Map<String, dynamic> event) async {
    if (event['_isImageLoading'] == true) {
      return;
    }
    if (event['image_url'] != null && event['image_url'] != 'assets/placeholder_image.png') {
      return;
    }
    event['_isImageLoading'] = true;

    final fullname = event['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeEventsBucket = '${widget.college['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}/events';

    try {
      final file = await Supabase.instance.client.storage.from(collegeEventsBucket)
          .download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeEventsBucket)
            .getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      // Handle error if needed
    }
    event['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
    event['_isImageLoading'] = false;
  }

  Future<bool> _checkHelpdesksAvailability(Map<String, dynamic> college) async {
    final helpdesksTableName = '${college['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}_helpdesks';

    try {
      final response = await Supabase.instance.client
          .from(helpdesksTableName)
          .select('id')
          .limit(1);

      return (response is List) && response.isNotEmpty;
    } catch (error) {
      print('Error checking helpdesks availability (pre-fetch): $error');
      return false;
    }
  }

  Future<bool> _checkLinksAvailability(Map<String, dynamic> college) async {
    final linksTableName = '${college['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}_links';
    try {
      final response = await Supabase.instance.client
          .from(linksTableName)
          .select('id')
          .limit(1);
      return (response is List) && response.isNotEmpty;
    } catch (error) {
      print('Error checking links availability (pre-fetch): $error');
      return false;
    }
  }

  Future<void> _loadPreloadTodayEvents() async {
    if (!_hasTodayEvents) return;
    List<Map<String, dynamic>> todayEvents = await _preloadTodaysEvents();
    setState(() {
      _preloadedTodayEvents = todayEvents;
    });
  }

  Future<void> _preloadAndCacheHelpdesks() async {
    String collegeNameForTable = widget.college['fullname'] ?? '';
    if (collegeNameForTable.isEmpty) return;

    await _clearHelpdeskCache(collegeNameForTable);
    List<Map<String, dynamic>> helpdesks = await _fetchHelpdesksFromDatabase(collegeNameForTable);
    await _cacheHelpdesks(collegeNameForTable, helpdesks);
    setState(() {
      _preloadedHelpdesks = helpdesks;
      print('Helpdesks preloaded and cached for ${widget.college['fullname']}.');
    });
  }

  Future<void> _clearHelpdeskCache(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('helpdesks_${collegeName.toLowerCase().replaceAll(' ', '')}');
    print('Helpdesk cache cleared for $collegeName.');
  }

  Future<List<Map<String, dynamic>>> _fetchHelpdesksFromDatabase(String collegeNameForTable) async {
    final helpdesksTableName = '${collegeNameForTable.toLowerCase().replaceAll(' ', '')}_helpdesks';
    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(helpdesksTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (response.isEmpty) {
        return [];
      }
      List<Map<String, dynamic>> updatedHelpdesks = await _updateHelpdeskImageUrls(response, collegeNameForTable);
      return updatedHelpdesks;
    } catch (error) {
      print('Error fetching helpdesks from database: $error');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> _updateHelpdeskImageUrls(List<Map<String, dynamic>> helpdesks, String collegeNameForTable) async {
    final collegeHelpdeskBucket = '${collegeNameForTable.toLowerCase().replaceAll(' ', '')}/helpdesks';
    for (final helpdesk in helpdesks) {
      final fullname = helpdesk['fullname'] as String? ?? '';
      final imageNamePng = '$fullname.png';
      final imageNameJpg = '$fullname.jpg';
      final imageNameWebp = '$fullname.webp';
      String imageUrl = 'assets/placeholder_image.png';

      try {
        await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNamePng);
        imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNamePng);
      } catch (e) {
        try {
          await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameJpg);
          imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameJpg);
        } catch (e) {
          try {
            await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameWebp);
            imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameWebp);
          } catch (e) {
             // Handle error if needed
          }
        }
      }
      helpdesk['image_url'] = imageUrl;
    }
    return helpdesks;
  }

  Future<void> _cacheHelpdesks(String collegeName, List<Map<String, dynamic>> helpdesks) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String helpdesksJson = jsonEncode(helpdesks);
    await prefs.setString('helpdesks_${collegeName.toLowerCase().replaceAll(' ', '')}', helpdesksJson);
    print('Helpdesks cached for $collegeName.');
  }

  void _loadRewardedAd() {
    RewardedAd.load(
      adUnitId: _adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          ad.fullScreenContentCallback = FullScreenContentCallback(
            onAdDismissedFullScreenContent: (AdAd) {
              setState(() {
                _rewardedAd = null;
                _isAdLoaded = false;
              });
              _loadRewardedAd();
              Navigator.pop(context);
            },
            onAdFailedToShowFullScreenContent: (AdAd, error) {
              setState(() {
                _rewardedAd = null;
                _isAdLoaded = false;
              });
              print('Failed to show rewarded ad: $error');
              _loadRewardedAd();
              Navigator.pop(context);
            },
          );
          setState(() {
            _rewardedAd = ad;
            _isAdLoaded = true;
          });
        },
        onAdFailedToLoad: (LoadAdError error) {
          print('RewardedAd failed to load: $error');
          setState(() {
            _rewardedAd = null;
            _isAdLoaded = false;
          });
        },
      ),
    );
  }

  void _showRewardedAd() {
    if (_rewardedAd != null && _isAdLoaded) {
      _rewardedAd!.show(
        onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
          print('User earned reward: ${reward.amount} ${reward.type}');
        },
      );
    } else {
      print('Rewarded ad not ready, navigating back directly.');
      Navigator.pop(context);
    }
  }

  Future<void> _launchDialer() async {
    final String phoneNumber = widget.college['phone'] ?? '';
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _launchWhatsapp() async {
    final String whatsappNumber = widget.college['whatsapp'] ?? '';
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _launchNavigation() async {
    final double latitude = widget.college['latitude'] ?? 0.0;
    final double longitude = widget.college['longitude'] ?? 0.0;
    final Uri url = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _getUserLocation() async {
    setState(() => _mapIsLoadingLocation = true);
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() => _mapIsLoadingLocation = false); return;
      }
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() => _mapIsLoadingLocation = false); return;
        }
      }
      if (permission == LocationPermission.deniedForever) {
        setState(() => _mapIsLoadingLocation = false); return;
      }
      Position position = await Geolocator.getCurrentPosition();
      if(mounted) {
        setState(() {
          _mapUserLocation = LatLng(position.latitude, position.longitude);
          _mapIsLoadingLocation = false;
        });
      }
    } catch (e) {
      print('Error getting location: $e');
      if(mounted) setState(() => _mapIsLoadingLocation = false);
    }
  }

  Future<void> _loadMapData() async {
    if (_mapIsLoading && _mapAllLocations.isNotEmpty) return;
    setState(() => _mapIsLoading = true);
    try {
      await Future.wait([
        _fetchBuildingsFromSupabase(),
        _fetchHousingFromSupabase()
      ]);
      _combineLocationData();
    } catch (e) {
      print('Error in _loadMapData: $e');
    } finally {
      if(mounted) setState(() => _mapIsLoading = false);
    }
  }

  void _analyzeMapCategories() {
    if (_mapAllLocations.isEmpty) {
      if (mounted) setState(() => _shouldShowMapFilters = false);
      return;
    }

    final categories = <String>{};
    for (final loc in _mapAllLocations) {
      if (loc['school'] == true || loc['lab'] == true) categories.add('Academic Buildings');
      if (loc['residence'] == true || loc['housing'] == true) categories.add('Residences');
      if (loc['dining'] == true) categories.add('Dining');
      if (loc['fitnessspace'] == true || loc['athleticspace'] == true) categories.add('Athletics');
      if (loc['parking'] == true) categories.add('Parking');
      if (loc['library'] == true) categories.add('Libraries');
      if (loc['studyspace'] == true) categories.add('Study Spaces');
      if (loc['location_type'] == 'housing') categories.add('Housing');
      if (categories.length > 1) break; // Optimization
    }

    if (mounted) {
      setState(() {
        _shouldShowMapFilters = categories.length > 1;
      });
    }
  }

  void _combineLocationData() {
    List<Map<String, dynamic>> allLocations = [..._mapBuildings, ..._mapHousing];
    final locationsWithCoords = allLocations.where((loc) =>
      (loc['latitude'] ?? 0.0) != 0.0 && (loc['longitude'] ?? 0.0) != 0.0
    ).toList();
    
    if (mounted) {
      setState(() {
        _mapAllLocations = allLocations;
        _mapFilteredLocations = allLocations;
        _mapLocationsWithCoordinates = locationsWithCoords;
      });

      if (!_mapInitialized) {
        _updateMapCenter();
        _mapInitialized = true;
      }
      
      _analyzeMapCategories();
    }
  }

  Future<void> _fetchBuildingsFromSupabase() async {
    try {
      final buildingsTableName = '${widget.college['fullname'].toLowerCase().replaceAll(' ', '')}_building';
      final response = await Supabase.instance.client.from(buildingsTableName).select('*');
      final buildings = List<Map<String, dynamic>>.from(response);
      for (var b in buildings) { b['location_type'] = 'building'; }
      if (mounted) setState(() => _mapBuildings = buildings);
    } catch (e) { print('Error fetching map buildings: $e'); }
  }

  Future<void> _fetchHousingFromSupabase() async {
    try {
      final housingTableName = '${widget.college['fullname'].toLowerCase().replaceAll(' ', '')}_housing';
      final response = await Supabase.instance.client.from(housingTableName).select('*');
      final housing = List<Map<String, dynamic>>.from(response);
      for (var h in housing) { h['location_type'] = 'housing'; }
      if (mounted) setState(() => _mapHousing = housing);
    } catch (e) { print('Error fetching map housing: $e'); }
  }

  void _setupMapRealtimeListeners() {
    final institutionName = widget.college['fullname'].toLowerCase().replaceAll(' ', '');
    final buildingsTableName = '${institutionName}_building';
    _mapBuildingsChannel = Supabase.instance.client
        .channel('buildings_map_realtime')
        .onPostgresChanges(event: PostgresChangeEvent.all, schema: 'public', table: buildingsTableName,
          callback: (payload) {
            print('Realtime update for map buildings');
            _fetchBuildingsFromSupabase().then((_) => _combineLocationData());
          },
        ).subscribe();

    final housingTableName = '${institutionName}_housing';
    _mapHousingChannel = Supabase.instance.client
        .channel('housing_map_realtime')
        .onPostgresChanges(event: PostgresChangeEvent.all, schema: 'public', table: housingTableName,
          callback: (payload) {
            print('Realtime update for map housing');
            _fetchHousingFromSupabase().then((_) => _combineLocationData());
          },
        ).subscribe();
  }

  void _updateMapCenter() {
    Map<String, dynamic>? referenceLocation;
    
    final referencePoints = _mapLocationsWithCoordinates
        .where((loc) => loc['mapreferencepoint'] == 'yes');

    if (referencePoints.isNotEmpty) {
      referenceLocation = referencePoints.first;
    } else if (_mapLocationsWithCoordinates.isNotEmpty) {
      referenceLocation = _mapLocationsWithCoordinates.first;
    } 

    LatLng? newCenter;

    if (referenceLocation != null) {
      final lat = referenceLocation['latitude'] ?? 0.0;
      final lng = referenceLocation['longitude'] ?? 0.0;
      if (lat != 0.0 && lng != 0.0) {
        newCenter = LatLng(lat, lng);
      }
    } else {
      final collegeLat = widget.college['latitude'];
      final collegeLng = widget.college['longitude'];
      if (collegeLat != null && collegeLng != null && (collegeLat != 0.0 || collegeLng != 0.0)) {
        newCenter = LatLng(collegeLat, collegeLng);
      }
    }

    if (newCenter != null) {
      if (mounted) {
        setState(() => _mapCenter = newCenter!);
      }
      try {
        final zoom = (referenceLocation != null) ? _mapController.camera.zoom : 14.0;
        _mapController.move(newCenter, zoom);
      } catch (e) {
        print("Could not move map on initial load: $e");
      }
    }
  }

  void _filterMapLocations(String filter) {
    setState(() {
      _mapSelectedFilter = filter;
      _mapFilteredLocations = (filter == 'All')
          ? List.from(_mapAllLocations)
          : _mapAllLocations.where((loc) {
              switch (filter) {
                case 'Academic Buildings': return loc['school'] == true || loc['lab'] == true;
                case 'Residences': return loc['residence'] == true || loc['housing'] == true;
                case 'Dining': return loc['dining'] == true;
                case 'Athletics': return loc['fitnessspace'] == true || loc['athleticspace'] == true;
                case 'Parking': return loc['parking'] == true;
                case 'Libraries': return loc['library'] == true;
                case 'Study Spaces': return loc['studyspace'] == true;
                case 'Housing': return loc['location_type'] == 'housing';
                default: return true;
              }
            }).toList();
    });
  }

  void _searchMapLocations(String query) {
    if (query.isEmpty) {
      setState(() {
        _mapShowSuggestions = false;
        _mapSearchSuggestions = [];
        _mapSelectedLocation = null;
      });
      _filterMapLocations(_mapSelectedFilter);
      return;
    }
    final lowercaseQuery = query.toLowerCase();
    final suggestions = _mapLocationsWithCoordinates.where((loc) =>
      (loc['fullname']?.toString().toLowerCase() ?? '').contains(lowercaseQuery) ||
      (loc['location']?.toString().toLowerCase() ?? '').contains(lowercaseQuery)
    ).toList();
    suggestions.sort((a,b) => (a['fullname'] ?? '').compareTo(b['fullname'] ?? ''));
    setState(() {
      _mapSearchSuggestions = suggestions;
      _mapShowSuggestions = true;
      _mapFilteredLocations = _mapAllLocations.where((loc) =>
        (loc['fullname']?.toString().toLowerCase() ?? '').contains(lowercaseQuery) ||
        (loc['location']?.toString().toLowerCase() ?? '').contains(lowercaseQuery)
      ).toList();
    });
  }

  void _selectMapLocation(Map<String, dynamic> location) {
    setState(() {
      _mapShowSuggestions = false;
      _mapSelectedLocation = location;
      _mapSearchController.text = location['fullname'] ?? '';
    });
    final lat = location['latitude'] ?? 0.0;
    final lng = location['longitude'] ?? 0.0;
    if (lat != 0.0 && lng != 0.0) {
      final target = LatLng(lat, lng);
      _mapController.move(target, 16.0);
      _mapSearchFocusNode.unfocus();
      if (_mapUserLocation != null) _drawPathToLocation(target);
    }
  }

  void _drawPathToLocation(LatLng destination) {
    if (_mapUserLocation == null) return;
    setState(() => _mapPathPoints = [_mapUserLocation!, destination]);
    final bounds = LatLngBounds.fromPoints(_mapPathPoints);
    _mapController.fitCamera(
      CameraFit.bounds(bounds: bounds, padding: const EdgeInsets.all(50.0))
    );
  }

  String get _mapUrlTemplate {
    if (_mapIsSatelliteView) {
      return 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';
    } else {
      return 'https://tile.openstreetmap.org/{z}/{x}/{y}.png';
    }
  }
  //</editor-fold>
  
  // --- Virtual Tour Methods ---
  Future<void> _speakSceneDescription() async {
    if (_isMuted || _scenes.isEmpty) return;
    String description = _scenes[_currentSceneIndex]['description'] ?? '';
    if (description.isNotEmpty) {
      await _flutterTts.speak(description);
    }
  }

  void _refreshPanorama() {
    _flutterTts.stop();
    setState(() {
      _isPanoramaVisible = false;
    });
    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) {
        setState(() {
          _panoramaKey = UniqueKey();
          _isPanoramaVisible = true;
        });
      }
    });
  }

  Widget _buildPanorama() {
    return Panorama(
      key: _panoramaKey,
      child: Image.network(
        _scenes[_currentSceneIndex]['imageUrl']!,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          
          final double progress = loadingProgress.expectedTotalBytes != null
              ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
              : -1;

          return Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                CircularProgressIndicator(
                  value: progress == -1 ? null : progress,
                  strokeWidth: 5,
                  backgroundColor: Colors.grey.withOpacity(0.5),
                ),
                if (progress != -1)
                  Text(
                    '${(progress * 100).toStringAsFixed(0)}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      shadows: [Shadow(blurRadius: 2)],
                    ),
                  ),
              ],
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return const Center(child: Icon(Icons.error_outline, color: Colors.red, size: 48));
        },
      ),
    );
  }

  void _showPanoramaFullScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PanoramaFullScreenPage(
          imageUrl: _scenes[_currentSceneIndex]['imageUrl']!,
        ),
      ),
    );
  }

  void _jumpToScene(int index) {
    if (index < 0 || index >= _scenes.length || !mounted) return;
    _flutterTts.stop();
    setState(() {
      _currentSceneIndex = index;
    });
    _refreshPanorama();
    Future.delayed(const Duration(milliseconds: 200), _speakSceneDescription);
  }

  void _nextScene() {
    if (_currentSceneIndex < _scenes.length - 1) {
      _jumpToScene(_currentSceneIndex + 1);
    }
  }

  void _previousScene() {
    if (_currentSceneIndex > 0) {
      _jumpToScene(_currentSceneIndex - 1);
    }
  }

  void _showSceneList(BuildContext context) {
    final theme = Theme.of(context);
    showModalBottomSheet(
      context: context,
      backgroundColor: theme.colorScheme.surface,
      builder: (BuildContext bc) {
        return SafeArea(
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _scenes.length,
            itemBuilder: (BuildContext context, int index) {
              final scene = _scenes[index];
              return ListTile(
                leading: Icon(Icons.panorama, color: theme.colorScheme.onSurface),
                title: Text(scene['title']!, style: TextStyle(color: theme.colorScheme.onSurface)),
                onTap: () {
                  Navigator.pop(context);
                  _jumpToScene(index);
                },
              );
            },
          ),
        );
      },
    );
  }
  
  void _showImageDialog(String imageUrl) {
    if (imageUrl.isEmpty || imageUrl.contains('placeholder')) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          child: GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(MaterialPageRoute(
                builder: (_) => FullScreenImagePage(imageUrl: imageUrl),
              ));
            },
            child: CircleAvatar(
              radius: 120,
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
              backgroundImage: CachedNetworkImageProvider(imageUrl),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final phone = widget.college['phone'];
    final hasPhone = phone != null && phone.isNotEmpty;
    final latitude = widget.college['latitude'];
    final longitude = widget.college['longitude'];
    final hasLocation = latitude != null && longitude != null;
    final whatsapp = widget.college['whatsapp'];
    final hasWhatsapp = whatsapp != null && whatsapp.isNotEmpty;

    List<Widget> getTabViews() {
      final views = <Widget>[];
      views.add(_buildTimeTabView());
      if (_hasAcademicData) {
        views.add(_buildAcademicCalendarTabView());
      }
      if (_hasLocationData) {
        views.add(_buildMapViewTab());
      }
      if (_hasVirtualTour) {
        views.add(_buildVirtualTourTabView());
      }
      views.add(_buildCampusTabView());
      return views;
    }

    return WillPopScope(
      onWillPop: () async {
        _showRewardedAd();
        return false;
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
            onPressed: () => _showRewardedAd(),
          ),
          title: Text(
            widget.college['fullname'] ?? '',
            style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface),
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: GestureDetector(
                onTap: () => _showImageDialog(widget.college['image_url'] ?? ''),
                child: ClipOval(
                  child: SizedBox(
                    width: 32,
                    height: 32,
                    child: CachedNetworkImage(
                      imageUrl: widget.college['image_url'] ??
                          'assets/placeholder_image.png',
                      errorWidget: (context, url, error) =>
                          Image.asset('assets/placeholder_image.png'),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        body: _isLoadingTabs
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  Container(
                    color: theme.colorScheme.surface,
                    child: TabBar(
                      controller: _tabController,
                      isScrollable: false, 
                      labelColor: theme.colorScheme.onSurface,
                      unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.7),
                      indicatorColor: theme.colorScheme.onSurface,
                      indicatorWeight: 3.0,
                      dividerColor: Colors.transparent,
                      tabs: _tabs,
                    ),
                  ),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: getTabViews(),
                    ),
                  ),
                ],
              ),
        bottomNavigationBar: Container(
          color: theme.colorScheme.surface,
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  IconButton(
                    icon: Icon(Icons.call,
                        color: theme.colorScheme.onSurface
                            .withOpacity(hasPhone ? 1.0 : 0.5)),
                    onPressed: hasPhone ? _launchDialer : null,
                  ),
                  IconButton(
                    icon: Icon(Icons.navigation,
                        color: theme.colorScheme.onSurface
                            .withOpacity(hasLocation ? 1.0 : 0.5)),
                    onPressed: hasLocation ? _launchNavigation : null,
                  ),
                  IconButton(
                    iconSize: 34.0,
                    icon: Icon(Icons.support_agent,
                        color: theme.colorScheme.onSurface),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => AiAgentPage(
                            isDarkMode: widget.isDarkMode,
                            toggleTheme: widget.toggleTheme,
                            collegeData: widget.college,
                          ),
                        ),
                      );
                    },
                  ),
                  IconButton(
                    icon: FaIcon(FontAwesomeIcons.whatsapp,
                        color: theme.colorScheme.onSurface
                            .withOpacity(hasWhatsapp ? 1.0 : 0.5)),
                    onPressed: hasWhatsapp ? _launchWhatsapp : null,
                  ),
                  IconButton(
                    icon: Icon(Icons.info, color: theme.colorScheme.onSurface),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TertiaryInfoPage(
                            isDarkMode: widget.isDarkMode,
                            toggleTheme: widget.toggleTheme,
                            schoolName: widget.college['fullname'] ?? '',
                            collegeData: widget.college,
                            isFromDetailPage: widget.isFromTertiaryPage,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTimeTabView() {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    if (_isCalendarLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        Container(
          margin: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.0),
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2)),
            ],
          ),
          child: TableCalendar(
            firstDay: DateTime.utc(2020, 1, 1),
            lastDay: DateTime.utc(2030, 12, 31),
            focusedDay: _focusedDay,
            calendarFormat: _calendarFormat,
            eventLoader: _getItemsForDay,
            selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
            onDaySelected: _onDaySelected,
            onFormatChanged: (format) {
              if (_calendarFormat != format) {
                setState(() => _calendarFormat = format);
              }
            },
            onPageChanged: (focusedDay) {
              _focusedDay = focusedDay;
            },
            calendarStyle: CalendarStyle(
              outsideDaysVisible: false,
              markersMaxCount: 1,
              markerDecoration: BoxDecoration(
                  color: currentIsDarkMode ? Colors.white : Colors.black,
                  shape: BoxShape.circle),
              todayDecoration: BoxDecoration(
                  color: (currentIsDarkMode ? Colors.white : Colors.black)
                      .withOpacity(0.3),
                  shape: BoxShape.circle),
              selectedDecoration: BoxDecoration(
                  color: currentIsDarkMode ? Colors.white : Colors.black,
                  shape: BoxShape.circle),
              defaultTextStyle: TextStyle(color: theme.colorScheme.onSurface),
              weekendTextStyle: TextStyle(color: theme.colorScheme.onSurface),
              outsideTextStyle:
                  TextStyle(color: theme.colorScheme.onSurface.withOpacity(0.6)),
              selectedTextStyle: TextStyle(
                  color: currentIsDarkMode ? Colors.black : Colors.white,
                  fontWeight: FontWeight.bold),
              todayTextStyle: TextStyle(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.bold),
            ),
            headerStyle: HeaderStyle(
              formatButtonVisible: true,
              titleCentered: true,
              formatButtonShowsNext: false,
              formatButtonDecoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(16.0)),
              formatButtonTextStyle:
                  TextStyle(color: theme.colorScheme.onSurface),
              titleTextStyle: TextStyle(
                  color: theme.colorScheme.onSurface,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
              leftChevronIcon:
                  Icon(Icons.chevron_left, color: theme.colorScheme.onSurface),
              rightChevronIcon:
                  Icon(Icons.chevron_right, color: theme.colorScheme.onSurface),
            ),
            daysOfWeekStyle: DaysOfWeekStyle(
              weekdayStyle: TextStyle(color: theme.colorScheme.onSurface),
              weekendStyle: TextStyle(color: theme.colorScheme.onSurface),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: _selectedItems.isEmpty
              ? Center(
                  child: Text('No items scheduled for this day',
                      style: TextStyle(color: theme.colorScheme.onSurface)))
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _selectedItems.length,
                  itemBuilder: (context, index) {
                    final item = _selectedItems[index];
                    return _buildUnifiedItemCard(item, theme, currentIsDarkMode);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildAcademicCalendarTabView() {
    final theme = Theme.of(context);

    if (_isCalendarLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_academicCalendarItems.isEmpty) {
      return Center(
        child: Text(
          'No academic calendar entries found.',
          style: TextStyle(color: theme.colorScheme.onSurface),
        ),
      );
    }

    List<Widget> listItems = [];
    String? currentHeader;

    for (var item in _academicCalendarItems) {
      try {
        final startDate = DateTime(
          int.parse(item['startyear'].toString()),
          int.parse(item['startmonth'].toString()),
          int.parse(item['startday'].toString()),
        );
        
        final newHeader = DateFormat('MMMM yyyy').format(startDate);

        if (currentHeader != newHeader) {
          currentHeader = newHeader;
          listItems.add(
            Padding(
              padding: const EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 8.0),
              child: Text(
                currentHeader!,
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          );
        }
        
        listItems.add(_buildAcademicItemCard(item, theme));

      } catch (e) {
        print("Skipping malformed academic calendar item: $item. Error: $e");
      }
    }

    return ListView(
      padding: const EdgeInsets.only(bottom: 16.0),
      children: listItems,
    );
  }

  Widget _buildMapViewTab() {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final fabBackgroundColor = currentIsDarkMode ? Colors.black : Colors.white;
    final fabForegroundColor = currentIsDarkMode ? Colors.white : Colors.black;

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
          child: Column(
            children: [
              TextField(
                controller: _mapSearchController,
                focusNode: _mapSearchFocusNode,
                decoration: InputDecoration(
                  hintText: 'Search locations...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _mapSearchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _mapSearchController.clear();
                            _searchMapLocations('');
                            _mapSearchFocusNode.unfocus();
                          },
                        )
                      : null,
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(15), borderSide: BorderSide.none),
                  filled: true,
                  fillColor: theme.colorScheme.surface,
                ),
                onChanged: _searchMapLocations,
              ),
              if (_mapShowSuggestions)
                Container(
                  margin: const EdgeInsets.only(top: 4),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 8, offset: const Offset(0, 2))],
                  ),
                  constraints: const BoxConstraints(maxHeight: 200),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _mapSearchSuggestions.length,
                    itemBuilder: (context, index) {
                      final building = _mapSearchSuggestions[index];
                      return ListTile(
                        title: Text(building['fullname'] ?? 'Unnamed'),
                        subtitle: Text(building['location'] ?? ''),
                        leading: const Icon(Icons.location_on),
                        dense: true,
                        onTap: () => _selectMapLocation(building),
                      );
                    },
                  ),
                ),
            ],
          ),
        ),
        if (_shouldShowMapFilters)
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _mapFilters.length,
              itemBuilder: (context, index) {
                final filter = _mapFilters[index];
                final isSelected = _mapSelectedFilter == filter;
                return Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: FilterChip(
                    label: Text(filter, style: TextStyle(
                        color: isSelected ? Colors.white : theme.colorScheme.onSurface,
                    )),
                    selected: isSelected,
                    showCheckmark: false,
                    backgroundColor: theme.colorScheme.surface,
                    selectedColor: Colors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                      side: BorderSide(
                        color: isSelected ? Colors.black : theme.colorScheme.surface,
                      ),
                    ),
                    onSelected: (selected) {
                      if (selected) _filterMapLocations(filter);
                    },
                  ),
                );
              },
            ),
          ),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: _mapIsLoading
                ? const Center(child: CircularProgressIndicator())
                : Stack(
                    children: [
                      FlutterMap(
                        mapController: _mapController,
                        options: MapOptions(
                          initialCenter: _mapCenter,
                          initialZoom: 14.0,
                          keepAlive: true,
                        ),
                        children: [
                          TileLayer(
                            urlTemplate: _mapUrlTemplate,
                            userAgentPackageName: 'com.harmonizr.app',
                            tileProvider: CancellableNetworkTileProvider(),
                            tileBuilder: (context, tileWidget, tile) {
                              if (currentIsDarkMode && !_mapIsSatelliteView) {
                                return ColorFiltered(
                                  colorFilter: const ColorFilter.matrix(<double>[
                                    0.2126, 0.7152, 0.0722, 0, 0,
                                    0.2126, 0.7152, 0.0722, 0, 0,
                                    0.2126, 0.7152, 0.0722, 0, 0,
                                    0,      0,      0,      1, 0,
                                  ]),
                                  child: tileWidget,
                                );
                              }
                              return tileWidget;
                            },
                          ),
                          if (_mapUserLocation != null)
                            MarkerLayer(markers: [
                              Marker(
                                point: _mapUserLocation!,
                                width: 60, height: 60,
                                child: const Icon(Icons.my_location, color: Colors.blue, size: 30),
                              ),
                            ]),
                          MarkerLayer(
                            markers: _mapFilteredLocations.where((loc) =>
                              (loc['latitude'] ?? 0.0) != 0.0 && (loc['longitude'] ?? 0.0) != 0.0
                            ).map((location) {
                              final lat = location['latitude'] ?? 0.0;
                              final lng = location['longitude'] ?? 0.0;
                              final isSelected = _mapSelectedLocation != null && _mapSelectedLocation!['id'] == location['id'];
                              return Marker(
                                point: LatLng(lat, lng),
                                width: 80, height: 80,
                                child: GestureDetector(
                                  onTap: () {
                                    _selectMapLocation(location);
                                    Future.delayed(const Duration(milliseconds: 300), () {
                                      Navigator.push(context, MaterialPageRoute(builder: (context) =>
                                        BuildingDetailPage(
                                          building: location,
                                          collegeNameForTable: widget.college['fullname'],
                                          isDarkMode: widget.isDarkMode,
                                          toggleTheme: widget.toggleTheme,
                                        ),
                                      ));
                                    });
                                  },
                                  child: Column(
                                    children: [
                                      Icon(Icons.location_on, color: isSelected ? Colors.blue : Colors.red, size: isSelected ? 40 : 30),
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                        decoration: BoxDecoration(color: theme.colorScheme.surface.withOpacity(0.8), borderRadius: BorderRadius.circular(4)),
                                        child: Text(location['fullname'] ?? '', style: TextStyle(fontSize: 10, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface), textAlign: TextAlign.center, overflow: TextOverflow.ellipsis),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                          if (_mapPathPoints.length >= 2)
                            PolylineLayer(polylines: [
                              Polyline(points: _mapPathPoints, strokeWidth: 4.0, color: Colors.blue),
                            ]),
                        ],
                      ),
                      Positioned(
                        right: 16,
                        bottom: 16,
                        child: Column(
                          children: [
                            FloatingActionButton(
                              mini: true,
                              heroTag: "mapTypeBtn",
                              backgroundColor: fabBackgroundColor,
                              foregroundColor: fabForegroundColor,
                              onPressed: () => setState(() => _mapIsSatelliteView = !_mapIsSatelliteView),
                              child: Icon(_mapIsSatelliteView ? Icons.map_outlined : Icons.satellite_alt_outlined),
                            ),
                            const SizedBox(height: 8),
                            FloatingActionButton(
                              mini: true,
                              heroTag: "zoomInBtn",
                              backgroundColor: fabBackgroundColor,
                              foregroundColor: fabForegroundColor,
                              onPressed: () => _mapController.move(_mapController.camera.center, _mapController.camera.zoom + 1),
                              child: const Icon(Icons.add),
                            ),
                            const SizedBox(height: 8),
                            FloatingActionButton(
                              mini: true,
                              heroTag: "zoomOutBtn",
                              backgroundColor: fabBackgroundColor,
                              foregroundColor: fabForegroundColor,
                              onPressed: () => _mapController.move(_mapController.camera.center, _mapController.camera.zoom - 1),
                              child: const Icon(Icons.remove),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildVirtualTourTabView() {
    final theme = Theme.of(context);
    final iconColor = theme.colorScheme.onSurface;
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final fabBackgroundColor = currentIsDarkMode ? theme.colorScheme.surface : Colors.white;
    final fabForegroundColor = currentIsDarkMode ? Colors.white : Colors.black;

    final isFirstScene = _currentSceneIndex == 0;
    final isLastScene = _currentSceneIndex >= _scenes.length - 1;

    return Stack(
      children: [
        if (_isPanoramaVisible)
          _buildPanorama()
        else
          const Center(child: CircularProgressIndicator()),
        Positioned(
          top: 16,
          left: 16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              FloatingActionButton.small(
                heroTag: "muteBtn",
                backgroundColor: fabBackgroundColor,
                foregroundColor: fabForegroundColor,
                onPressed: () {
                  setState(() {
                    _isMuted = !_isMuted;
                    if (_isMuted) {
                      _flutterTts.stop();
                    }
                  });
                },
                child: Icon(_isMuted ? Icons.volume_off : Icons.volume_up),
              ),
              const SizedBox(height: 8),
              FloatingActionButton.small(
                heroTag: "refreshBtn",
                backgroundColor: fabBackgroundColor,
                foregroundColor: fabForegroundColor,
                onPressed: _refreshPanorama,
                child: const Icon(Icons.refresh),
              ),
              const SizedBox(height: 8),
              FloatingActionButton.small(
                heroTag: "fullscreenBtn",
                backgroundColor: fabBackgroundColor,
                foregroundColor: fabForegroundColor,
                onPressed: _showPanoramaFullScreen,
                child: const Icon(Icons.fullscreen),
              ),
            ],
          ),
        ),
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: Container(
            color: theme.colorScheme.surface,
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16.0),
                  child: IconButton(
                    icon: Icon(Icons.list, color: iconColor),
                    onPressed: () => _showSceneList(context),
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.chevron_left,
                        color: isFirstScene
                            ? iconColor.withOpacity(0.4)
                            : iconColor,
                      ),
                      onPressed: isFirstScene ? null : _previousScene,
                    ),
                    IconButton(
                      icon: Icon(_isTtsPlaying ? Icons.stop_circle_outlined : Icons.play_circle_outline, color: iconColor, size: 28),
                      onPressed: () {
                        if (_isTtsPlaying) {
                          _flutterTts.stop();
                        } else {
                          _speakSceneDescription();
                        }
                      },
                    ),
                    IconButton(
                      icon: Icon(Icons.chevron_right,
                        color: isLastScene
                            ? iconColor.withOpacity(0.4)
                            : iconColor,
                      ),
                      onPressed: isLastScene ? null : _nextScene,
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(right: 16.0),
                  child: IconButton(
                    icon: FaIcon(
                      FontAwesomeIcons.vrCardboard,
                      color: iconColor.withOpacity(0.5),
                    ),
                    onPressed: null,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCampusTabView() {
    final theme = Theme.of(context);
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: LayoutBuilder(
        builder: (context, constraints) {
          int crossAxisCount = 2;
          double aspectRatio = 1.3;

          if (constraints.maxWidth > 1200) {
            crossAxisCount = 6;
            aspectRatio = 1.4;
          } else if (constraints.maxWidth > 900) {
            crossAxisCount = 4;
            aspectRatio = 1.3;
          } else if (constraints.maxWidth > 600) {
            crossAxisCount = 3;
            aspectRatio = 1.2;
          }

          return GridView.count(
            crossAxisCount: crossAxisCount,
            padding: const EdgeInsets.all(16),
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            childAspectRatio: aspectRatio,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _buildGridItem(context, 'Start', Icons.play_arrow, theme, () async {
                bool hasHelpdesks = await _checkHelpdesksAvailability(widget.college);
                bool hasLinks = await _checkLinksAvailability(widget.college);
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryStartPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      hasHelpdesksPreloaded: hasHelpdesks, hasLinksPreloaded: hasLinks,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Updates', Icons.update, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryUpdatesPage(
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      collegeData: widget.college, institutionName: widget.college['fullname'] ?? '',
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Path', Icons.timeline, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryPathPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'People', Icons.people, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryPeoplePage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Lodging', Icons.hotel, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryLodgingPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Shop & Eat', Icons.restaurant_menu, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryShopEatPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Transport', Icons.directions_bus, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryTransportPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Core', Icons.stars, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryCorePage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Academics', Icons.school, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryAcademicsPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Programs', Icons.list_alt, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryProgramsPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Athletics & Groups', Icons.sports_soccer, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryAthleticsGroupsPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Media', Icons.perm_media, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryMediaPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Startups', Icons.rocket_launch, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryStartupsPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Projects & Publications', Icons.article, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryProjectsPublicationsPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Buildings & Spaces', Icons.apartment, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryBuildingsSpacesPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Calendar', Icons.calendar_month, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryCalendarPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Feedback', Icons.feedback, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryFeedbackPage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Historical Timeline', Icons.history_edu, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryTimelinePage(
                      institutionName: widget.college['fullname'] ?? '', collegeData: widget.college,
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Rentals', Icons.swap_horiz, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryRentalsPage(
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      collegeData: widget.college, institutionName: widget.college['fullname'] ?? '',
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Jobs', Icons.work, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryJobsPage(
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      collegeData: widget.college, institutionName: widget.college['fullname'] ?? '',
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Services', Icons.miscellaneous_services, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryServicesPage(
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      collegeData: widget.college, institutionName: widget.college['fullname'] ?? '',
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Money', Icons.payments, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryMoneyPage(
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      collegeData: widget.college, institutionName: widget.college['fullname'] ?? '',
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
              _buildGridItem(context, 'Health & Safety', Icons.local_hospital, theme, () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => TertiaryHealthPage(
                      isDarkMode: widget.isDarkMode, toggleTheme: widget.toggleTheme,
                      collegeData: widget.college, institutionName: widget.college['fullname'] ?? '',
                      isFromDetailPage: widget.isFromTertiaryPage)));
              }),
            ],
          );
        },
      ),
    );
  }

  Widget _buildAcademicItemCard(Map<String, dynamic> item, ThemeData theme) {
    final String title = item['fullname'] ?? 'No Title';
    
    final int startDay = int.tryParse(item['startday']?.toString() ?? '') ?? 0;
    final int startMonth = int.tryParse(item['startmonth']?.toString() ?? '') ?? 0;
    final int startYear = int.tryParse(item['startyear']?.toString() ?? '') ?? 0;

    final int endDay = int.tryParse(item['endday']?.toString() ?? '') ?? 0;
    final int endMonth = int.tryParse(item['endmonth']?.toString() ?? '') ?? 0;
    final int endYear = int.tryParse(item['endyear']?.toString() ?? '') ?? 0;

    String dateString = 'Date not specified';

    if (startYear != 0) {
      final startDate = DateTime(startYear, startMonth, startDay);
      if (endYear != 0 && (startDay != endDay || startMonth != endMonth || startYear != endYear)) {
        final endDate = DateTime(endYear, endMonth, endDay);
        dateString = "${DateFormat.yMMMd().format(startDate)} - ${DateFormat.yMMMd().format(endDate)}";
      } else {
        dateString = DateFormat.yMMMd().format(startDate);
      }
    }
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 6.0),
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 14, color: theme.colorScheme.onSurfaceVariant),
                const SizedBox(width: 8),
                Text(
                  dateString,
                  style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconForType(String type) {
    switch (type) {
      case 'event': return Icons.event;
      case 'academic': return Icons.school;
      case 'weekly': return Icons.schedule;
      case 'class': return Icons.class_;
      default: return Icons.circle;
    }
  }

  String _getDisplayNameForType(String type) {
    switch (type) {
        case 'event': return 'Event';
        case 'academic': return 'Academic';
        case 'weekly': return 'Schedule';
        case 'class': return 'Class';
        default: return 'Item';
    }
  }

  Widget _buildUnifiedItemCard(_UnifiedCalendarItem item, ThemeData theme, bool isDarkMode) {
    Color labelBackgroundColor;
    Color labelTextColor;

    if (isDarkMode) {
      labelBackgroundColor = theme.colorScheme.secondaryContainer.withOpacity(0.5);
      labelTextColor = theme.colorScheme.onSecondaryContainer;
    } else {
      labelBackgroundColor = Colors.black87; 
      labelTextColor = Colors.white;
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => EventDetailPage(
                  eventData: item.sourceData,
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(_getIconForType(item.type),
                    color: isDarkMode ? Colors.white : Colors.black),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(item.title, style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface)),
                    Padding(
                      padding: const EdgeInsets.only(top: 6, bottom: 4),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: labelBackgroundColor,
                          borderRadius: BorderRadius.circular(4)
                        ),
                        child: Text(
                          _getDisplayNameForType(item.type),
                          style: TextStyle(
                            fontSize: 10, 
                            fontWeight: FontWeight.bold,
                            color: labelTextColor
                          ),
                        ),
                      ),
                    ),
                    if (item.location.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(item.location,
                            style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12)),
                      ),
                    if (item.timeString.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(item.timeString,
                            style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12)),
                      ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios,
                  size: 16, color: theme.colorScheme.onSurfaceVariant),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon,
      ThemeData theme, VoidCallback? onTap) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon,
                  size: 32,
                  color: isDarkMode ? theme.colorScheme.secondary : Colors.black),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FullScreenImagePage extends StatelessWidget {
  final String imageUrl;
  const FullScreenImagePage({Key? key, required this.imageUrl}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Center(
        child: InteractiveViewer(
          panEnabled: true,
          minScale: 0.5,
          maxScale: 4.0,
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            fit: BoxFit.contain,
            placeholder: (context, url) => const Center(child: CircularProgressIndicator()),
            errorWidget: (context, url, error) => const Icon(Icons.error, color: Colors.white),
          ),
        ),
      ),
    );
  }
}

class PanoramaFullScreenPage extends StatelessWidget {
  final String imageUrl;
  const PanoramaFullScreenPage({Key? key, required this.imageUrl}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          Panorama(
            child: Image.network(
              imageUrl,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return const Center(child: CircularProgressIndicator());
              },
            ),
          ),
          Positioned(
            top: 40,
            left: 16,
            child: CircleAvatar(
              backgroundColor: Colors.black.withOpacity(0.5),
              child: IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}