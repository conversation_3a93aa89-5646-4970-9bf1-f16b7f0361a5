import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:speech_to_text/speech_recognition_error.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'dart:async';
import 'dart:io';
import 'package:collection/collection.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';

// --- Helper Classes (Moved Outside State Class) ---
class ChatMessage {
  final String text;
  final bool isUser;
  final bool isTyping;
  ChatMessage({required this.text, required this.isUser, this.isTyping = false});

  @override bool operator ==(Object other) => identical(this, other) || other is ChatMessage && runtimeType == other.runtimeType && text == other.text && isUser == other.isUser && isTyping == other.isTyping;
  @override int get hashCode => text.hashCode ^ isUser.hashCode ^ isTyping.hashCode;
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;
  final ThemeData theme;
  const ChatBubble({
    Key? key,
    required this.message,
    required this.isDarkMode,
    required this.theme,
  }) : super(key: key);

  @override Widget build(BuildContext context) {
    if (message.isTyping) {
       return Align( alignment: Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
            decoration: BoxDecoration( color: theme.colorScheme.surfaceVariant.withOpacity(0.8), borderRadius: const BorderRadius.only( topLeft: Radius.circular(4.0), topRight: Radius.circular(18.0), bottomLeft: Radius.circular(18.0), bottomRight: Radius.circular(18.0) ) ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                 Text( message.text, style: TextStyle( color: theme.colorScheme.onSurfaceVariant, fontStyle: FontStyle.italic, fontSize: 14 ) ),
              ],
            ),
          ),
       );
    }
    final Color userBubbleColor = isDarkMode ? Colors.grey[800]! : Colors.grey[300]!;
    final Color aiBubbleColor = theme.colorScheme.surfaceVariant;
    final Color userTextColor = theme.colorScheme.onSurface;
    final Color aiTextColor = theme.colorScheme.onSurfaceVariant;
    final bubbleColor = message.isUser ? userBubbleColor : aiBubbleColor;
    final textColor = message.isUser ? userTextColor : aiTextColor;
    final borderRadius = BorderRadius.only( topLeft: Radius.circular(message.isUser ? 18.0 : 4.0), topRight: Radius.circular(message.isUser ? 4.0 : 18.0), bottomLeft: const Radius.circular(18.0), bottomRight: const Radius.circular(18.0) );

    return Align( alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 5.0, horizontal: 8.0),
        padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 10.0),
        constraints: BoxConstraints( maxWidth: MediaQuery.of(context).size.width * 0.8 ),
        decoration: BoxDecoration( color: bubbleColor, borderRadius: borderRadius, boxShadow: [ BoxShadow( color: Colors.black.withOpacity(0.06), blurRadius: 3, offset: const Offset(1, 2) ) ] ),
        child: SelectableText( message.text, style: TextStyle( color: textColor, fontSize: 15, height: 1.35 ) ),
      ),
    );
  }
}

// --- Extensions (Moved Outside State Class) ---
extension StringExtension on String {
  String capitalize() {
    if (isEmpty) return "";
    return split(' ')
       .map((word) {
         if (word.isEmpty) return "";
         if (word.length > 1 && word == word.toUpperCase()) return word; // Preserve acronyms
         return word[0].toUpperCase() + (word.length > 1 ? word.substring(1).toLowerCase() : "");
       })
       .join(' ');
  }

  String readableFieldName() {
    if (isEmpty) return "";
    String spaced = replaceAll('_', ' ');
    spaced = spaced.replaceAllMapped(RegExp(r'([a-z])([A-Z])'), (match) => '${match.group(1)} ${match.group(2)}');
    spaced = spaced.replaceAllMapped(RegExp(r'([A-Z])([A-Z][a-z])'), (match) => '${match.group(1)} ${match.group(2)}');
    return spaced.capitalize();
  }
}


// --- Main Widget ---
class AiAgentPage extends StatefulWidget {
 final bool isDarkMode;
 final VoidCallback toggleTheme;
 final Map<String, dynamic>? collegeData;

 const AiAgentPage({
   Key? key,
   required this.isDarkMode,
   required this.toggleTheme,
   this.collegeData,
 }) : super(key: key);

 // --- Static Cache Members (for pre-warming and persistence) ---
 static final Map<String, String> _sDbCache = {};
 static String? _sKnowledgeBaseCache;
 static bool _sIsCacheReady = false;
 static bool _sIsCaching = false;
 static String? _sCachingForCollegeId;
 static Future<void>? _sCachingFuture;

 /// Public static method to allow pre-warming the cache from other parts of the app.
 static Future<void> prewarmCacheForCollege(Map<String, dynamic> collegeData) {
    if (collegeData['id'] == null) {
      return Future.error(Exception("College data must have an 'id' for caching."));
    }
    final String collegeId = collegeData['id'].toString();

    // Case 1: Cache is already ready for this specific college. Do nothing.
    if (_sIsCacheReady && _sCachingForCollegeId == collegeId) {
      print("AI Cache is already warm for college $collegeId.");
      return Future.value();
    }

    // Case 2: Caching is currently in progress for this college. Return the existing future.
    if (_sIsCaching && _sCachingForCollegeId == collegeId) {
      print("AI Cache warmup already in progress for college $collegeId. Joining existing process.");
      return _sCachingFuture!;
    }

    // Case 3: Need to start a new caching process.
    print("Starting new AI Cache warmup for college $collegeId.");
    _sIsCaching = true;
    _sIsCacheReady = false;
    _sCachingForCollegeId = collegeId;
    _sDbCache.clear();
    _sKnowledgeBaseCache = null;

    // The actual work, wrapped in the static future.
    _sCachingFuture = _cacheDatabaseAndKnowledgeBase(collegeData).then((_) {
      _sIsCaching = false;
      _sIsCacheReady = true;
      print("AI Cache warmup SUCCESS for college $collegeId.");
    }).catchError((e, stacktrace) {
      print("AI Cache warmup FAILED for college $collegeId. Error: $e\n$stacktrace");
      _sIsCaching = false;
      _sIsCacheReady = false;
      _sCachingForCollegeId = null; // Invalidate cache on error
      throw e; // Re-throw so callers can handle it.
    });

    return _sCachingFuture!;
 }
 
 /// Static method that performs the one-time, heavy-lifting of fetching all data.
 static Future<void> _cacheDatabaseAndKnowledgeBase(Map<String, dynamic> collegeData) async {
    if (collegeData['fullname']?.isEmpty == true) {
      print("Caching skipped: collegeData is not available.");
      return;
    }
    print("Starting data caching process...");
    // This is a stand-in for the real map which is an instance member.
    // For static caching, we just need the keys.
    const tableManifestKeys = [
      'helpdesks', 'accessibility', 'faq', 'links', 'construction', 'printing',
      'daycares', 'sustainability', 'notices', 'socialmediafeeds', 'admissionsprocess',
      'registrationprocess', 'selection', 'costsorrates', 'scholarships', 'payments',
      'orientations', 'symposiums', 'graduation', 'people', 'currentstudents',
      'housing', 'locallodging', 'shopsoreateries', 'mealplans', 'localareadining',
      'studentdiscounts', 'inventory', 'menus', 'campusshuttle', 'parkingspaces',
      'localtransport', 'schools', 'departments', 'centers', 'documents', 'majors',
      'minors', 'funding', 'coursecatalog', 'enrollmentexercise', 'academicresources',
      'academichonors', 'academicprizes', 'academicdress', 'entryrequirements',
      'gradingscale', 'programs', 'signatureevents', 'traditions', 'partnershipopportunities',
      'athletics', 'orgsorclubs', 'researchgroups', 'committees', 'news',
      'periodicals', 'radio', 'television', 'photos', 'videos', 'accelerators',
      'makerspaces', 'startupfunds', 'startups', 'researchprojects', 'theses',
      'books', 'articles', 'patents', 'building', 'rooms', 'roomequipment',
      'roomassignments', 'publicart', 'emergencyequipment', 'classschedules',
      'weeklyschedule', 'events', 'academiccalendar', 'feedback', 'historicaltimeline',
      'rentals', 'rentalequipmentcalendar', 'jobs', 'services', 'atms',
      'clinicsorhospitals', 'counselingservices', 'emergencycontacts', 'safetyprocedures',
      'connectivity', 'giving'
    ];


    final collegeIdentifier = collegeData['tableprefix']?.toString().trim() ??
                               collegeData['fullname']!.toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]+'), '').replaceAll(' ', '_');

    // 1. Cache Database Tables in parallel
    List<Future> dbFutures = [];
    final tableNamesToCache = tableManifestKeys;
    for (String tableNameShort in tableNamesToCache) {
      final fullTableName = "${collegeIdentifier}_$tableNameShort";
      dbFutures.add(
        _fetchSupabaseDataForRow(fullTableName, maxLengthBudget: 50000)
            .then((data) {
              if (data.isNotEmpty) {
                _sDbCache[tableNameShort] = data;
              }
            }).catchError((e) {
              print("Error caching table $tableNameShort: $e");
            })
      );
    }
    await Future.wait(dbFutures);
    print("Database caching complete. Cached ${_sDbCache.length} tables.");

    // 2. Cache Knowledge Base
    _sKnowledgeBaseCache = await _searchPdfKnowledgeBase(
      collegeBucketId: collegeIdentifier,
      maxLengthBudget: 300000 // Approx 75k tokens
    );
    print("Knowledge base caching complete. Cache size: ${_sKnowledgeBaseCache?.length ?? 0} chars.");
}

 /// This function is now only used for the initial caching and is static.
 static Future<String> _searchPdfKnowledgeBase({required String collegeBucketId, required int maxLengthBudget}) async {
    if (maxLengthBudget <= 0) return "";
    final knowledgeBaseDir = 'knowledgebase';
    final pdfTextExtractor = StringBuffer();
    try {
        final fileList = await Supabase.instance.client.storage.from(collegeBucketId).list(path: knowledgeBaseDir);
        final pdfFiles = fileList.where((file) => file.name.toLowerCase().endsWith('.pdf')).toList();
        if (pdfFiles.isEmpty) return "";

        for (final file in pdfFiles) {
            if (pdfTextExtractor.length >= maxLengthBudget) {
                pdfTextExtractor.writeln("\n... (knowledge base truncated due to budget)");
                break;
            }
            try {
                final filePath = '$knowledgeBaseDir/${file.name}';
                final Uint8List fileBytes = await Supabase.instance.client.storage.from(collegeBucketId).download(filePath);
                final PdfDocument document = PdfDocument(inputBytes: fileBytes);
                String text = PdfTextExtractor(document).extractText();
                document.dispose();
                pdfTextExtractor.writeln('\n--- Content from ${file.name} ---\n');
                pdfTextExtractor.writeln(text);
            } catch (e) {
                print("Error processing PDF file '${file.name}': $e");
            }
        }
    } catch (e) {
        print("Error searching PDF knowledge base: $e");
        return "";
    }
    return pdfTextExtractor.length > maxLengthBudget
        ? pdfTextExtractor.toString().substring(0, maxLengthBudget)
        : pdfTextExtractor.toString();
}

 /// This function is now only used for the initial caching and is static.
 static Future<String> _fetchSupabaseDataForRow(String tableName, {required int maxLengthBudget}) async {
    if (maxLengthBudget <= 0) return "";
    try {
        final response = await Supabase.instance.client
            .from(tableName)
            .select()
            .timeout(const Duration(seconds: 25));

        if (response is List && response.isNotEmpty) {
            final sb = StringBuffer();
            for (int i = 0; i < response.length; i++) {
                if (sb.length >= maxLengthBudget) {
                    sb.writeln("- ... (items truncated)");
                    break;
                }
                final row = response[i] as Map<String, dynamic>;
                List<String> parts = [];
                row.forEach((key, value) {
                    if (value != null &&
                        key != 'id' && key != 'created_at' && key != 'updated_at' &&
                        key != 'uuid' && !key.endsWith("_id") &&
                        value.toString().trim().isNotEmpty) {
                        String valStr = value.toString().trim();
                        // Use extension method on string literal
                        parts.add('${key.readableFieldName()}: $valStr');
                    }
                });
                if (parts.isNotEmpty) {
                    sb.write('- ${parts.join('; ')}\n');
                }
            }
            return sb.toString().trim();
        } else {
            return '';
        }
    } on PostgrestException catch (e) {
        if (e.code == '42P01') {
             return "";
        }
        print("Supabase error fetching '$tableName': ${e.message}");
        return "Error fetching data for ${tableName.readableFieldName()}.\n";
    } catch (e) {
        print("General error fetching '$tableName': $e");
        return "Error fetching data for ${tableName.readableFieldName()}: $e\n";
    }
}


 @override
 _AiAgentPageState createState() => _AiAgentPageState();
}

enum ProcessingStep { idle, processing }

class _AiAgentPageState extends State<AiAgentPage> {
 final TextEditingController _messageController = TextEditingController();
 final List<ChatMessage> _messages = [];
 ProcessingStep _processingStep = ProcessingStep.idle;
 final ScrollController _scrollController = ScrollController();

 bool _isListening = false;

 final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // IMPORTANT: Replace with your actual API Key
 final String _model = 'gemini-2.0-flash-lite';

 // Language selection
 String? _outputLanguage = 'English';

 int _dailyTokenCount = 0;
 final int _maxDailyTokens = 5000000;
 int _lastPromptTokenCount = 0;
 int _lastApiResponseTokenCount = 0;

  static const double _inputCostPer1MTokens_FlashLatest = 0.35;
  static const double _outputCostPer1MTokens_FlashLatest = 1.05;

  final double _inputCostPer1kTokens = _inputCostPer1MTokens_FlashLatest / 1000.0;
  final double _outputCostPer1kTokens = _outputCostPer1MTokens_FlashLatest / 1000.0;

 final double _exchangeRate = 2000.0;
 static const double APPROX_CHARS_PER_TOKEN = 4.0;
 static const int APPROX_TABLE_SELECTION_RESPONSE_TOKENS = 50;
 static const int APPROX_FINAL_RESPONSE_TOKENS = 300;

  final List<String> _alwaysConsiderTables = [
      'people', 'services', 'departments', 'schools', 'centers', 'helpdesks', 'faq'
  ];

 late stt.SpeechToText _speechToText;
 late FlutterTts _flutterTts;
 bool _isMuted = true;

 List<Map<String, String>> _availableVoices = [];
 Map<String, String>? _selectedVoice;
 bool _voicesLoaded = false;

 // Local instance state reflecting the global cache status
 bool _isCacheReady = false;

 final Map<String, List<String>> _tableKeywords = {
      'helpdesks': ['help desk', 'it support', 'tech support', 'library help', 'student services desk', 'assistance', 'computer problem', 'wifi issue', 'password reset', 'student support', 'get help', 'contact helpdesk'],
      'accessibility': ['accessibility', 'disability', 'disabled', 'ada', 'special needs', 'accomodation', 'access', 'mobility', 'learning support'],
      'faq': ['faq', 'frequently asked', 'questions', 'common questions', 'q&a', 'ask', 'wondering', 'general help', 'information', 'how do i', 'what about'],
      'links': ['link', 'website', 'url', 'resource', 'webpage', 'portal', 'online form', 'find'],
      'construction': ['construction', 'building work', 'renovation', 'campus updates', 'project', 'noise', 'closure'],
      'printing': ['print', 'printer', 'printing', 'copies', 'photocopy', 'scan', 'cost to print'],
      'daycares': ['daycare', 'childcare', 'nursery', 'kids', 'child care', 'preschool'],
      'sustainability': ['sustainability', 'green', 'environment', 'eco', 'recycling', 'conservation', 'solar'],
      'notices': ['notice', 'alert', 'announcement', 'update', 'important info', 'news', 'bulletin'],
      'socialmediafeeds': ['social media', 'twitter', 'facebook', 'instagram', 'linkedin', 'tiktok', 'youtube channel'],
      'admissionsprocess': ['admission', 'admissions', 'apply', 'application', 'how to apply', 'enroll', 'acceptance', 'get in', 'prospectus', 'entry requirements'],
      'registrationprocess': ['registration', 'register', 'enrollment', 'course selection', 'sign up', 'add drop class', 'choose courses', 'academic planning'],
      'selection': ['selection criteria', 'admission profile', 'student demographics', 'acceptance rate', 'average gpa', 'sat score', 'act score'],
      'costsorrates': ['cost', 'fee', 'fees', 'tuition', 'rate', 'price', 'how much', 'payment plan', 'expense', 'school fees'],
      'scholarships': [
          'scholarship', 'scholarships', 'grant', 'grants', 'financial aid',
          'bursary', 'funding', 'award', 'loan', 'fafsa', 'student aid',
          'tuition assistance', 'financial support', 'fellowship', 'list scholarships', 'all scholarships'
      ],
      'payments': ['payment', 'pay', 'billing', 'invoice', 'tuition payment', 'how to pay', 'deadline', 'finance office'],
      'orientations': ['orientation', 'welcome week', 'new student', 'campus tour', 'introduction', 'onboarding'],
      'symposiums': ['symposium', 'conference', 'seminar', 'lecture series', 'guest speaker', 'academic event'],
      'graduation': ['graduation', 'commencement', 'ceremony', 'degree', 'graduate', 'diploma', 'finish school'],
      'people': ['faculty', 'staff', 'directory', 'contact', 'professor', 'teacher', 'employee list', 'instructor', 'advisor', 'dean', 'department head', 'phone number for', 'email for', 'list people', 'all staff'],
      'currentstudents': ['current student', 'student portal', 'student id', 'student life', 'enrolled student'],
      'housing': ['housing', 'residence', 'dorm', 'dormitory', 'accommodation', 'living on campus', 'room assignment', 'ra', 'res life', 'list housing', 'all dorms', 'where to live', 'housing options'],
      'locallodging': ['hotel', 'lodging', 'bnb', 'accomodation', 'off-campus stay', 'nearby hotel', 'place to stay', 'visitor housing'],
      'shopsoreateries': ['store', 'shop', 'cafe', 'dining', 'eatery', 'food', 'restaurant', 'canteen', 'bookstore', 'campus store', 'merchandise', 'eat', 'list stores', 'where to eat'],
      'mealplans': ['meal plan', 'dining plan', 'food points', 'swipes', 'how much is meal plan'],
      'localareadining': ['nearby restaurant', 'off-campus food', 'places to eat near campus', 'town dining'],
      'studentdiscounts': ['discount', 'student deal', 'coupon', 'offer', 'save money'],
      'inventory': ['bookstore inventory', 'item stock', 'merchandise', 'textbook', 'supplies'],
      'menus': ['menu', 'dining hall menu', 'whats for lunch', 'food options', 'cafeteria food', 'nutrition'],
      'campusshuttle': ['shuttle', 'bus', 'transport', 'campus bus', 'route', 'schedule', 'getting around'],
      'parkingspaces': ['parking', 'permit', 'lot', 'where to park', 'car park', 'vehicle registration', 'ticket'],
      'localtransport': ['public transport', 'local bus', 'train', 'metro', 'getting here', 'travel to campus'],
      'schools': ['school of', 'college of', 'division', 'faculty', 'academic school', 'list schools', 'all schools'],
      'departments': ['department', 'academic department', 'program contact', 'major department', 'minor department', 'list departments', 'all departments'],
      'centers': ['research center', 'institute', 'lab', 'facility', 'program center', 'list centers', 'all centers'],
      'documents': ['form', 'report', 'pdf', 'download', 'handbook', 'policy document', 'transcript request', 'application form', 'list documents'],
      'majors': ['major', 'degree program', 'field of study', 'course of study', 'what majors', 'list of majors', 'all majors', 'available majors'],
      'minors': ['minor', 'concentration', 'certificate program', 'list of minors', 'all minors', 'available minors'],
      'funding': ['research funding', 'grant opportunity', 'project support', 'financial support for research'],
      'coursecatalog': ['course catalog', 'course list', 'class description', 'module details', 'subject list', 'curriculum', 'all courses', 'list courses'],
      'enrollmentexercise': ['mock registration', 'practice enrollment', 'course signup simulation'],
      'academicresources': ['tutoring', 'academic support', 'writing center', 'library resources', 'study help', 'advisor', 'mentor', 'struggling academically'],
      'academichonors': ['honors', 'dean\'s list', 'gpa requirement', 'academic distinction', 'cum laude'],
      'academicprizes': ['prize', 'award', 'competition', 'student award', 'scholarship award'],
      'academicdress': ['graduation gown', 'regalia', 'cap and gown', 'hood', 'academic attire'],
      'entryrequirements': ['admission requirements', 'prerequisites', 'how to get in', 'application criteria', 'gpa needed', 'test scores', 'apply'],
      'gradingscale': ['grading system', 'gpa scale', 'how grades work', 'marks', 'pass fail'],
      'programs': ['student program', 'special program', 'initiative', 'extracurricular', 'leadership program', 'list programs'],
      'signatureevents': ['homecoming', 'annual event', 'campus tradition', 'key ceremony', 'founder day'],
      'traditions': ['campus traditions', 'rituals', 'history', 'customs'],
      'partnershipopportunities': ['partnership', 'collaboration', 'industry link', 'community engagement'],
      'athletics': ['athletics', 'sports', 'team', 'game schedule', 'varsity', 'intramural', 'coach', 'stadium', 'gym', 'list sports teams'],
      'orgsorclubs': ['clubs', 'student organizations', 'student group', 'society', 'join a club', 'extracurricular activities', 'list clubs', 'all clubs'],
      'researchgroups': ['research group', 'lab group', 'research team', 'project group'],
      'committees': ['committee', 'governance', 'board', 'student government', 'faculty senate'],
      'news': ['news', 'latest news', 'press release', 'campus updates', 'announcements'],
      'periodicals': ['campus newspaper', 'magazine', 'journal', 'student publication'],
      'radio': ['campus radio', 'radio station', 'broadcast'],
      'television': ['campus tv', 'tv station', 'student broadcast'],
      'photos': ['photos', 'gallery', 'images', 'pictures', 'campus scenery'],
      'videos': ['videos', 'youtube', 'promotional video', 'recordings'],
      'accelerators': ['startup accelerator', 'incubator', 'entrepreneurship program', 'business support'],
      'makerspaces': ['makerspace', 'fab lab', 'diy space', '3d printer', 'workshop'],
      'startupfunds': ['seed fund', 'venture capital', 'student startup funding', 'pitch competition'],
      'startups': ['student startup', 'campus venture', 'spin-off company'],
      'researchprojects': ['research project', 'faculty research', 'student research', 'study'],
      'theses': ['thesis', 'dissertation', 'capstone project', 'final paper', 'senior project'],
      'books': ['faculty books', 'alumni books', 'library books', 'textbooks'],
      'articles': ['journal article', 'research paper', 'faculty publication'],
      'patents': ['patent', 'invention', 'intellectual property', 'ip'],
      'building': ['building map', 'building directory', 'specific building', 'hall name', 'facility location', 'list buildings'],
      'rooms': ['room schedule', 'classroom location', 'lab number', 'book a room', 'study space', 'lecture hall'],
      'roomequipment': ['projector', 'smartboard', 'av equipment', 'classroom tech', 'computer lab'],
      'roomassignments': ['dorm assignment', 'room key', 'housing placement', 'move-in'],
      'publicart': ['campus art', 'sculpture', 'mural', 'art installation', 'gallery exhibit'],
      'emergencyequipment': ['aed location', 'fire extinguisher', 'safety equipment', 'first aid kit'],
      'classschedules': ['class schedule', 'my schedule', 'course times', 'timetable', 'find class location'],
      'weeklyschedule': ['weekly events', 'this week schedule', 'regular meetings'],
      'events': ['events', 'calendar', 'upcoming events', 'activity', 'workshop', 'register for event', 'what happening', 'list events', 'all events'],
      'academiccalendar': ['academic calendar', 'term dates', 'semester dates', 'important dates', 'deadline', 'holiday', 'break'],
      'feedback': ['feedback', 'suggestion', 'complaint', 'survey', 'course evaluation'],
      'historicaltimeline': ['history', 'college history', 'timeline', 'milestones', 'founding'],
      'rentals': ['equipment rental', 'space rental', 'book equipment', 'reserve room'],
      'rentalequipmentcalendar': ['equipment availability', 'rental booking', 'gear schedule'],
      'jobs': ['job', 'campus job', 'student employment', 'work study', 'career services', 'vacancy', 'hiring', 'list jobs', 'all jobs'],
      'services': ['student services', 'support services', 'health services', 'it services', 'counseling services', 'career services', 'what services offered', 'helpdesk services', 'list of services', 'all services', 'available services'],
      'atms': ['atm', 'cash machine', 'bank machine', 'withdraw cash'],
      'clinicsorhospitals': ['health center', 'clinic', 'hospital', 'doctor', 'nurse', 'medical appointment', 'sick'],
      'counselingservices': ['counseling', 'mental health', 'therapist', 'psychologist', 'support group', 'wellness center', 'stress', 'anxiety'],
      'emergencycontacts': ['emergency number', 'campus security', 'campus police', 'report incident', 'hotline'],
      'safetyprocedures': ['safety plan', 'emergency procedure', 'evacuation', 'lockdown', 'fire safety'],
      'connectivity': ['wifi', 'internet access', 'network', 'eduroam', 'connect to wifi', 'internet down'],
      'giving': ['donate', 'donation', 'support', 'fundraising', 'alumni giving', 'gift'],
  };
 final Map<String, String> _tableManifest = {
      'helpdesks': 'Campus help desk locations and services (IT, library, student services, etc.) and general student support. Contains details about specific help desks like IT Help Desk, including contact info, hours, and a general scope of their support areas. Useful for finding helpdesk contact or location.',
      'accessibility': 'Disability support services and campus accessibility resources.',
      'faq': 'Frequently asked questions about various campus topics including admissions, academics, and campus life. Good for general queries.',
      'links': 'Important website links for departments, applications, and resources.',
      'construction': 'Current/pending campus construction projects with locations and timelines.',
      'printing': 'Printing service locations, costs, and availability.',
      'daycares': 'On-campus childcare facilities and registration information.',
      'sustainability': 'Environmental initiatives and green campus programs.',
      'notices': 'Campus-wide announcements and time-sensitive alerts.',
      'socialmediafeeds': 'Official college social media accounts and links.',
      'admissionsprocess': 'Step-by-step application procedures, admission requirements, and related information.',
      'registrationprocess': 'Course enrollment steps and academic planning.',
      'selection': 'Demographic/academic profiles of admitted students, acceptance rates, and selection criteria.',
      'costsorrates': 'Tuition fees, housing costs, school fees, and other financial rates.',
      'scholarships': 'Information on available grants, awards, scholarships, bursaries, loans, and other financial aid opportunities for students. Provides a list of scholarships.',
      'payments': 'Payment methods, portals, and billing information for tuition and fees.',
      'orientations': 'New student orientation programs and schedules.',
      'symposiums': 'Academic conference details and participation info.',
      'graduation': 'Commencement ceremony logistics and graduate data.',
      'people': 'Faculty/staff directories with contact info, roles, and departments. Useful for finding specific individuals or people in a department. Contains a list of people.',
      'currentstudents': 'Profiles of enrolled students (majors, housing, etc.).',
      'housing': 'Residence hall details, policies, and living arrangements on campus. Lists available dormitories/residences and their features. Use this table for queries about housing options, dorms, or on-campus living.',
      'locallodging': 'Off-campus hotels/B&Bs near the college for visitors.',
      'shopsoreateries': 'On-campus stores, cafes, and dining options. Lists places to eat or shop on campus.',
      'mealplans': 'Dining plan options and associated costs for students.',
      'localareadining': 'Nearby off-campus restaurants and food discounts.',
      'studentdiscounts': 'Local business offers for students.',
      'inventory': 'Campus store products, merchandise, and pricing.',
      'menus': 'Daily dining hall meal offerings and nutritional info.',
      'campusshuttle': 'Transportation routes and schedules for the campus bus/shuttle.',
      'parkingspaces': 'Parking lot locations, permits, and regulations.',
      'localtransport': 'Public transit options and regional travel to/from campus.',
      'schools': 'Academic divisions (e.g., School of Arts), their deans, and departments within them. Provides a list of schools.',
      'departments': 'Academic department info, faculty contacts, and associated school. Provides a list of departments.',
      'centers': 'Research centers, institutes, and special program facilities, often with their focus area. Provides a list of centers.',
      'documents': 'Official forms, reports, and policy PDFs for download. Provides a list of available documents.',
      'majors': 'Undergraduate degree programs, requirements, and descriptions. Provides a list of all available majors.',
      'minors': 'Minor programs, concentration details, and certification information. Provides a list of all available minors.',
      'funding': 'General research grants and project funding opportunities (distinct from student scholarships).',
      'coursecatalog': 'Course descriptions, prerequisites, credits, and class details for all academic offerings. Provides a list of courses.',
      'enrollmentexercise': 'Registration practice simulations.',
      'academicresources': 'Tutoring, libraries, and study support services.',
      'academichonors': 'Dean’s list, honors programs, and GPA requirements for academic distinction.',
      'academicprizes': 'Student achievement awards and competitions.',
      'academicdress': 'Graduation regalia info and ordering.',
      'entryrequirements': 'Detailed admission criteria, prerequisites, and application guidelines. Often related to `admissionsprocess`.',
      'gradingscale': 'Letter grade definitions and GPA calculations.',
      'programs': 'Special academic initiatives, student programs, and partnerships. Provides a list of programs.',
      'signatureevents': 'Major annual campus traditions/ceremonies.',
      'traditions': 'Historical campus customs and rituals.',
      'partnershipopportunities': 'Community/corporate collaboration programs.',
      'athletics': 'Sports teams, schedules, and athlete resources. Lists sports teams.',
      'orgsorclubs': 'Student organizations, club listings, and contact information. Provides a list of clubs.',
      'researchgroups': 'Active academic research teams/projects.',
      'committees': 'Campus governance groups and their functions.',
      'news': 'College news articles and press releases.',
      'periodicals': 'Student-run publications and magazines.',
      'radio': 'Campus radio station programming and staff.',
      'television': 'Student-produced TV shows and content.',
      'photos': 'Campus photo archives and event galleries.',
      'videos': 'Official college videos and student projects.',
      'accelerators': 'Entrepreneurship programs and startup support.',
      'makerspaces': 'Creative labs with equipment/tech resources.',
      'startupfunds': 'Funding opportunities for student ventures.',
      'startups': 'Student-run businesses and their profiles.',
      'researchprojects': 'Ongoing faculty/student research studies.',
      'theses': 'Senior capstone projects and research papers.',
      'books': 'Publications by faculty/alumni.',
      'articles': 'Academic papers and journal contributions.',
      'patents': 'Innovations/IP created at the college.',
      'building': 'Campus building info, maps, and facilities directories. Lists campus buildings.',
      'rooms': 'Classroom/lab specifications and reservations.',
      'roomequipment': 'AV/tech gear available in spaces.',
      'roomassignments': 'Student housing placements.',
      'publicart': 'Campus art installations and exhibits.',
      'emergencyequipment': 'Safety devices and their locations.',
      'classschedules': 'Course times, locations, and instructors.',
      'weeklyschedule': 'Recurring events and meetings.',
      'events': 'Campus activities calendar, event details, and RSVP info. Provides a list of upcoming events.',
      'academiccalendar': 'Term dates, holidays, and academic deadlines.',
      'feedback': 'Student surveys and feedback forms.',
      'historicaltimeline': 'Key moments in college history.',
      'rentals': 'Equipment/space rental options and policies.',
      'rentalequipmentcalendar': 'Reservation schedule for gear.',
      'jobs': 'Campus employment, career opportunities, and job listings. Provides a list of available jobs.',
      'services': 'Overview of student support services, including specific services offered by IT, health, counseling, career centers, etc. This table can list individual services (e.g., "Password Reset", "Wifi Support", "Resume Review") and the department or helpdesk providing them (e.g., "IT Helpdesk", "Career Services"). Useful for "what services does X offer?" type questions or listing all services.',
      'atms': 'On-campus cash machine locations.',
      'clinicsorhospitals': 'Health center services and hours.',
      'counselingservices': 'Mental health resources and appointments.',
      'emergencycontacts': 'Critical phone numbers and protocols.',
      'safetyprocedures': 'Emergency response guidelines.',
      'connectivity': 'WiFi, tech resources, and IT support.',
      'giving': 'Donation opportunities and alumni fundraising.',
  };
 final Map<String, List<String>> _collegeFieldKeywords = {
      'about': ['about', 'overview', 'information', 'general info', 'tell me about the college', 'history', 'background'],
      'address': ['address', 'location', 'located', 'where is', 'find you', 'campus address', 'physical address'],
      'daysnhours': ['hours', 'opening hours', 'closing time', 'open', 'close', 'days open', 'schedule', 'operating hours', 'business hours'],
      'postaladdress': ['postal address', 'mailing address', 'zip code', 'postcode', 'mail to'],
      'mission': ['mission', 'mission statement', 'purpose', 'college aim', 'institutional mission', 'our mission'],
      'vision': ['vision', 'vision statement', 'college aspiration', 'future goals', 'institutional vision', 'our vision'],
      'corevalues': ['values', 'core values', 'principles', 'ethics', 'guiding principles'],
      'motto': ['motto', 'tagline', 'slogan', 'college motto'],
      'goals': ['goals', 'objectives', 'targets', 'aims', 'strategic goals'],
      'mandate': ['mandate', 'authority', 'charge', 'official purpose'],
      'founded': ['founded', 'established', 'since', 'when was it founded', 'history start', 'year founded'],
      'accreditation': ['accreditation', 'accredited', 'certified', 'recognized', 'licensing'],
      'freewifi': ['wifi', 'internet', 'wireless', 'free wifi', 'connect to internet'],
      'objectives': ['objectives', 'aims', 'goals', 'key objectives'],
      'aims': ['aims', 'objectives', 'goals'],
      'pledge': ['pledge', 'commitment', 'promise', 'dedication', 'our pledge'],
      'statementoffaith': ['faith', 'belief', 'statement of faith', 'religious statement', 'creed'],
      'religiousaffiliation': ['religious', 'faith', 'denomination', 'affiliation', 'church associated'],
      'whychooseus': ['why choose', 'why us', 'advantages', 'benefits', 'choose us', 'selling points', 'why attend here', 'unique features'],
      'institutiontype': ['type', 'public', 'private', 'institution type', 'kind of school', 'college type', 'university type'],
      'campussetting': ['campus setting', 'setting', 'urban', 'rural', 'suburban', 'campus environment', 'location type'],
      'highestqualificationoffered': ['qualification', 'degree', 'certificate', 'highest degree', 'level of study', 'programs offered', 'diploma offered'],
      'studentpopulation': ['population', 'students', 'enrollment', 'how many students', 'student body size', 'number of students'],
      'academicyearcalendar': ['academic calendar', 'terms', 'semesters', 'academic year', 'school year schedule', 'term dates', 'session dates'],
      'website': ['website', 'site', 'url', 'web address', 'online', 'homepage', 'official website'],
      'city': ['city', 'town', 'located in which city'],
      'state': ['state', 'region', 'province', 'located in which state'],
      'fullname': ['name', 'full name', 'official name', 'college name'],
      'phone': ['phone', 'number', 'contact number', 'call', 'telephone', 'main phone'],
      'email': ['email', 'email address', 'contact email', 'mail address', 'main email'],
  };

 @override
 void initState() {
   super.initState();
   _speechToText = stt.SpeechToText();
   _flutterTts = FlutterTts();
   _messageController.addListener(() {
      if(mounted) setState(() {});
   });
    WidgetsBinding.instance.addPostFrameCallback((_) {
     _initializeAndCacheData();
   });
 }

 @override
 void dispose() {
   _messageController.removeListener(() { if(mounted) setState(() {}); });
   _messageController.dispose();
   _scrollController.dispose();
   _speechToText.stop();
   _speechToText.cancel();
   _flutterTts.stop();
   super.dispose();
 }

// --- Caching and Initialization Flow ---

/// Main initialization function for the AI Agent page instance.
Future<void> _initializeAndCacheData() async {
    // Start standard UI-related initializations immediately
    await _initSpeech();
    await _configureTts();
    await _loadTtsVoices();
    _addInitialGreeting(); // Shows "Hello! ... please wait while I prepare..."

    // Now, await the static caching method. This will be instant if already completed.
    try {
        await AiAgentPage.prewarmCacheForCollege(widget.collegeData!);
        // If we reach here, the static cache is ready for use.
        if (mounted) {
            setState(() {
                _isCacheReady = true; // Update local state to enable UI interactions
            });
            _showReadyMessage(); // Shows "I am now ready to help..."
        }
    } catch (e) {
        if (mounted) {
            _showError("Failed to load the knowledge base. Please try again later.");
            setState(() {
                _isCacheReady = false; // Ensure UI reflects the failure
            });
        }
    }
}

 Future<void> _initSpeech() async {
   try {
     _speechToText.statusListener = _onSpeechStatusUpdate;
     _speechToText.errorListener = _onSpeechErrorUpdate;

     bool available = await _speechToText.initialize(
       onStatus: (status) {},
       onError: (errorNotification) {
         print('Error during Speech System INITIALIZATION: ${errorNotification.errorMsg}');
         if (mounted) {
           _showError("Failed to initialize speech recognition: ${errorNotification.errorMsg}");
         }
       }
     );

     if (available) {
       print("Speech recognition initialized and listeners assigned.");
     } else if (mounted) {
       print("Speech recognition not available on this device after initialize.");
       _showError("Speech recognition is not available on this device.");
     }
   } catch (e) {
     print("Exception calling speech.initialize: $e");
     if (mounted) {
       _showError("Failed to initialize speech recognition service due to an exception.");
     }
   }
 }

 void _onSpeechStatusUpdate(String status) {
    if (!mounted) return;
    bool newListeningState;
    if (status == stt.SpeechToText.listeningStatus) {
      newListeningState = true;
    } else if (status == stt.SpeechToText.notListeningStatus || status == stt.SpeechToText.doneStatus) {
      newListeningState = false;
    } else {
      return;
    }
    if (_isListening != newListeningState) {
      if (mounted) {
        setState(() { _isListening = newListeningState; });
      }
    }
 }

 void _onSpeechErrorUpdate(SpeechRecognitionError errorNotification) {
    print('Speech Listen Error (from errorListener): ${errorNotification.errorMsg}, permanent: ${errorNotification.permanent}');
    if (mounted) {
      if (_isListening) {
          setState(() => _isListening = false);
      }
      _showError("Speech recognition error: ${errorNotification.errorMsg}");
    }
 }

 Future<void> _configureTts() async {
     await _flutterTts.awaitSpeakCompletion(true);
     await _flutterTts.setVolume(1.0);
     await _flutterTts.setSpeechRate(0.5);
     await _flutterTts.setPitch(1.0);
     if (_selectedVoice != null && mounted) {
       try {
         List<dynamic>? voices = await _flutterTts.getVoices;
         bool voiceExists = voices?.any((v) => v is Map && v['name'] == _selectedVoice!['name'] && v['locale'] == _selectedVoice!['locale']) ?? false;

         if (voiceExists) {
           await _flutterTts.setVoice(_selectedVoice!);
         } else {
           print("Selected voice ' ${_selectedVoice!['name']}' not found or incompatible, attempting to set by language 'en-US'.");
           if(mounted) setState(() => _selectedVoice = null);
           await _flutterTts.setLanguage("en-US");
         }
       } catch (e) {
         print("Error setting TTS voice: $e. Using default 'en-US'.");
         if (mounted) setState(() => _selectedVoice = null);
         await _flutterTts.setLanguage("en-US");
       }
     } else {
       await _flutterTts.setLanguage("en-US");
     }
  }

 Future<void> _loadTtsVoices() async {
     if (!mounted) return;
     try {
       var voices = await _flutterTts.getVoices;
       if (voices != null && voices is List && mounted) {
         List<Map<String, String>> englishVoices = voices
               .map((v) => Map<String, String>.from(v as Map))
               .where((v) => v['locale']?.startsWith('en-') ?? false)
               .sortedBy<String>((v) => v['name'] ?? '')
               .toList();

         setState(() {
           _availableVoices = englishVoices;
           _voicesLoaded = true;
           if (_selectedVoice == null || !_availableVoices.any((v) => v['name'] == _selectedVoice!['name'] && v['locale'] == _selectedVoice!['locale'])) {
             _selectedVoice = _availableVoices.firstWhereOrNull( (v) => v['locale'] == 'en-US' && (v['name']?.toLowerCase().contains('female') ?? false))
                            ?? _availableVoices.firstWhereOrNull((v) => v['locale'] == 'en-US')
                            ?? _availableVoices.firstOrNull;
           }
         });
         await _configureTts();
       }
     } catch (e) {
       print("Error getting TTS voices: $e");
       if (mounted) setState(() => _voicesLoaded = false);
     }
   }

  Future<void> _speakText(String textToSpeak) async {
    if (_isMuted || !mounted || textToSpeak.isEmpty) return;
    try {
      await _flutterTts.stop();
      await _configureTts();
      if (!_isMuted && mounted) {
        await _flutterTts.speak(textToSpeak);
      }
    } catch (e) {
      print("Error during TTS speak operation: $e");
    }
  }

 void _toggleListening() async {
    if (_processingStep != ProcessingStep.idle || !_isCacheReady) return;

    if (_speechToText.isListening) {
      await _speechToText.stop();
    } else {
      if (!await _speechToText.hasPermission) {
        _showError("Speech permission required. Please grant microphone access in settings.");
        return;
      }
      if (!_speechToText.isAvailable) {
          print("STT service not available. Attempting re-initialization.");
          await _initSpeech();
          if (!_speechToText.isAvailable && mounted) {
             _showError("Could not start speech recognition: service unavailable after re-attempt.");
             return;
          }
      }
      if(mounted && _speechToText.isAvailable){
         _startSingleListenSession();
      }
    }
 }

 Future<void> _startSingleListenSession() async {
    if (_speechToText.isListening || _processingStep != ProcessingStep.idle || !mounted) {
       return;
    }
    await _flutterTts.stop();

    _speechToText.listen(
      listenFor: const Duration(seconds: 30),
      pauseFor: const Duration(seconds: 4),
      partialResults: true,
      onResult: (result) {
        if (!mounted) return;
        _messageController.text = result.recognizedWords;
        _messageController.selection = TextSelection.fromPosition(TextPosition(offset: _messageController.text.length));
      },
      listenMode: stt.ListenMode.dictation,
    ).catchError((error, stackTrace) {
       print("Error calling STT listen() method: $error\n$stackTrace");
       if (mounted) {
          _showError("Failed to start speech recognition listening session.");
          if (_isListening) setState(() => _isListening = false);
       }
    });
 }

 void _handleManualInput(String value) {
     if (_speechToText.isListening && _messageController.text.isNotEmpty && mounted) {
        _speechToText.stop();
     }
 }

 Future<void> _sendMessage(String message) async {
   final String userMessageText = message.trim();
   if (userMessageText.isEmpty || _processingStep != ProcessingStep.idle || !_isCacheReady) return;

   if (_speechToText.isListening) {
       await _speechToText.stop();
   }

   String? quickAnswer = _getQuickAnswer(userMessageText);
   if (quickAnswer != null) {
     print("Quick Answer triggered for: '$userMessageText'");
     final approxUserTokens = _calculateApproxTokens(userMessageText);
     final approxResponseTokens = _calculateApproxTokens(quickAnswer);

     if (_dailyTokenCount + approxUserTokens + approxResponseTokens > _maxDailyTokens) {
       _showError("Daily token limit reached. Cannot send message.");
       return;
     }
     if (mounted) _messageController.clear();
     setState(() {
       _messages.add(ChatMessage(text: userMessageText, isUser: true));
       _messages.add(ChatMessage(text: quickAnswer, isUser: false));
       _dailyTokenCount += approxUserTokens + approxResponseTokens;
       _lastPromptTokenCount = approxUserTokens;
       _lastApiResponseTokenCount = approxResponseTokens;
     });
     _scrollToBottom();
     _speakText(quickAnswer);
     return;
   }

   int approxUserMessageTokens = _calculateApproxTokens(userMessageText);
   final userChatMessage = ChatMessage(text: userMessageText, isUser: true);
   setState(() {
     _processingStep = ProcessingStep.processing;
     _isListening = false;
     _messages.add(userChatMessage);
     _lastPromptTokenCount = approxUserMessageTokens;
     _lastApiResponseTokenCount = 0;
   });
   _scrollToBottom();
   if (mounted) _messageController.clear();

   int actualPromptTokensForTableSelection = 0;
   int actualResponseTokensForTableSelection = 0;
   String tableSelectionResponseText = "";

   int actualPromptTokensForFinalResponse = 0;
   int actualResponseTokensForFinalResponse = 0;
   String finalApiResponseText = "";

   List<String> tablesToFetchInitially = [];
   String contextData = "";

   try {
     _addOrUpdateTypingIndicator("Analyzing query...");
     _scrollToBottom();

     final tableSelectionPrompt = _buildTableSelectionPrompt(userMessageText);
     actualPromptTokensForTableSelection = await _countTokensApi(tableSelectionPrompt, _model);
     if(mounted) setState(() => _lastPromptTokenCount = actualPromptTokensForTableSelection);
     if (_dailyTokenCount + actualPromptTokensForTableSelection + APPROX_TABLE_SELECTION_RESPONSE_TOKENS > _maxDailyTokens) {
        throw Exception("Daily token limit would be exceeded by table selection request.");
     }

     _addOrUpdateTypingIndicator("Selecting relevant info...");
     tableSelectionResponseText = await _callGeminiApi(tableSelectionPrompt, isContextBuilding: true);
     if (!mounted) return;

     actualResponseTokensForTableSelection = await _countTokensApi(tableSelectionResponseText, _model);
     if(mounted) setState(() => _lastApiResponseTokenCount = actualResponseTokensForTableSelection);

     tablesToFetchInitially = tableSelectionResponseText
         .split(',')
         .map((t) => t.trim().toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]+'),''))
         .where((t) => t.isNotEmpty && t != 'none' && _tableManifest.containsKey(t))
         .toSet().toList();

     _addOrUpdateTypingIndicator("Building context...");
     contextData = await _getCollegeDataForPrompt(userMessageText, tablesToFetchInitially);
     if (!mounted) return;
     if (contextData.startsWith('No specific college data')) throw Exception("Error loading essential college information.");

     _addOrUpdateTypingIndicator("Generating response...");
     final finalPrompt = _buildFinalPrompt(userMessageText, contextData, _buildConversationHistory());
     actualPromptTokensForFinalResponse = await _countTokensApi(finalPrompt, _model);
     if(mounted) setState(() => _lastPromptTokenCount = actualPromptTokensForFinalResponse);
     if (_dailyTokenCount + actualPromptTokensForTableSelection + actualResponseTokensForTableSelection + actualPromptTokensForFinalResponse + APPROX_FINAL_RESPONSE_TOKENS > _maxDailyTokens) {
        throw Exception("Daily token limit would be exceeded by final response generation request.");
     }

     _addOrUpdateTypingIndicator("Generating final response...");
     finalApiResponseText = await _callGeminiApi(finalPrompt);
     if (!mounted) return;

     actualResponseTokensForFinalResponse = await _countTokensApi(finalApiResponseText, _model);
     if(mounted) setState(() => _lastApiResponseTokenCount = actualResponseTokensForFinalResponse);

     _removeTypingIndicator();
     final aiResponseMessage = ChatMessage(
         text: finalApiResponseText.isEmpty ? "Sorry, I couldn't generate a response based on the available information." : finalApiResponseText,
         isUser: false
     );

     int totalTokensConsumedThisTurn =
         actualPromptTokensForTableSelection +
         actualResponseTokensForTableSelection +
         actualPromptTokensForFinalResponse +
         actualResponseTokensForFinalResponse;

     if (mounted) {
        setState(() {
            _messages.add(aiResponseMessage);
            if (_dailyTokenCount + totalTokensConsumedThisTurn > _maxDailyTokens && _dailyTokenCount < _maxDailyTokens) {
                print("Warning: Daily token limit was exceeded upon final count. Displaying response.");
            }
            _dailyTokenCount += totalTokensConsumedThisTurn;
            _processingStep = ProcessingStep.idle;
        });
     }
     _scrollToBottom();
     _speakText(finalApiResponseText);

   } catch (e, stacktrace) {
     print('Error in multi-step pipeline: $e\n$stacktrace');
     _removeTypingIndicator();
     int tokensConsumedBeforeError = actualPromptTokensForTableSelection + actualResponseTokensForTableSelection + actualPromptTokensForFinalResponse;

     if (mounted && tokensConsumedBeforeError > 0) {
       setState(() {
         _dailyTokenCount += tokensConsumedBeforeError;
       });
     }
     String errorMessageText = e.toString().replaceFirst("Exception: ", "");
     _showError('Sorry, an error occurred: $errorMessageText');
     if (mounted && _processingStep != ProcessingStep.idle) {
        setState(() { _processingStep = ProcessingStep.idle; });
     }
   } finally {
     if (mounted) {
       _removeTypingIndicator();
       if (_processingStep != ProcessingStep.idle) {
         setState(() { _processingStep = ProcessingStep.idle; });
       }
       _scrollToBottom();
     }
   }
 }

  String _buildFinalPrompt(String userQuery, String contextData, String history) {
    final collegeName = widget.collegeData?['fullname'] ?? 'the institution';
    final mainPhoneNumber = widget.collegeData?['phone'] ?? 'the main college phone number';
    String languageInstruction = '';
    if (_outputLanguage != null && _outputLanguage != 'English') {
      languageInstruction = "- IMPORTANT: Respond ONLY in ${_outputLanguage!} language. All your text must be in ${_outputLanguage!}.\n";
    }

    return '''SYSTEM: You are a specialized AI assistant for $collegeName. Answer the user's query based ONLY on the information provided in the 'Provided Context' section below.
Instructions:
$languageInstruction
- The 'Provided Context' contains multiple sources: 'General Info', 'Relevant Specific Information' (from database tables), and a 'Knowledge Base (from PDFs)'.
- **CRITICAL: First, try to answer the query using the structured 'Relevant Specific Information' from the database tables. This is your primary source.**
- **If the answer is incomplete or missing from the database context, then consult the unstructured 'Knowledge Base (from PDFs)' section for relevant information.** You can synthesize information from both sources if the query requires it.
- **Only if the information is not present in EITHER the database context OR the PDF knowledge base context, should you state that the information is unavailable and politely suggest the user call $mainPhoneNumber for further assistance or consult the college website.**
- Format your response for easy readability using clear paragraphs and complete sentences.
- When presenting information that comes from distinct fields about a single entity (e.g., name, phone, email for a department), weave them into a natural sentence or paragraph.
- For lists of multiple distinct items (e.g., a list of scholarships or clubs), use an asterisk (*) at the beginning of each item, with each item on a new line.
- Avoid using any raw markdown formatting such as '##' for headers or '**' for bolding. Plain text output is required.
- Do NOT use external knowledge or make assumptions beyond the provided context. Be concise and directly answer the question.
--- Provided Context ---
$contextData
--- End Provided Context ---
--- Recent Conversation History ---
$history
--- End Conversation History ---
user: $userQuery
model:''';
  }


 String _buildTableSelectionPrompt(String userQuery) {
   final manifestFormatted = _tableManifest.entries
       .map((e) => '- ${e.key}: ${e.value}')
       .join('\n');
   return '''SYSTEM: You are an AI assistant helping to select relevant database tables to answer a user's query about a college.
User Query: "$userQuery"
Available Data Tables (Manifest):
Each line has the format: table_name: description
---
$manifestFormatted
---
Instruction: Based *only* on the User Query and the table descriptions in the manifest, list the short table names (e.g., 'admissionsprocess', 'housing', 'costs') from the manifest that are MOST likely to contain the information needed to answer the query.
- List ONLY the relevant table_names, separated by commas (e.g., housing,costs,mealplans).
- Be liberal in your selection. If a table seems even partially relevant, include it.
- If no specific table seems relevant based on the query and descriptions, respond with the single word 'NONE'.
- Do NOT add any explanation, preamble, or concluding text. Just provide the comma-separated list or 'NONE'.
model:''';
 }

 String? _getQuickAnswer(String query) {
     if (widget.collegeData == null) return null;
     final lowerQuery = query.toLowerCase();
     String? answer;

     if (RegExp(r'^\b(hi|hello|hey|hallo|good morning|good afternoon)\b').hasMatch(lowerQuery)) {
       String collegeName = widget.collegeData?['fullname'] ?? 'the institution';
       answer = "Hello! I'm the AI assistant for $collegeName. How can I help you today?";
     }
     else if (RegExp(r'\b(website|site|url|web address|homepage)\b').hasMatch(lowerQuery)) {
         answer = widget.collegeData!['website'] != null
             ? "The college website is: ${widget.collegeData!['website']}"
             : null;
     }
     else if (RegExp(r'\b(phone|contact number|call|telephone)\b').hasMatch(lowerQuery) &&
         !lowerQuery.contains("department") && !lowerQuery.contains("office") && !lowerQuery.contains("faculty") && !lowerQuery.contains("staff") && !lowerQuery.contains("helpdesk")) {
         answer = widget.collegeData!['phone'] != null
             ? "The main phone number for the college is: ${widget.collegeData!['phone']}"
             : null;
     }
     else if (RegExp(r'\b(email|email address|contact email)\b').hasMatch(lowerQuery) &&
          !lowerQuery.contains("department") && !lowerQuery.contains("office") && !lowerQuery.contains("faculty") && !lowerQuery.contains("staff") && !lowerQuery.contains("helpdesk")) {
         answer = widget.collegeData!['email'] != null
             ? "The main contact email for the college is: ${widget.collegeData!['email']}"
             : null;
     }
     else if (RegExp(r'\b(address|location|located|where is|find you|campus address)\b').hasMatch(lowerQuery)) {
         String? address = widget.collegeData!['address']?.toString();
         String? city = widget.collegeData!['city']?.toString();
         String? state = widget.collegeData!['state']?.toString();
         List<String?> parts = [address, city, state];
         List<String> validParts = parts.whereNotNull().where((s) => s.trim().isNotEmpty).toList();
         answer = validParts.isNotEmpty
             ? "The college address is: ${validParts.join(', ')}"
             : null;
     }
     if (answer != null && _outputLanguage != null && _outputLanguage != 'English') {
       answer = "$answer\n\n(Note: This would be translated to $_outputLanguage in a full implementation)";
     }
     return answer;
 }

  /// This function now reads from the static cache instead of fetching live data.
  Future<String> _getCollegeDataForPrompt(String userQuery, List<String> geminiSuggestedTables) async {
    if (!mounted) return 'Component unmounted';
    // Use the static cache status check
    if (!AiAgentPage._sIsCacheReady) {
      await AiAgentPage.prewarmCacheForCollege(widget.collegeData!);
      if (!AiAgentPage._sIsCacheReady) {
        return "Error: Data cache is not available.";
      }
    }

    String collegeName = widget.collegeData!['fullname']!;
    final sb = StringBuffer();

    // 1. Add General College Info from widget data
    sb.writeln('## General Info ($collegeName):');
    bool baseDataAdded = false;
    List<String> essentialFields = [ 'fullname', 'about', 'mission', 'vision', 'website', 'phone', 'email', 'address' ];
    for (String field in essentialFields) {
      if (widget.collegeData!.containsKey(field) && widget.collegeData![field] != null) {
        final value = widget.collegeData![field]?.toString().trim() ?? '';
        if (value.isNotEmpty) {
          sb.write('- ${field.readableFieldName()}: $value\n');
          baseDataAdded = true;
        }
      }
    }
    if (!baseDataAdded) sb.writeln("(No general details found)");
    sb.writeln('---');

    // 2. Add relevant table data FROM STATIC CACHE
    sb.writeln('\n## Relevant Specific Information (Data from Database):');
    bool specificDataAdded = false;
    Set<String> finalTablesToInclude = Set<String>.from(geminiSuggestedTables);
    finalTablesToInclude.addAll(_alwaysConsiderTables);

    for (String tableName in finalTablesToInclude) {
      if (AiAgentPage._sDbCache.containsKey(tableName)) {
        final data = AiAgentPage._sDbCache[tableName]!;
        final sectionTitle = tableName.readableFieldName();
        sb.writeln('\n### From $sectionTitle Data:\n$data\n---');
        specificDataAdded = true;
      }
    }
    if (!specificDataAdded) {
      sb.writeln("(No specific data found in the cache for the query).");
    }

    // 3. Add knowledge base data FROM STATIC CACHE
    sb.writeln('\n## Knowledge Base (from PDFs):');
    if (AiAgentPage._sKnowledgeBaseCache != null && AiAgentPage._sKnowledgeBaseCache!.isNotEmpty) {
      sb.writeln(AiAgentPage._sKnowledgeBaseCache);
    } else {
      sb.writeln("(No information found in the PDF knowledge base).");
    }
    sb.writeln('---');

    print("Context built from cache. Chars: ${sb.length}. Approx Tokens: ${_calculateApproxTokens(sb.toString())}");
    return sb.toString();
  }


  Future<String> _callGeminiApi(String promptText, {bool isContextBuilding = false}) async {
    if (_apiKey == 'YOUR_GEMINI_API_KEY' || _apiKey.length < 20) {
      throw Exception("Configuration error: Invalid API key.");
    }
    final modelToUse = _model.startsWith('models/') ? _model.split('/').last : _model;
    final url = Uri.parse('https://generativelanguage.googleapis.com/v1beta/models/$modelToUse:generateContent?key=$_apiKey');

    final generationConfig = {
      'temperature': isContextBuilding ? 0.1 : 0.6,
      'topP': 0.95,
      'topK': 40,
      'maxOutputTokens': isContextBuilding ? 256 : 8192,
    };
    final body = jsonEncode({
      'contents': [{'role': 'user', 'parts': [{'text': promptText}]}],
      'generationConfig': generationConfig,
      'safetySettings': [
        {'category': 'HARM_CATEGORY_HARASSMENT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_HATE_SPEECH', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'}
      ]
    });

    try {
       final response = await http.post(url, headers: {'Content-Type': 'application/json'}, body: body)
           .timeout(const Duration(seconds: 180));

       final decoded = jsonDecode(response.body);

       if (response.statusCode == 200) {
          if (decoded['promptFeedback'] != null && decoded['promptFeedback']['blockReason'] != null) {
            final reason = decoded['promptFeedback']['blockReason'];
            throw Exception("Request blocked by AI due to input content (Reason: $reason). Please rephrase your query.");
          }
          final candidates = decoded['candidates'];
          if (candidates != null && candidates.isNotEmpty) {
             final candidate = candidates[0];
             final finishReason = candidate['finishReason'];
             if (finishReason == 'SAFETY') {
                 throw Exception("Response blocked by AI due to output content (Safety).");
             }
             final content = candidate['content'];
             if (content != null && content['parts'] != null && (content['parts'] as List).isNotEmpty) {
               return content['parts'][0]['text']?.trim() ?? '';
             }
             return '';
          }
          throw Exception("No valid response generated by AI.");
       } else {
          String errorMessage = 'AI API request failed (Status: ${response.statusCode}).';
          final errorDetails = decoded['error'] as Map<String, dynamic>?;
          if (errorDetails != null && errorDetails['message'] != null) {
            errorMessage += ' Error: ${errorDetails['message']}';
          }
          throw Exception(errorMessage);
       }
    } on TimeoutException catch (_) {
        throw Exception("Request to AI service timed out. Please try again.");
    } on SocketException catch (_) {
        throw Exception("Network error: Could not connect to AI service.");
    } catch (e) {
      throw Exception("An unexpected error occurred while communicating with the AI: ${e.toString()}");
    }
  }

  Future<int> _countTokensApi(String textToCount, String modelName) async {
    if (textToCount.isEmpty) return 0;
    if (_apiKey == 'YOUR_GEMINI_API_KEY' || _apiKey.length < 20) {
      return _calculateApproxTokens(textToCount);
    }

    final modelId = modelName.startsWith('models/') ? modelName.split('/').last : modelName;
    final uri = Uri.https(
      'generativelanguage.googleapis.com',
      '/v1beta/models/$modelId:countTokens',
      {'key': _apiKey},
    );

    try {
      final response = await http.post(
        uri,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
            'contents': [{'role': 'user', 'parts': [{'text': textToCount}]}]
        }),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        final tokenCount = data['totalTokens'] as int?;
        return tokenCount ?? _calculateApproxTokens(textToCount);
      } else {
        return _calculateApproxTokens(textToCount);
      }
    } catch (e) {
      return _calculateApproxTokens(textToCount);
    }
  }


 @override
 Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final iconColor = isDark ? Colors.white70 : Colors.black87;
    String appBarTitle = 'College AI Assistant';
    if (widget.collegeData?['fullname']?.isNotEmpty == true) {
      appBarTitle = "${widget.collegeData!['fullname']} AI";
    }
    final bool tokenLimitReached = _dailyTokenCount >= _maxDailyTokens;
    final bool canInteractGenerally = _processingStep == ProcessingStep.idle && !tokenLimitReached && _isCacheReady;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        elevation: 1.0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: iconColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          appBarTitle,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 0.0),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _outputLanguage,
                hint: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Icon(Icons.language, color: iconColor.withOpacity(0.7), size: 20)
                ),
                icon: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Icon(Icons.arrow_drop_down, color: iconColor.withOpacity(0.7), size: 20)
                ),
                selectedItemBuilder: (context) => [
                  'English', 'Chichewa', 'Chitumbuka', 'Swahili', 'Shona', 'Zulu'
                ].map((_) => Center(
                  child: Tooltip(
                    message: "Output Language: $_outputLanguage",
                    child: Icon(Icons.language, color: iconColor.withOpacity(0.7), size: 20)
                  )
                )).toList(),
                items: [
                  DropdownMenuItem(value: 'English', child: Text('English')),
                  DropdownMenuItem(value: 'Chichewa', child: Text('Chichewa')),
                  DropdownMenuItem(value: 'Chitumbuka', child: Text('Chitumbuka')),
                  DropdownMenuItem(value: 'Swahili', child: Text('Swahili')),
                  DropdownMenuItem(value: 'Shona', child: Text('Shona')),
                  DropdownMenuItem(value: 'Zulu', child: Text('Zulu')),
                ],
                onChanged: (_processingStep == ProcessingStep.idle)
                  ? (String? newValue) {
                      if (newValue != null && mounted) {
                        setState(() => _outputLanguage = newValue);
                      }
                    }
                  : null,
                style: TextStyle(color: theme.colorScheme.onSurface),
                dropdownColor: theme.colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
                elevation: 4,
              ),
            ),
          ),
          if (_voicesLoaded && _availableVoices.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(right: 0.0),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<Map<String, String>>(
                  value: _selectedVoice,
                  hint: Padding(padding: const EdgeInsets.symmetric(horizontal: 8.0), child: Icon(Icons.record_voice_over, color: iconColor.withOpacity(0.7), size: 20)),
                  icon: Padding(padding: const EdgeInsets.symmetric(horizontal: 8.0), child: Icon(Icons.arrow_drop_down, color: iconColor.withOpacity(0.7), size: 20)),
                  selectedItemBuilder: (context) => _availableVoices
                      .map((_) => Center(
                            child: Tooltip(
                                message: _selectedVoice != null ? "${_selectedVoice!['name']} (${_selectedVoice!['locale']})" : "Select Voice",
                                child: Icon(Icons.record_voice_over, color: iconColor.withOpacity(0.7), size: 20)
                             )
                          )).toList(),
                  items: _availableVoices
                      .map((voice) => DropdownMenuItem<Map<String, String>>(
                          value: voice,
                          child: Tooltip(
                              message: "${voice['name']} (${voice['locale']})",
                              child: SizedBox(
                                width: 180,
                                child: Text("${voice['name']} (${voice['locale']})",
                                    style: TextStyle(fontSize: 12, color: theme.colorScheme.onSurfaceVariant),
                                    overflow: TextOverflow.ellipsis)
                              )
                          )
                       )).toList(),
                  onChanged: (_processingStep == ProcessingStep.idle)
                      ? (Map<String, String>? newValue) {
                          if (newValue != null && mounted) {
                            setState(() => _selectedVoice = newValue);
                            _configureTts();
                          }
                        }
                      : null,
                  style: TextStyle(color: theme.colorScheme.onSurface),
                  dropdownColor: theme.colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(8),
                  elevation: 4,
                ),
              ),
            ),
          IconButton(
            icon: Icon(Icons.refresh, color: (_processingStep == ProcessingStep.idle) ? iconColor : theme.disabledColor),
            tooltip: "Start New Conversation",
            onPressed: (_processingStep == ProcessingStep.idle) ? _startNewConversation : null,
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                itemCount: _messages.length,
                padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 8.0),
                itemBuilder: (context, index) {
                  final msg = _messages[index];
                  return ChatBubble(
                    key: ValueKey("msg-${msg.hashCode}-${index}"),
                    message: msg,
                    isDarkMode: isDark,
                    theme: theme,
                  );
                },
              ),
            ),
            _buildInputAreaReverted(theme),
          ],
        ),
      ),
    );
 }

 Widget _buildInputAreaReverted(ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;
    final iconColor = isDark ? Colors.white70 : Colors.black87;
    final sendProgressColor = isDark ? Colors.white : Colors.black;
    final bool tokenLimitReached = _dailyTokenCount >= _maxDailyTokens;
    final bool canSendOrListen = _processingStep == ProcessingStep.idle && !tokenLimitReached && _isCacheReady;
    final bool inputFieldEnabled = canSendOrListen || _isListening;

    String hintText;
    if (!_isCacheReady) {
      hintText = 'Initializing AI, please wait...';
    } else if (_processingStep != ProcessingStep.idle) {
       final typingMsg = _messages.lastWhereOrNull((m) => m.isTyping);
       hintText = typingMsg?.text ?? "Processing...";
    } else if (_isListening) {
       hintText = 'Listening... Speak now';
    } else if (tokenLimitReached){
       hintText = 'Daily token limit reached';
    } else {
       hintText = 'Ask a question...';
    }

    final threadMetrics = _calculateThreadMetrics();
    double threadCostMkw = (threadMetrics['totalCost'] ?? 0.0) * _exchangeRate;

    return Container(
      padding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 10.0),
      margin: const EdgeInsets.only(bottom: 4.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
              offset: const Offset(0, -1),
              blurRadius: 3,
              color: Colors.black.withOpacity(0.08))
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_isCacheReady && (_messageController.text.isNotEmpty || (threadMetrics['totalTokens'] ?? 0) > 0))
            Padding(
              padding: const EdgeInsets.only(bottom: 6.0, left: 8.0, right: 8.0),
              child: DefaultTextStyle(
                 style: TextStyle(
                     color: theme.colorScheme.onSurface.withOpacity(0.7),
                     fontSize: 10.5,
                 ),
                 child: Wrap(
                    spacing: 8.0,
                    runSpacing: 2.0,
                    children: [
                       ValueListenableBuilder<TextEditingValue>(
                          valueListenable: _messageController,
                          builder: (context, value, child) {
                             int approxTokens = _calculateApproxTokens(value.text);
                             if (approxTokens > 0) {
                               double cost = (approxTokens / 1000.0) * _inputCostPer1kTokens;
                               double costMkw = cost * _exchangeRate;
                               return Text("Msg (est): ~${approxTokens}t (\$${cost.toStringAsFixed(5)}/MKW${costMkw.toStringAsFixed(3)})");
                             }
                             return const SizedBox.shrink();
                          },
                       ),
                       if (_lastPromptTokenCount > 0) Text("Last Prompt: ${_lastPromptTokenCount}t"),
                       if (_lastApiResponseTokenCount > 0) Text("Last Resp: ${_lastApiResponseTokenCount}t"),
                       if ((threadMetrics['totalTokens'] ?? 0) > 0) Text("Thread (est): ~${threadMetrics['totalTokens']}t (\$${threadMetrics['totalCost'].toStringAsFixed(5)}/MKW${threadCostMkw.toStringAsFixed(3)})"),
                       Text(
                          "Daily Tokens: ${_dailyTokenCount}/${_maxDailyTokens}",
                          style: TextStyle(
                             color: tokenLimitReached ? Colors.redAccent : theme.colorScheme.onSurface.withOpacity(0.7),
                             fontWeight: tokenLimitReached ? FontWeight.bold : FontWeight.normal,
                          ),
                       ),
                    ],
                 ),
              ),
            ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: TextField(
                  controller: _messageController,
                  onChanged: _handleManualInput,
                  onTap: () {
                     if (_isListening && mounted && canSendOrListen) {
                        _speechToText.stop();
                     }
                  },
                  maxLines: 1,
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.send,
                  decoration: InputDecoration(
                    hintText: hintText,
                    hintStyle: TextStyle(
                      color: !canSendOrListen
                          ? theme.disabledColor.withOpacity(0.6)
                          : (_isListening
                             ? iconColor.withOpacity(0.9)
                             : theme.hintColor.withOpacity(0.6)),
                    ),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
                  ),
                  style: TextStyle(
                    color: inputFieldEnabled
                        ? theme.colorScheme.onSurface
                        : theme.disabledColor,
                    fontSize: 15,
                  ),
                  textCapitalization: TextCapitalization.sentences,
                  onSubmitted: (value) {
                    if (canSendOrListen && value.trim().isNotEmpty) {
                      _sendMessage(value);
                    }
                  },
                  enabled: inputFieldEnabled,
                  cursorColor: inputFieldEnabled ? theme.colorScheme.primary : Colors.transparent,
                ),
              ),
              IconButton(
                 icon: Icon(
                   _isListening ? Icons.mic : Icons.mic_none,
                   color: canSendOrListen ? iconColor : theme.disabledColor,
                 ),
                 tooltip: _isListening ? "Stop Listening" : (canSendOrListen ? "Start Listening" : "Listening disabled"),
                 visualDensity: VisualDensity.compact,
                 splashRadius: 20,
                 onPressed: canSendOrListen ? _toggleListening : null,
              ),
              (_processingStep != ProcessingStep.idle || !_isCacheReady)
                  ? Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: SizedBox(
                        width: 24, height: 24,
                        child: CircularProgressIndicator( strokeWidth: 2.5, color: sendProgressColor, ),
                      ),
                    )
                  : IconButton(
                      icon: Icon(
                        Icons.send,
                        color: (_messageController.text.trim().isEmpty || !canSendOrListen)
                            ? theme.disabledColor
                            : sendProgressColor,
                      ),
                      tooltip: "Send Message",
                      visualDensity: VisualDensity.compact,
                      splashRadius: 20,
                      onPressed: (_messageController.text.trim().isEmpty || !canSendOrListen)
                          ? null
                          : () => _sendMessage(_messageController.text),
                    ),
              IconButton(
                 icon: Icon(
                    _isMuted ? Icons.volume_off_outlined : Icons.volume_up_outlined,
                    color: iconColor,
                    size: 22,
                 ),
                 visualDensity: VisualDensity.compact,
                 splashRadius: 20,
                 tooltip: _isMuted ? "Unmute TTS" : "Mute TTS",
                 onPressed: () {
                    if (mounted) {
                       setState(() => _isMuted = !_isMuted);
                       if (_isMuted) _flutterTts.stop();
                    }
                 },
              ),
            ],
          ),
        ],
      ),
    );
 }


 Map<String, dynamic> _calculateThreadMetrics() {
     int currentInputTokensApproximation = 0;
     int currentOutputTokensApproximation = 0;

     for (int i = 0; i < _messages.length; i++) {
       final msg = _messages[i];
       if (!msg.isTyping && msg.text.trim().isNotEmpty) {
         int approxMsgTokens = _calculateApproxTokens(msg.text);
         bool isInitialGreeting = i == 0 && !msg.isUser;
         if (msg.isUser) {
           currentInputTokensApproximation += approxMsgTokens;
         } else if (!isInitialGreeting) {
           currentOutputTokensApproximation += approxMsgTokens;
         }
       }
     }
     double inCost = (currentInputTokensApproximation / 1000.0) * _inputCostPer1kTokens;
     double outCost = (currentOutputTokensApproximation / 1000.0) * _outputCostPer1kTokens;

     return {
       'totalTokens': currentInputTokensApproximation + currentOutputTokensApproximation,
       'totalCost': inCost + outCost,
     };
 }

 void _addOrUpdateTypingIndicator(String text) {
   if (!mounted) return;
   _messages.removeWhere((m) => m.isTyping);
   final newIndicator = ChatMessage(text: text, isUser: false, isTyping: true);
   _messages.add(newIndicator);
   setState(() {});
   _scrollToBottom();
 }

 void _removeTypingIndicator() {
   if (!mounted) return;
   final initialLength = _messages.length;
   _messages.removeWhere((m) => m.isTyping);
   if (_messages.length < initialLength) {
     setState(() {});
   }
 }

 String _buildConversationHistory() {
    List<String> orderedHistorySegments = [];
    int currentHistoryTokens = 0;
    const int maxHistoryTokens = 1800;
    const int maxHistoryMessages = 12;

    List<ChatMessage> eligibleMessages = _messages.where((m) => !m.isTyping && m.text.trim().isNotEmpty).toList();

    if (eligibleMessages.length > maxHistoryMessages) {
      eligibleMessages = eligibleMessages.sublist(eligibleMessages.length - maxHistoryMessages);
    }

    for (final msg in eligibleMessages) {
      // Exclude initial and "ready" messages
      bool isSystemMessage = _messages.indexOf(msg) < 2 && !msg.isUser;
      bool isCurrentUserMessageBeingProcessed = msg.isUser && _messages.lastWhereOrNull((m) => m.isUser && !m.isTyping) == msg && _processingStep == ProcessingStep.processing;

      if (!isSystemMessage && !isCurrentUserMessageBeingProcessed) {
        String role = msg.isUser ? 'user' : 'model';
        String formattedMsg = '$role: ${msg.text}\n';
        int msgTokens = _calculateApproxTokens(formattedMsg);
        if (currentHistoryTokens + msgTokens <= maxHistoryTokens) {
          orderedHistorySegments.add(formattedMsg);
          currentHistoryTokens += msgTokens;
        } else {
          break;
        }
      }
    }
    return orderedHistorySegments.join();
 }

 void _addInitialGreeting() {
     String collegeName = widget.collegeData?['fullname']?.toString().isNotEmpty == true
         ? widget.collegeData!['fullname']!
         : 'the institution';
     String greeting = "Hello! I'm the AI assistant for $collegeName. Please wait a moment while I prepare my knowledge base.";
     final initialMessage = ChatMessage(text: greeting, isUser: false);
     if(mounted){
       setState(() {
           if (_messages.isEmpty) {
             _messages.add(initialMessage);
           }
        });
       _speakText(greeting);
     }
 }

 void _showReadyMessage() {
   const readyMessageText = "I have finished preparing and am now ready to help. How can I assist you?";
   final readyMessage = ChatMessage(text: readyMessageText, isUser: false);
   if (mounted) {
     if (_messages.isEmpty || _messages.last.text != readyMessageText) {
       setState(() {
         // Replace the "initializing" message if it exists
         if (_messages.isNotEmpty && _messages.first.text.contains("prepare my knowledge base")) {
           _messages[0] = readyMessage;
         } else {
           _messages.add(readyMessage);
         }
       });
     }
     _scrollToBottom();
     _speakText(readyMessageText);
   }
 }

 void _startNewConversation() {
     if(_processingStep != ProcessingStep.idle && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
           const SnackBar(content: Text("Please wait for the current response to complete."), duration: Duration(seconds: 2))
        );
        return;
     }
     if (mounted) {
       if (_speechToText.isListening) {
           _speechToText.stop();
       }
       _flutterTts.stop();

       setState(() {
           _messages.clear();
           _lastPromptTokenCount = 0;
           _lastApiResponseTokenCount = 0;
           _processingStep = ProcessingStep.idle;
           _isListening = false;
       });
       _showReadyMessage();
     }
 }

 void _showError(String errorMessageText) {
     final errorMsg = ChatMessage(text: "Error: $errorMessageText", isUser: false);
     if (mounted) {
       _removeTypingIndicator();
       setState(() {
            if (_messages.isEmpty || _messages.last.text != errorMsg.text) {
                _messages.add(errorMsg);
            }
            if (_processingStep != ProcessingStep.idle) {
                _processingStep = ProcessingStep.idle;
            }
       });
       _scrollToBottom();
       _speakText(errorMessageText);
     }
 }

 void _scrollToBottom() {
     WidgetsBinding.instance.addPostFrameCallback((_) {
         if (_scrollController.hasClients && mounted && _scrollController.position.hasContentDimensions) {
             _scrollController.animateTo(
                 _scrollController.position.maxScrollExtent,
                 duration: const Duration(milliseconds: 300),
                 curve: Curves.easeOut);
         }
     });
 }

 List<String> _getRelevantCollegeFields(String query, {int max = 7}) {
      final lowerQuery = query.toLowerCase().replaceAll(RegExp(r'[^\w\s]+'), '');
      final scores = <String, int>{};
      scores['fullname'] = 100; scores['about'] = 50; scores['website'] = 50;
      scores['address'] = 40; scores['city'] = 40; scores['state'] = 40;
      scores['phone'] = 40; scores['email'] = 40; scores['mission'] = 30;
      scores['vision'] = 30; scores['motto'] = 25; scores['daysnhours'] = 25;
      scores['institutiontype'] = 20; scores['accreditation'] = 20;
      scores['founded'] = 15; scores['studentpopulation'] = 15;

      _collegeFieldKeywords.forEach((field, keywords) {
        int currentScore = scores[field] ?? 0;
        for (var kw in keywords) {
          if (lowerQuery.contains(kw)) {
             currentScore += (kw.contains(' ') ? 4 : 2);
             if (RegExp(r'\b' + RegExp.escape(kw) + r'\b').hasMatch(lowerQuery)) {
                currentScore += 6;
             }
          }
        }
        if (RegExp(r'\b' + RegExp.escape(field.toLowerCase()) + r'\b', caseSensitive: false).hasMatch(lowerQuery)) {
           currentScore += 12;
        }
        scores[field] = currentScore;
      });

      final scoredFields = scores.keys.where((key) => scores[key]! > 0).toList();
      scoredFields.sort((a, b) => scores[b]!.compareTo(scores[a]!));

      return scoredFields.take(max).toList();
 }

  int _calculateApproxTokens(String text) {
    if (text.isEmpty) return 0;
    return (text.trim().length / APPROX_CHARS_PER_TOKEN).ceil();
  }
}