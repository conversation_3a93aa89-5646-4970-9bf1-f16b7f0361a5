import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class LinksDetailPage extends StatefulWidget {
  final Map<String, dynamic> link;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;

  const LinksDetailPage({
    Key? key,
    required this.link,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
  }) : super(key: key);

  @override
  State<LinksDetailPage> createState() => _LinksDetailPageState();
}

class _LinksDetailPageState extends State<LinksDetailPage> {
  late RealtimeChannel _linkRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _setupLinkRealtimeListener();
  }

  @override
  void dispose() {
    _linkRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupLinkRealtimeListener() {
    final table =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_links';
    _linkRealtimeChannel = Supabase.instance.client
        .channel('link_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.update,
      schema: 'public',
      table: table,
      callback: (payload) {
        if (payload.newRecord['id'] == widget.link['id']) {
          _fetchUpdatedLinkData();
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedLinkData() async {
    final table =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_links';
    try {
      final updated = await Supabase.instance.client
          .from(table)
          .select('*')
          .eq('id', widget.link['id'])
          .single();
      if (mounted && updated != null) {
        setState(() {
          widget.link
            ..clear()
            ..addAll(Map<String, dynamic>.from(updated));
        });
        _updateLinksCache(updated);
      }
    } catch (e) {
      print('Error fetching updated link data: $e');
    }
  }

  Future<void> _updateLinksCache(Map<String, dynamic> updated) async {
    final prefs = await SharedPreferences.getInstance();
    final key =
        'links_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    final jsonStr = prefs.getString(key);
    if (jsonStr != null) {
      final list = (jsonDecode(jsonStr) as List)
          .cast<Map<String, dynamic>>();
      for (var i = 0; i < list.length; i++) {
        if (list[i]['id'] == updated['id']) {
          list[i] = updated;
          break;
        }
      }
      await prefs.setString(key, jsonEncode(list));
    }
  }

  // --- MODIFIED FUNCTION ---
  // This now matches the TertiaryInfoPage's URL opening behavior.
  Future<void> _launchResourceLink() async {
    final urlStr = widget.link['link'] as String? ?? '';
    if (urlStr.isEmpty) {
      _showSnackbar('No resource link available.');
      return;
    }
    
    // Using tryParse is safer as it won't throw an exception on a malformed URL.
    final uri = Uri.tryParse(urlStr);
    
    // Check if the URI is valid and can be launched.
    if (uri != null && await canLaunchUrl(uri)) {
      // By REMOVING the `mode` parameter, we use the platform default,
      // which is typically a smooth in-app browser experience.
      await launchUrl(uri);
    } else {
      _showSnackbar('Could not launch resource link.');
    }
  }

  void _showSnackbar(String msg) {
    if (!mounted) return;
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(content: Text(msg)));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final link = widget.link;

    final title = link['fullname'] as String? ?? '';
    final about = link['about'] as String? ?? '';
    final urlStr = link['link'] as String? ?? '';
    final hasUrl = urlStr.isNotEmpty;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 200,
              color: theme.colorScheme.surfaceVariant,
              child: Center(
                child: Icon(
                  Icons.link_rounded,
                  size: 80,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CircleAvatar(
                                radius: 30,
                                backgroundColor: isDark
                                    ? Colors.white.withOpacity(0.1)
                                    : Colors.black.withOpacity(0.1),
                                child: Icon(
                                  Icons.link_rounded,
                                  size: 30,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      title.isNotEmpty ? title : 'Unnamed Link',
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          if (hasUrl) 
                            _buildDetailRow(
                              theme, 
                              Icons.link, 
                              'URL', 
                              urlStr, 
                              canCopy: true, 
                              onTap: _launchResourceLink,
                            ),
                          if (about.isNotEmpty) ...[
                            const SizedBox(height: 16),
                            _buildSectionTitle(theme, Icons.info_outline, 'About'),
                            const SizedBox(height: 8),
                            Text(
                              about,
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: ElevatedButton.icon(
              icon: const Icon(Icons.open_in_new),
              label: const Text('Open Link'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: hasUrl ? _launchResourceLink : null,
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value, {bool canCopy = false, VoidCallback? onTap}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    final bool isClickable = onTap != null;

    Widget content = Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: theme.colorScheme.onSurface,
          size: 20,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      value.toString(),
                      style: TextStyle(
                        color: isClickable ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                        decoration: isClickable ? TextDecoration.underline : TextDecoration.none,
                      ),
                    ),
                  ),
                  if (canCopy)
                    SizedBox(
                      width: 30,
                      height: 30,
                      child: IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy $title',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ],
    );

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: isClickable
          ? InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(8.0),
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: content,
              ),
            )
          : content,
    );
  }

  Widget _buildSectionTitle(ThemeData theme, IconData icon, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, color: theme.colorScheme.onSurface, size: 20),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}