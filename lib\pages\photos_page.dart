// photos_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

import 'login_page.dart';
import 'photo_detail_page.dart';

class PhotosPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedPhotos;
  final bool isFromDetailPage;

  const PhotosPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedPhotos,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<PhotosPage> createState() => _PhotosPageState();
}

class _PhotosPageState extends State<PhotosPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('photos_list');
  List<Map<String, dynamic>> _photos = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("PhotosPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant PhotosPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("PhotosPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("PhotosPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedPhotos != null && widget.preloadedPhotos!.isNotEmpty) {
      print("Preloaded photos found, using them.");
      setState(() {
        _photos = List<Map<String, dynamic>>.from(widget.preloadedPhotos!);
        _photos.forEach((photo) {
          photo['_isImageLoading'] = false;
        });
        _sortPhotos();
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded photos or empty list, loading from database.");
      await _loadPhotosFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final photosTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_photos';
    
    try {
      final response = await Supabase.instance.client
          .from(photosTableName)
          .select('id')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(_photos.length, _photos.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadPhotosFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadPhotosFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final photosTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_photos';

    try {
      int startRange = initialLoad ? 0 : _photos.length;
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(photosTableName)
          .select('*')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(startRange, endRange);

      final updatedPhotos =
          await _updatePhotoImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _photos = updatedPhotos;
          } else {
            _photos.addAll(updatedPhotos);
          }
          _photos.forEach((photo) {
            photo['_isImageLoading'] = false;
          });
          _sortPhotos();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
      _cachePhotos(_photos);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching photos: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updatePhotoImageUrls(
      List<Map<String, dynamic>> photos) async {
    List<Future<void>> futures = [];
    for (final photo in photos) {
      if (photo['image_url'] == null ||
          photo['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(photo));
      }
    }
    await Future.wait(futures);
    return photos;
  }
  
  void _setupRealtime() {
    final photosTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_photos';
    _realtimeChannel = Supabase.instance.client
        .channel('photos')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: photosTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newPhotoId = payload.newRecord['id'];
          final newPhotoResponse = await Supabase.instance.client
              .from(photosTableName)
              .select('*')
              .eq('id', newPhotoId)
              .single();
          if (mounted) {
            Map<String, dynamic> newPhoto = Map.from(newPhotoResponse);
            final updatedPhoto = await _updatePhotoImageUrls([newPhoto]);
            setState(() {
              _photos.add(updatedPhoto.first);
              updatedPhoto.first['_isImageLoading'] = false;
              _sortPhotos();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedPhotoId = payload.newRecord['id'];
          final updatedPhotoResponse = await Supabase.instance.client
              .from(photosTableName)
              .select('*')
              .eq('id', updatedPhotoId)
              .single();
          if (mounted) {
            final updatedPhoto = Map<String, dynamic>.from(updatedPhotoResponse);
            setState(() {
              _photos = _photos.map((photo) {
                return photo['id'] == updatedPhoto['id'] ? updatedPhoto : photo;
              }).toList();
              _sortPhotos();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedPhotoId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _photos.removeWhere((photo) => photo['id'] == deletedPhotoId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("PhotosPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMorePhotos();
    }
  }

  Future<void> _loadMorePhotos() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more photos...");
      await _loadPhotosFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _sortPhotos() {
    _photos.sort((a, b) {
      final aYear = a['year'] ?? 0;
      final bYear = b['year'] ?? 0;
      if (aYear != bYear) return bYear.compareTo(aYear);
      
      final aMonth = a['month'] ?? 0;
      final bMonth = b['month'] ?? 0;
      if (aMonth != bMonth) return bMonth.compareTo(aMonth);
      
      final aDay = a['day'] ?? 0;
      final bDay = b['day'] ?? 0;
      return bDay.compareTo(aDay);
    });
  }

  Future<void> _cachePhotos(List<Map<String, dynamic>> photos) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String photosJson = jsonEncode(photos);
      await prefs.setString(
          'photos_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          photosJson);
    } catch (e) {
      print('Error caching photos: $e');
    }
  }
  
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> photo) async {
    if (photo['_isImageLoading'] == true) return;
    if (photo['image_url'] != null && photo['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => photo['_isImageLoading'] = true);

    final fullname = photo['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegePhotoBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/photos';

    try {
      final file = await Supabase.instance.client.storage.from(collegePhotoBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegePhotoBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        photo['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        photo['_isImageLoading'] = false;
      });
    } else {
      photo['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> photo) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PhotoDetailPage(
            photo: photo,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  String _formatDate(Map<String, dynamic> photo) {
    final day = photo['day'] as int?;
    final month = photo['month'] as int?;
    final year = photo['year'] as int?;
    
    if (day == null || month == null || year == null) {
      return '';
    }
    
    try {
      final date = DateTime(year, month, day);
      return DateFormat('MMM d, yyyy').format(date);
    } catch (e) {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    print("PhotosPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Photos',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadPhotosFromSupabase(initialLoad: true);
              },
              child: _photos.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No photos available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _photos.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _photos.length) {
                          final photo = _photos[index];
                          return VisibilityDetector(
                            key: Key('photo_${photo['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (photo['image_url'] == null ||
                                      photo['image_url'] == 'assets/placeholder_image.png') &&
                                  !photo['_isImageLoading']) {
                                _fetchImageUrl(photo);
                              }
                            },
                            child: _buildPhotoCard(photo, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPhotoCard(
    Map<String, dynamic> photo,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = photo['fullname'] ?? 'Unknown';
    final String platform = photo['platform'] ?? '';
    final String dateStr = _formatDate(photo);
    final String imageUrl = photo['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, photo),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.photo_camera,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.photo_camera,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.photo_camera,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (dateStr.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          dateStr,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (platform.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          platform,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                           maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}