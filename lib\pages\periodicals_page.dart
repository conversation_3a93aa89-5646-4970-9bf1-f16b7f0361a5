// periodicals_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'periodical_detail_page.dart';

class PeriodicalsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedPeriodicals;
  final bool isFromDetailPage;

  const PeriodicalsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedPeriodicals,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<PeriodicalsPage> createState() => _PeriodicalsPageState();
}

class _PeriodicalsPageState extends State<PeriodicalsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('periodicals_list');
  List<Map<String, dynamic>> _periodicals = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("PeriodicalsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant PeriodicalsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("PeriodicalsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("PeriodicalsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedPeriodicals != null && widget.preloadedPeriodicals!.isNotEmpty) {
      print("Preloaded periodicals found, using them.");
      setState(() {
        _periodicals = List<Map<String, dynamic>>.from(widget.preloadedPeriodicals!);
        _periodicals.forEach((periodical) {
          periodical['_isImageLoading'] = false;
        });
        _periodicals.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded periodicals or empty list, loading from database.");
      await _loadPeriodicalsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final periodicalsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_periodicals';
    
    try {
      final response = await Supabase.instance.client
          .from(periodicalsTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_periodicals.length, _periodicals.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadPeriodicalsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadPeriodicalsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final periodicalsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_periodicals';

    try {
      int startRange = initialLoad ? 0 : _periodicals.length;
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(periodicalsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedPeriodicals =
          await _updatePeriodicalImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _periodicals = updatedPeriodicals;
          } else {
            _periodicals.addAll(updatedPeriodicals);
          }
          _periodicals.forEach((periodical) {
            periodical['_isImageLoading'] = false;
          });
          _periodicals.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
      _cachePeriodicals(_periodicals);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching periodicals: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updatePeriodicalImageUrls(
      List<Map<String, dynamic>> periodicals) async {
    List<Future<void>> futures = [];
    for (final periodical in periodicals) {
      if (periodical['image_url'] == null ||
          periodical['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(periodical));
      }
    }
    await Future.wait(futures);
    return periodicals;
  }
  
  void _setupRealtime() {
    final periodicalsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_periodicals';
    _realtimeChannel = Supabase.instance.client
        .channel('periodicals')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: periodicalsTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newPeriodicalId = payload.newRecord['id'];
          final newPeriodicalResponse = await Supabase.instance.client
              .from(periodicalsTableName)
              .select('*')
              .eq('id', newPeriodicalId)
              .single();
          if (mounted) {
            Map<String, dynamic> newPeriodical = Map.from(newPeriodicalResponse);
            final updatedPeriodical = await _updatePeriodicalImageUrls([newPeriodical]);
            setState(() {
              _periodicals.add(updatedPeriodical.first);
              updatedPeriodical.first['_isImageLoading'] = false;
              _periodicals.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedPeriodicalId = payload.newRecord['id'];
          final updatedPeriodicalResponse = await Supabase.instance.client
              .from(periodicalsTableName)
              .select('*')
              .eq('id', updatedPeriodicalId)
              .single();
          if (mounted) {
            final updatedPeriodical = Map<String, dynamic>.from(updatedPeriodicalResponse);
            setState(() {
              _periodicals = _periodicals.map((periodical) {
                return periodical['id'] == updatedPeriodical['id'] ? updatedPeriodical : periodical;
              }).toList();
              _periodicals.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedPeriodicalId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _periodicals.removeWhere((periodical) => periodical['id'] == deletedPeriodicalId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("PeriodicalsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMorePeriodicals();
    }
  }

  Future<void> _loadMorePeriodicals() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more periodicals...");
      await _loadPeriodicalsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _cachePeriodicals(List<Map<String, dynamic>> periodicals) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String periodicalsJson = jsonEncode(periodicals);
      await prefs.setString(
          'periodicals_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          periodicalsJson);
    } catch (e) {
      print('Error caching periodicals: $e');
    }
  }
  
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> periodical) async {
    if (periodical['_isImageLoading'] == true) return;
    if (periodical['image_url'] != null && periodical['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => periodical['_isImageLoading'] = true);

    final fullname = periodical['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegePeriodicalBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/periodicals';

    try {
      final file = await Supabase.instance.client.storage.from(collegePeriodicalBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegePeriodicalBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        periodical['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        periodical['_isImageLoading'] = false;
      });
    } else {
      periodical['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> periodical) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PeriodicalDetailPage(
            periodical: periodical,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("PeriodicalsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Periodicals',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadPeriodicalsFromSupabase(initialLoad: true);
              },
              child: _periodicals.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No periodicals available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _periodicals.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _periodicals.length) {
                          final periodical = _periodicals[index];
                          return VisibilityDetector(
                            key: Key('periodical_${periodical['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (periodical['image_url'] == null ||
                                      periodical['image_url'] == 'assets/placeholder_image.png') &&
                                  !periodical['_isImageLoading']) {
                                _fetchImageUrl(periodical);
                              }
                            },
                            child: _buildPeriodicalCard(periodical, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodicalCard(
    Map<String, dynamic> periodical,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = periodical['fullname'] ?? 'Unknown';
    final String about = periodical['about'] ?? '';
    final String imageUrl = periodical['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, periodical),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.menu_book,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.menu_book,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.menu_book,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}