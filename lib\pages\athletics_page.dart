// athletics_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';

import 'login_page.dart';
import 'athletic_detail_page.dart';

class AthleticsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedAthletics;
  final bool isFromDetailPage;

  const AthleticsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedAthletics,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<AthleticsPage> createState() => _AthleticsPageState();
}

class _AthleticsPageState extends State<AthleticsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('athletics_list');
  List<Map<String, dynamic>> _athletics = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("AthleticsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant AthleticsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("AthleticsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("AthleticsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedAthletics != null && widget.preloadedAthletics!.isNotEmpty) {
      print("Preloaded athletics found, using them.");
      setState(() {
        _athletics = List<Map<String, dynamic>>.from(widget.preloadedAthletics!);
        _athletics.forEach((athletic) {
          athletic['_isImageLoading'] = false;
        });
        _athletics.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded athletics or empty list, loading from database.");
      await _loadAthleticsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final athleticsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_athletics';
    
    try {
      final response = await Supabase.instance.client
          .from(athleticsTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_athletics.length, _athletics.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadAthleticsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadAthleticsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final athleticsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_athletics';

    try {
      int startRange = initialLoad ? 0 : _athletics.length;
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(athleticsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedAthletics =
          await _updateAthleticImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _athletics = updatedAthletics;
          } else {
            _athletics.addAll(updatedAthletics);
          }
          _athletics.forEach((athletic) {
            athletic['_isImageLoading'] = false;
          });
          _athletics.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
      _cacheAthletics(_athletics);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching athletics: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateAthleticImageUrls(
      List<Map<String, dynamic>> athletics) async {
    List<Future<void>> futures = [];
    for (final athletic in athletics) {
      if (athletic['image_url'] == null ||
          athletic['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(athletic));
      }
    }
    await Future.wait(futures);
    return athletics;
  }
  
  void _setupRealtime() {
    final athleticsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_athletics';
    _realtimeChannel = Supabase.instance.client
        .channel('athletics')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: athleticsTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newAthleticId = payload.newRecord['id'];
          final newAthleticResponse = await Supabase.instance.client
              .from(athleticsTableName)
              .select('*')
              .eq('id', newAthleticId)
              .single();
          if (mounted) {
            Map<String, dynamic> newAthletic = Map.from(newAthleticResponse);
            final updatedAthletic = await _updateAthleticImageUrls([newAthletic]);
            setState(() {
              _athletics.add(updatedAthletic.first);
              updatedAthletic.first['_isImageLoading'] = false;
              _athletics.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedAthleticId = payload.newRecord['id'];
          final updatedAthleticResponse = await Supabase.instance.client
              .from(athleticsTableName)
              .select('*')
              .eq('id', updatedAthleticId)
              .single();
          if (mounted) {
            final updatedAthletic = Map<String, dynamic>.from(updatedAthleticResponse);
            setState(() {
              _athletics = _athletics.map((athletic) {
                return athletic['id'] == updatedAthletic['id'] ? updatedAthletic : athletic;
              }).toList();
              _athletics.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedAthleticId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _athletics.removeWhere((athletic) => athletic['id'] == deletedAthleticId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("AthleticsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreAthletics();
    }
  }

  Future<void> _loadMoreAthletics() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more athletics...");
      await _loadAthleticsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _cacheAthletics(List<Map<String, dynamic>> athletics) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String athleticsJson = jsonEncode(athletics);
      await prefs.setString(
          'athletics_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          athleticsJson);
    } catch (e) {
      print('Error caching athletics: $e');
    }
  }
  
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> athletic) async {
    if (athletic['_isImageLoading'] == true) return;
    if (athletic['image_url'] != null && athletic['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => athletic['_isImageLoading'] = true);

    final fullname = athletic['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeAthleticBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/athletics';

    try {
      final file = await Supabase.instance.client.storage.from(collegeAthleticBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeAthleticBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        athletic['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        athletic['_isImageLoading'] = false;
      });
    } else {
      athletic['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> athletic) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AthleticDetailPage(
            athletic: athletic,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("AthleticsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Athletics',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadAthleticsFromSupabase(initialLoad: true);
              },
              child: _athletics.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No athletics available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _athletics.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _athletics.length) {
                          final athletic = _athletics[index];
                          return VisibilityDetector(
                            key: Key('athletic_${athletic['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (athletic['image_url'] == null ||
                                      athletic['image_url'] == 'assets/placeholder_image.png') &&
                                  !athletic['_isImageLoading']) {
                                _fetchImageUrl(athletic);
                              }
                            },
                            child: _buildAthleticCard(athletic, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAthleticCard(
    Map<String, dynamic> athletic,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = athletic['fullname'] ?? 'Unknown';
    final String about = athletic['about'] ?? '';
    final String imageUrl = athletic['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, athletic),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.sports_basketball,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.sports_basketball,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.sports_basketball,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}