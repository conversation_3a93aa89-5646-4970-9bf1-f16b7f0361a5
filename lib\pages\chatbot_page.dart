// pages/chatbot_page.dart

import 'package:flutter/material.dart';

class ChatbotPage extends StatefulWidget {
  final Map<String, dynamic> organization;
  final bool isDarkMode;

  const ChatbotPage({
    Key? key,
    required this.organization,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  State<ChatbotPage> createState() => _ChatbotPageState();
}

class _ChatbotPageState extends State<ChatbotPage> {
  final TextEditingController _controller = TextEditingController();
  final List<Map<String, String>> _messages = [];

  @override
  void initState() {
    super.initState();
    // Add a welcome message from the bot
    _messages.add({
      'sender': 'bot',
      'text': 'Hello! How can I help you with ${widget.organization['name']} today?'
    });
  }

  void _sendMessage(String text) {
    if (text.trim().isEmpty) return;

    setState(() {
      _messages.add({'sender': 'user', 'text': text});
      // **Placeholder for AI response**
      // In a real app, you would call your AI service here
      // and add the response to the _messages list.
      Future.delayed(const Duration(seconds: 1), () {
        setState(() {
          _messages.add({
            'sender': 'bot',
            'text': 'This is a placeholder response for "${text.trim()}". Integrate your AI here.'
          });
        });
      });
    });
    _controller.clear();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.organization['name'] ?? 'Chat'),
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                final isUserMessage = message['sender'] == 'user';
                return Align(
                  alignment: isUserMessage ? Alignment.centerRight : Alignment.centerLeft,
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 5.0),
                    padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 10.0),
                    decoration: BoxDecoration(
                      color: isUserMessage
                          ? theme.primaryColor.withOpacity(0.8)
                          : theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(20),
                      border: isUserMessage ? null : Border.all(color: theme.dividerColor),
                    ),
                    child: Text(
                      message['text']!,
                      style: TextStyle(
                        color: isUserMessage
                            ? (theme.primaryTextTheme.bodyLarge?.color ?? Colors.white)
                            : theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          _buildMessageComposer(theme),
        ],
      ),
    );
  }

  Widget _buildMessageComposer(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      color: theme.colorScheme.surface,
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: _controller,
                decoration: InputDecoration(
                  hintText: 'Ask a question...',
                  filled: true,
                  fillColor: theme.scaffoldBackgroundColor,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30.0),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                ),
                onSubmitted: _sendMessage,
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.send),
              onPressed: () => _sendMessage(_controller.text),
              style: IconButton.styleFrom(
                backgroundColor: theme.primaryColor,
                foregroundColor: theme.primaryTextTheme.bodyLarge?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}