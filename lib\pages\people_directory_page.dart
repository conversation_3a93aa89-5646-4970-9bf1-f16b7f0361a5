import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:async'; // For Timer (debouncing)
import 'dart:io';

import 'person_detail_page.dart';
import 'login_page.dart';
import 'login_page.dart';

class PeopleDirectoryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedPeople;
  final bool isFromDetailPage;

  const PeopleDirectoryPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedPeople,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _PeopleDirectoryPageState createState() => _PeopleDirectoryPageState();
}

class _PeopleDirectoryPageState extends State<PeopleDirectoryPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('people_directory_list');
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  // UI State
  bool _isFilterVisible = false;
  bool _isSearchActive = false;
  Timer? _debounceTimer;

  // Data State
  bool _isDisposed = false;
  List<Map<String, dynamic>> _people = [];
  List<Map<String, dynamic>> _filteredPeople = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isSearching = false;
  late final RealtimeChannel _peopleChannel;
  late final RealtimeChannel _studentsChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _selectedFilter = 'All';
  final List<String> _filterOptions = ['All', 'Faculty', 'Staff', 'Students'];
  String _searchQuery = '';

  String get _peopleTableName =>
      '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_people';
  String get _studentsTableName =>
      '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_currentstudents';

  @override
  void initState() {
    super.initState();
    print("PeopleDirectoryPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer!.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      final query = _searchController.text.trim();
      if (query != _searchQuery) {
        setState(() {
          _searchQuery = query;
        });
        _performSearch(query);
      }
    });
  }

  Future<void> _performSearch(String query) async {
    if (query.isEmpty) {
      setState(() {
        _filteredPeople = _people;
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      List<Map<String, dynamic>> searchResults = [];
      final searchTerm = query.toLowerCase();

      if (_selectedFilter == 'Faculty' || _selectedFilter == 'Staff') {
        var searchQuery = Supabase.instance.client.from(_peopleTableName).select('*');
        if (_selectedFilter == 'Faculty') searchQuery = searchQuery.eq('facultymember', true);
        if (_selectedFilter == 'Staff') searchQuery = searchQuery.eq('staffmember', true);
        
        searchResults = await searchQuery
            .or('fullname.ilike.%$query%,title.ilike.%$query%,department.ilike.%$query%')
            .order('fullname', ascending: true)
            .limit(100); // Limit search results
      } else if (_selectedFilter == 'Students') {
        searchResults = await Supabase.instance.client
            .from(_studentsTableName)
            .select('*')
            .or('fullname.ilike.%$query%,department.ilike.%$query%')
            .order('fullname', ascending: true)
            .limit(100);
        searchResults = searchResults.map((person) => {...person, 'type': 'student'}).toList();
      } else { // "All"
        // Search in people table
        final peopleResults = await Supabase.instance.client
            .from(_peopleTableName)
            .select('*')
            .or('fullname.ilike.%$query%,title.ilike.%$query%,department.ilike.%$query%')
            .order('fullname', ascending: true)
            .limit(50);
        
        // Search in students table
        final studentsResults = await Supabase.instance.client
            .from(_studentsTableName)
            .select('*')
            .or('fullname.ilike.%$query%,department.ilike.%$query%')
            .order('fullname', ascending: true)
            .limit(50);
        
        final studentsWithType = studentsResults.map((person) => {...person, 'type': 'student'}).toList();
        searchResults = [...peopleResults, ...studentsWithType];
        searchResults.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
      }

      if (mounted) {
        setState(() {
          _filteredPeople = searchResults;
          _isSearching = false;
        });
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });
        _showErrorSnackbar("Search error: $error");
      }
    }
  }

  void _activateSearch() {
    setState(() {
      _isSearchActive = true;
      _filteredPeople = _people;
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  void _deactivateSearch() {
    setState(() {
      _isSearchActive = false;
      _searchQuery = '';
      _filteredPeople = _people;
    });
    _searchController.clear();
    _searchFocusNode.unfocus();
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedPeople != null && widget.preloadedPeople!.isNotEmpty) {
      print("Preloaded people found, using them.");
      setState(() {
        _people = List<Map<String, dynamic>>.from(widget.preloadedPeople!);
        _filteredPeople = _people;
        _sortPeople();
        _hasMore = widget.preloadedPeople!.length == _pageSize;
        _page = 0;
      });
    } else {
      print("No preloaded people, loading from database.");
      await _loadPeopleFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadPeopleFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadPeopleFromSupabase called - initialLoad: $initialLoad, page: $_page, filter: $_selectedFilter");

    if (initialLoad) {
      setState(() {
        _isLoading = true;
        _page = 0;
        _people.clear();
        _filteredPeople.clear();
      });
    } else {
      setState(() => _isLoadingMore = true);
    }

    try {
      List<Map<String, dynamic>> response = [];

      if (_selectedFilter == 'Faculty' || _selectedFilter == 'Staff') {
        var query = Supabase.instance.client.from(_peopleTableName).select('*');
        if (_selectedFilter == 'Faculty') query = query.eq('facultymember', true);
        if (_selectedFilter == 'Staff') query = query.eq('staffmember', true);
        response = await query.order('fullname', ascending: true).limit(_pageSize);
        if (_page > 0) {
          response = await query.order('fullname', ascending: true).range(_page * _pageSize, (_page + 1) * _pageSize - 1);
        }
      } else if (_selectedFilter == 'Students') {
        var query = Supabase.instance.client.from(_studentsTableName).select('*');
        response = await query.order('fullname', ascending: true).limit(_pageSize);
        if (_page > 0) {
          response = await query.order('fullname', ascending: true).range(_page * _pageSize, (_page + 1) * _pageSize - 1);
        }
        // Add type field to identify students
        response = response.map((person) => {...person, 'type': 'student'}).toList();
      } else { // "All" - Load from both tables
        // Get people (faculty/staff) from people table
        final peopleQuery = Supabase.instance.client.from(_peopleTableName).select('*');
        final peopleResponse = await peopleQuery.order('fullname', ascending: true);
        
        // Get students from students table
        final studentsQuery = Supabase.instance.client.from(_studentsTableName).select('*');
        final studentsResponse = await studentsQuery.order('fullname', ascending: true);
        
        // Add type field to identify students
        final studentsWithType = studentsResponse.map((person) => {...person, 'type': 'student'}).toList();
        
        // Combine both lists
        final allPeople = [...peopleResponse, ...studentsWithType];
        
        // Sort by fullname
        allPeople.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        
        // Apply pagination
        final startIndex = _page * _pageSize;
        final endIndex = startIndex + _pageSize;
        
        if (startIndex < allPeople.length) {
          response = allPeople.sublist(startIndex, endIndex > allPeople.length ? allPeople.length : endIndex);
        } else {
          response = [];
        }
      }

      final newPeople = List<Map<String, dynamic>>.from(response);

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _people = newPeople;
            _filteredPeople = _people;
          } else {
            _people.addAll(newPeople);
            if (_searchQuery.isEmpty) {
              _filteredPeople = _people;
            }
          }
          _sortPeople();

          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = newPeople.length >= _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
        
        // Re-perform search if active
        if (_searchQuery.isNotEmpty) {
          _performSearch(_searchQuery);
        }
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') ||
            errorStr.contains('does not exist')) {
          errorMsg = "Directory data for this institution hasn't been added or configured yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching directory: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    _peopleChannel = Supabase.instance.client
        .channel('people_directory_people')
        .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: _peopleTableName,
            callback: (payload) => _handleRealtimeUpdate(payload))
        .subscribe();

    _studentsChannel = Supabase.instance.client
        .channel('people_directory_students')
        .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: _studentsTableName,
            callback: (payload) => _handleRealtimeUpdate(payload))
        .subscribe();
  }

  void _handleRealtimeUpdate(PostgresChangePayload payload) {
    if (!mounted) return;
    print("Realtime event: ${payload.eventType} from table ${payload.table}");
    _loadPeopleFromSupabase(initialLoad: true);
  }

  void _sortPeople() {
    _people.sort((a, b) => (a['fullname'] ?? '')
        .toLowerCase()
        .compareTo((b['fullname'] ?? '').toLowerCase()));
    if (_searchQuery.isEmpty) {
      _filteredPeople = _people;
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _debounceTimer?.cancel();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    _peopleChannel.unsubscribe();
    _studentsChannel.unsubscribe();
    print("PeopleDirectoryPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _searchQuery.isEmpty &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMorePeople();
    }
  }

  Future<void> _loadMorePeople() async {
    if (!_isLoadingMore && _hasMore && _searchQuery.isEmpty) {
      await _loadPeopleFromSupabase(initialLoad: false);
    }
  }

  void _onFilterChanged(String? newValue) {
    if (newValue != null && newValue != _selectedFilter) {
      setState(() {
        _selectedFilter = newValue;
      });
      _loadPeopleFromSupabase(initialLoad: true);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> person) {
    if (!_isDisposed) {
      final bool isStudent = person['type'] == 'student';
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PersonDetailPage(
            person: person,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            isStudent: isStudent,
          ),
        ),
      );
    }
  }

  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
          content: Text("Offline. Please check your internet connection."),
          backgroundColor: Colors.redAccent),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.redAccent),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: _isSearchActive
            ? IconButton(
                icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
                onPressed: _deactivateSearch,
              )
            : IconButton(
                icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
                onPressed: () => Navigator.pop(context),
              ),
        title: _isSearchActive
            ? TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                decoration: InputDecoration(
                  hintText: 'Search people...',
                  hintStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  focusedErrorBorder: InputBorder.none,
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: Icon(Icons.clear, color: theme.colorScheme.onSurfaceVariant),
                          onPressed: () {
                            _searchController.clear();
                            _performSearch('');
                          },
                        )
                      : null,
                ),
                style: TextStyle(
                  color: theme.colorScheme.onSurface,
                  fontSize: 16,
                ),
                textInputAction: TextInputAction.search,
              )
            : Text('A-Z Directory',
                style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface)),
        actions: _isSearchActive
            ? []
            : [
                IconButton(
                  icon: Icon(Icons.search, color: theme.colorScheme.onSurface),
                  onPressed: _activateSearch,
                ),
                IconButton(
                  icon: Icon(Icons.filter_list,
                      color: _isFilterVisible
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurface),
                  onPressed: () {
                    setState(() {
                      _isFilterVisible = !_isFilterVisible;
                    });
                  },
                ),
              ],
      ),
      body: Column(
        children: [
          if (_isFilterVisible && !_isSearchActive) _buildFilterChips(theme),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                    color: theme.colorScheme.onSurface,
                    onRefresh: () => _loadPeopleFromSupabase(initialLoad: true),
                    child: _buildPeopleList(theme, currentIsDarkMode),
                  ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                    icon: Icon(Icons.home_outlined,
                        color: theme.colorScheme.onSurface),
                    onPressed: () =>
                        Navigator.of(context).popUntil((route) => route.isFirst)),
                const SizedBox(width: 24),
                IconButton(
                    icon: Icon(
                        currentIsDarkMode
                            ? Icons.light_mode_outlined
                            : Icons.dark_mode_outlined,
                        color: theme.colorScheme.onSurface),
                    onPressed: widget.toggleTheme),
                const SizedBox(width: 24),
                IconButton(
                    icon: Icon(Icons.person_outline,
                        color: theme.colorScheme.onSurface),
                    onPressed: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => LoginPage(
                                isDarkMode: currentIsDarkMode,
                                toggleTheme: widget.toggleTheme)))),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPeopleList(ThemeData theme, bool currentIsDarkMode) {
    final displayList = _isSearchActive || _searchQuery.isNotEmpty ? _filteredPeople : _people;
    final isSearchMode = _isSearchActive || _searchQuery.isNotEmpty;

    if (_isSearching) {
      return const Center(child: CircularProgressIndicator());
    }

    if (displayList.isEmpty) {
      return LayoutBuilder(
        builder: (context, constraints) => SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: constraints.maxHeight,
            child: Center(
              child: Text(
                isSearchMode 
                    ? 'No people found matching your search.'
                    : 'No people found matching your criteria.',
                style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
              ),
            ),
          ),
        ),
      );
    }

    return ListView.builder(
      key: _listKey,
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: displayList.length + (!isSearchMode && _hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < displayList.length) {
          return _buildPersonCard(displayList[index], theme, currentIsDarkMode);
        } else if (!isSearchMode && _hasMore) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: CircularProgressIndicator(),
            ),
          );
        } else {
          return Container();
        }
      },
    );
  }

  Widget _buildFilterChips(ThemeData theme) {
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Wrap(
        spacing: 8.0,
        runSpacing: 4.0,
        alignment: WrapAlignment.center,
        children: _filterOptions.map((filter) {
          final isSelected = _selectedFilter == filter;
          return ChoiceChip(
            label: Text(filter),
            selected: isSelected,
            onSelected: (selected) {
              if (selected) {
                _onFilterChanged(filter);
              }
            },
            labelStyle: TextStyle(
              color: isSelected
                  ? (currentIsDarkMode ? Colors.black : Colors.white)
                  : theme.colorScheme.onSurfaceVariant,
            ),
            selectedColor: currentIsDarkMode ? Colors.white : Colors.black,
            backgroundColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            shape: StadiumBorder(side: BorderSide(color: Colors.transparent)),
            checkmarkColor: currentIsDarkMode ? Colors.black : Colors.white,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPersonCard(
      Map<String, dynamic> person, ThemeData theme, bool isDarkMode) {
    final String fullname = person['fullname'] ?? 'Unknown';
    final String title = person['title'] ?? '';
    final String department = person['department'] ?? '';
    final bool isStudent = person['type'] == 'student';

    String roleText = '';
    if (isStudent) {
      roleText = 'Student - Year ${person['year'] ?? '?'}';
    } else {
      if (person['facultymember'] == true) {
        roleText = 'Faculty';
      } else if (person['staffmember'] == true) {
        roleText = 'Staff';
      }
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, person),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(Icons.person_outline,
                    color: isDarkMode ? Colors.white : Colors.black),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(fullname,
                        style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface)),
                    if (title.isNotEmpty && !isStudent)
                      Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(title,
                              style: TextStyle(
                                  color: theme.colorScheme.onSurfaceVariant,
                                  fontSize: 13))),
                    if (department.isNotEmpty && !isStudent)
                      Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(department,
                              style: TextStyle(
                                  color: theme.colorScheme.onSurfaceVariant,
                                  fontSize: 13))),
                    if (roleText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(roleText,
                            style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontWeight: FontWeight.w500,
                                fontSize: 12)),
                      ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios,
                  size: 16, color: theme.colorScheme.onSurfaceVariant),
            ],
          ),
        ),
      ),
    );
  }
}