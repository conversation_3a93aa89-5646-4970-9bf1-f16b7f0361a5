import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'helpdesks_detail_page.dart';
import 'login_page.dart';

class HelpdesksPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedHelpdesks;
  final bool isFromDetailPage;

  const HelpdesksPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedHelpdesks,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _HelpdesksPageState createState() => _HelpdesksPageState();
}

class _HelpdesksPageState extends State<HelpdesksPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('helpdesks_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _helpdesks = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("HelpdesksPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant HelpdesksPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("HelpdesksPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("HelpdesksPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedHelpdesks != null && widget.preloadedHelpdesks!.isNotEmpty) {
      print("Preloaded helpdesks found, using them.");
      setState(() {
        _helpdesks = List<Map<String, dynamic>>.from(widget.preloadedHelpdesks!);
        _helpdesks.forEach((helpdesk) {
          helpdesk['_isImageLoading'] = false;
        });
        _helpdesks.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedHelpdesks!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      // Don't load more from database if we have preloaded data
      // But check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded helpdesks or empty list, loading from database.");
      await _loadHelpdesksFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final helpdesksTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_helpdesks';
    
    try {
      final response = await Supabase.instance.client
          .from(helpdesksTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_helpdesks.length, _helpdesks.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadHelpdesksFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadHelpdesksFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final helpdesksTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_helpdesks';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _helpdesks.length;
        endRange = _helpdesks.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(helpdesksTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedHelpdesks =
          await _updateHelpdeskImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _helpdesks = updatedHelpdesks;
          } else {
            _helpdesks.addAll(updatedHelpdesks);
          }
          _helpdesks.forEach((helpdesk) {
            helpdesk['_isImageLoading'] = false;
          });
          _helpdesks.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching helpdesks: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateHelpdeskImageUrls(
      List<Map<String, dynamic>> helpdesks) async {
    List<Future<void>> futures = [];
    for (final helpdesk in helpdesks) {
      if (helpdesk['image_url'] == null ||
          helpdesk['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(helpdesk));
      }
    }
    await Future.wait(futures);
    return helpdesks;
  }

  void _setupRealtime() {
    final helpdesksTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_helpdesks';
    _realtimeChannel = Supabase.instance.client
        .channel('helpdesks')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: helpdesksTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newHelpdeskId = payload.newRecord['id'];
          final newHelpdeskResponse = await Supabase.instance.client
              .from(helpdesksTableName)
              .select('*')
              .eq('id', newHelpdeskId)
              .single();
          if (mounted) {
            Map<String, dynamic> newHelpdesk = Map.from(newHelpdeskResponse);
            final updatedHelpdesk = await _updateHelpdeskImageUrls([newHelpdesk]);
            setState(() {
              _helpdesks.add(updatedHelpdesk.first);
              updatedHelpdesk.first['_isImageLoading'] = false;
              _helpdesks.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedHelpdeskId = payload.newRecord['id'];
          final updatedHelpdeskResponse = await Supabase.instance.client
              .from(helpdesksTableName)
              .select('*')
              .eq('id', updatedHelpdeskId)
              .single();
          if (mounted) {
            final updatedHelpdesk = Map<String, dynamic>.from(updatedHelpdeskResponse);
            setState(() {
              _helpdesks = _helpdesks.map((helpdesk) {
                return helpdesk['id'] == updatedHelpdesk['id'] ? updatedHelpdesk : helpdesk;
              }).toList();
              _helpdesks.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedHelpdeskId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _helpdesks.removeWhere((helpdesk) => helpdesk['id'] == deletedHelpdeskId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("HelpdesksPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreHelpdesks();
    }
  }

  Future<void> _loadMoreHelpdesks() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more helpdesks...");
      await _loadHelpdesksFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> helpdesk) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => HelpdeskDetailPage(
            helpdesk: helpdesk,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("HelpdesksPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Helpdesks',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadHelpdesksFromSupabase(initialLoad: true);
              },
              child: _helpdesks.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No helpdesks available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _helpdesks.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _helpdesks.length) {
                          final helpdesk = _helpdesks[index];
                          return VisibilityDetector(
                            key: Key('helpdesk_${helpdesk['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (helpdesk['image_url'] == null ||
                                      helpdesk['image_url'] == 'assets/placeholder_image.png') &&
                                  !helpdesk['_isImageLoading']) {
                                _fetchImageUrl(helpdesk);
                              }
                            },
                            child: _buildHelpdeskCard(helpdesk, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> helpdesk) async {
    if (helpdesk['_isImageLoading'] == true) {
      print('Image loading already in progress for ${helpdesk['fullname']}, skipping.');
      return;
    }
    if (helpdesk['image_url'] != null &&
        helpdesk['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${helpdesk['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      helpdesk['_isImageLoading'] = true;
    });

    final fullname = helpdesk['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeHelpdeskBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/helpdesks';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeHelpdeskBucket');
    print('Image URL before fetch: ${helpdesk['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        helpdesk['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        helpdesk['_isImageLoading'] = false;
        print('Setting image_url for ${helpdesk['fullname']} to: ${helpdesk['image_url']}');
      });
    } else {
      helpdesk['_isImageLoading'] = false;
    }
  }

  Widget _buildHelpdeskCard(
    Map<String, dynamic> helpdesk,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = helpdesk['fullname'] ?? 'Unknown';
    final String building = helpdesk['building'] ?? '';
    final String room = helpdesk['room'] ?? '';
    final String hours = helpdesk['hours'] ?? '';
    final String about = helpdesk['about'] ?? '';
    final String imageUrl = helpdesk['image_url'] ?? '';

    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, helpdesk),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.help_center,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.help_center,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.help_center,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          hours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}