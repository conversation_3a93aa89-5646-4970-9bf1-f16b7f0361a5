// research_projects_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'research_project_detail_page.dart';

class ResearchProjectsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedResearchProjects;
  final bool isFromDetailPage;

  const ResearchProjectsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedResearchProjects,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<ResearchProjectsPage> createState() => _ResearchProjectsPageState();
}

class _ResearchProjectsPageState extends State<ResearchProjectsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('research_projects_list');
  List<Map<String, dynamic>> _researchProjects = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  String? _selectedYear;
  String? _selectedDepartment;
  List<String> _availableYears = [];
  List<String> _availableDepartments = [];

  @override
  void initState() {
    super.initState();
    print("ResearchProjectsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant ResearchProjectsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.collegeNameForTable != widget.collegeNameForTable) {
      _loadInitialData();
    }
    print("ResearchProjectsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("ResearchProjectsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    await _fetchFilterOptions();
    if (widget.preloadedResearchProjects != null && widget.preloadedResearchProjects!.isNotEmpty) {
      print("Preloaded research projects found, using them.");
      setState(() {
        _researchProjects = List<Map<String, dynamic>>.from(widget.preloadedResearchProjects!);
        _sortProjects();
        _hasMore = widget.preloadedResearchProjects!.length == _pageSize;
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded research projects or empty list, loading from database.");
      await _loadResearchProjectsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _fetchFilterOptions() async {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchprojects';
    try {
      final yearsResponse = await Supabase.instance.client.from(tableName).select('year');
      final departmentsResponse = await Supabase.instance.client.from(tableName).select('department');

      if (mounted) {
        setState(() {
          _availableYears = (yearsResponse as List)
              .map((e) => e['year'].toString())
              .where((y) => y != 'null' && y.isNotEmpty)
              .toSet()
              .toList()..sort((a,b) => b.compareTo(a));

          _availableDepartments = (departmentsResponse as List)
              .map((e) => e['department'].toString())
              .where((d) => d != 'null' && d.isNotEmpty)
              .toSet()
              .toList()..sort();
        });
      }
    } catch (e) {
      print('Error fetching filter options: $e');
    }
  }

  Future<void> _checkForMoreData() async {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchprojects';
    
    try {
      var query = Supabase.instance.client.from(tableName).select('id');
      if (_selectedYear != null) query = query.eq('year', int.parse(_selectedYear!));
      if (_selectedDepartment != null) query = query.eq('department', _selectedDepartment!);
      
      final response = await query
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(_researchProjects.length, _researchProjects.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadResearchProjectsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadResearchProjectsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchprojects';

    try {
      int startRange = initialLoad ? 0 : _researchProjects.length;
      int endRange = startRange + _pageSize - 1;

      var query = Supabase.instance.client.from(tableName).select('*');
      if (_selectedYear != null) query = query.eq('year', int.parse(_selectedYear!));
      if (_selectedDepartment != null) query = query.eq('department', _selectedDepartment!);

      final response = await query
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(startRange, endRange);

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _researchProjects = List<Map<String, dynamic>>.from(response);
          } else {
            _researchProjects.addAll(List<Map<String, dynamic>>.from(response));
          }
          _sortProjects();
          _isLoading = false;
          _isLoadingMore = false;
          // Fix: Properly check if there's more data
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
    } catch (error) {
      if (!_isDisposed && mounted) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching research projects: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchprojects';
    _realtimeChannel = Supabase.instance.client
        .channel('research_projects_realtime')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: tableName,
      callback: (payload) async {
        if (!mounted) return;
        await _loadResearchProjectsFromSupabase(initialLoad: true);
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("ResearchProjectsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreProjects();
    }
  }

  Future<void> _loadMoreProjects() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more research projects...");
      await _loadResearchProjectsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _sortProjects() {
    _researchProjects.sort((a, b) {
      final aYear = a['year'] ?? 0;
      final bYear = b['year'] ?? 0;
      if (aYear != bYear) return bYear.compareTo(aYear);
      
      final aMonth = a['month'] ?? 0;
      final bMonth = b['month'] ?? 0;
      if (aMonth != bMonth) return bMonth.compareTo(aMonth);
      
      final aDay = a['day'] ?? 0;
      final bDay = b['day'] ?? 0;
      return bDay.compareTo(aDay);
    });
  }

  void _showOfflineSnackbar() {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> project) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ResearchProjectDetailPage(
            researchProject: project,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForTable: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  void _showFilterDialog() {
    String? tempYear = _selectedYear;
    String? tempDept = _selectedDepartment;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            final theme = Theme.of(context);
            final isDarkMode = theme.brightness == Brightness.dark;
            
            return AlertDialog(
              backgroundColor: theme.colorScheme.surface,
              title: Text(
                'Filter Projects',
                style: TextStyle(
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DropdownButtonFormField<String>(
                      value: tempYear,
                      hint: Text(
                        'Select Year',
                        style: TextStyle(
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      isExpanded: true,
                      onChanged: (value) => setDialogState(() => tempYear = value),
                      items: _availableYears.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(
                            value,
                            style: TextStyle(
                              color: isDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                        );
                      }).toList(),
                      decoration: InputDecoration(
                        labelText: 'Year',
                        labelStyle: TextStyle(
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      dropdownColor: theme.colorScheme.surface,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: tempDept,
                      hint: Text(
                        'Select Department',
                        style: TextStyle(
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      isExpanded: true,
                      onChanged: (value) => setDialogState(() => tempDept = value),
                      items: _availableDepartments.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(
                            value,
                            style: TextStyle(
                              color: isDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                        );
                      }).toList(),
                      decoration: InputDecoration(
                        labelText: 'Department',
                        labelStyle: TextStyle(
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      dropdownColor: theme.colorScheme.surface,
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  child: Text(
                    'Clear',
                    style: TextStyle(
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedYear = null;
                      _selectedDepartment = null;
                      _page = 0;
                      _hasMore = true;
                    });
                    _loadResearchProjectsFromSupabase(initialLoad: true);
                  },
                ),
                TextButton(
                  child: Text(
                    'Apply',
                    style: TextStyle(
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedYear = tempYear;
                      _selectedDepartment = tempDept;
                      _page = 0;
                      _hasMore = true;
                    });
                    _loadResearchProjectsFromSupabase(initialLoad: true);
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    print("ResearchProjectsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Research Projects',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.filter_list, color: theme.colorScheme.onSurface),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadResearchProjectsFromSupabase(initialLoad: true);
              },
              child: _researchProjects.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No research projects available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _researchProjects.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _researchProjects.length) {
                          final project = _researchProjects[index];
                          return VisibilityDetector(
                            key: Key('project_${project['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              // Add any lazy loading logic here if needed
                            },
                            child: _buildResearchProjectCard(project, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                            child: Padding(
                              padding: EdgeInsets.all(16),
                              child: CircularProgressIndicator(),
                            ),
                          );
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  String _formatResearchers(Map<String, dynamic> project) {
    final List<String> researchers = [];
    if (project['researcher'] != null && project['researcher'].toString().isNotEmpty) {
      researchers.add(project['researcher'].toString());
    }
    if (project['researcher2'] != null && project['researcher2'].toString().isNotEmpty) {
      researchers.add(project['researcher2'].toString());
    }
    if (project['researcher3'] != null && project['researcher3'].toString().isNotEmpty) {
      researchers.add(project['researcher3'].toString());
    }
    return researchers.isNotEmpty ? researchers.join(', ') : 'N/A';
  }

  Widget _buildResearchProjectCard(
    Map<String, dynamic> project,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = project['fullname'] ?? 'Unknown';
    final String researchers = _formatResearchers(project);
    final String department = project['department'] ?? 'N/A';
    final String year = project['year']?.toString() ?? 'N/A';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, project),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.biotech,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (researchers != 'N/A')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          researchers,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (department != 'N/A' || year != 'N/A')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          '$department • $year',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}