// research_groups_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'research_group_detail_page.dart';

class ResearchGroupsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedResearchGroups;
  final bool isFromDetailPage;

  const ResearchGroupsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedResearchGroups,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<ResearchGroupsPage> createState() => _ResearchGroupsPageState();
}

class _ResearchGroupsPageState extends State<ResearchGroupsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('research_groups_list');
  List<Map<String, dynamic>> _researchGroups = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("ResearchGroupsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant ResearchGroupsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("ResearchGroupsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("ResearchGroupsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedResearchGroups != null && widget.preloadedResearchGroups!.isNotEmpty) {
      print("Preloaded research groups found, using them.");
      setState(() {
        _researchGroups = List<Map<String, dynamic>>.from(widget.preloadedResearchGroups!);
        _researchGroups.forEach((group) {
          group['_isImageLoading'] = false;
        });
        _researchGroups.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded research groups or empty list, loading from database.");
      await _loadResearchGroupsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final researchGroupsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchgroups';
    
    try {
      final response = await Supabase.instance.client
          .from(researchGroupsTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_researchGroups.length, _researchGroups.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadResearchGroupsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadResearchGroupsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final researchGroupsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchgroups';

    try {
      int startRange = initialLoad ? 0 : _researchGroups.length;
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(researchGroupsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedResearchGroups =
          await _updateResearchGroupImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _researchGroups = updatedResearchGroups;
          } else {
            _researchGroups.addAll(updatedResearchGroups);
          }
          _researchGroups.forEach((group) {
            group['_isImageLoading'] = false;
          });
          _researchGroups.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
      _cacheResearchGroups(_researchGroups);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching research groups: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateResearchGroupImageUrls(
      List<Map<String, dynamic>> researchGroups) async {
    List<Future<void>> futures = [];
    for (final researchGroup in researchGroups) {
      if (researchGroup['image_url'] == null ||
          researchGroup['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(researchGroup));
      }
    }
    await Future.wait(futures);
    return researchGroups;
  }
  
  void _setupRealtime() {
    final researchGroupsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchgroups';
    _realtimeChannel = Supabase.instance.client
        .channel('research_groups')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: researchGroupsTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newResearchGroupId = payload.newRecord['id'];
          final newResearchGroupResponse = await Supabase.instance.client
              .from(researchGroupsTableName)
              .select('*')
              .eq('id', newResearchGroupId)
              .single();
          if (mounted) {
            Map<String, dynamic> newResearchGroup = Map.from(newResearchGroupResponse);
            final updatedResearchGroup = await _updateResearchGroupImageUrls([newResearchGroup]);
            setState(() {
              _researchGroups.add(updatedResearchGroup.first);
              updatedResearchGroup.first['_isImageLoading'] = false;
              _researchGroups.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedResearchGroupId = payload.newRecord['id'];
          final updatedResearchGroupResponse = await Supabase.instance.client
              .from(researchGroupsTableName)
              .select('*')
              .eq('id', updatedResearchGroupId)
              .single();
          if (mounted) {
            final updatedResearchGroup = Map<String, dynamic>.from(updatedResearchGroupResponse);
            setState(() {
              _researchGroups = _researchGroups.map((researchGroup) {
                return researchGroup['id'] == updatedResearchGroup['id'] ? updatedResearchGroup : researchGroup;
              }).toList();
              _researchGroups.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedResearchGroupId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _researchGroups.removeWhere((researchGroup) => researchGroup['id'] == deletedResearchGroupId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("ResearchGroupsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreResearchGroups();
    }
  }

  Future<void> _loadMoreResearchGroups() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more research groups...");
      await _loadResearchGroupsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _cacheResearchGroups(List<Map<String, dynamic>> researchGroups) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String researchGroupsJson = jsonEncode(researchGroups);
      await prefs.setString(
          'researchgroups_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          researchGroupsJson);
    } catch (e) {
      print('Error caching research groups: $e');
    }
  }
  
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> researchGroup) async {
    if (researchGroup['_isImageLoading'] == true) return;
    if (researchGroup['image_url'] != null && researchGroup['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => researchGroup['_isImageLoading'] = true);

    final fullname = researchGroup['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeResearchGroupBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/researchgroups';

    try {
      final file = await Supabase.instance.client.storage.from(collegeResearchGroupBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeResearchGroupBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        researchGroup['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        researchGroup['_isImageLoading'] = false;
      });
    } else {
      researchGroup['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> researchGroup) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ResearchGroupDetailPage(
            researchGroup: researchGroup,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("ResearchGroupsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Research Groups',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadResearchGroupsFromSupabase(initialLoad: true);
              },
              child: _researchGroups.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No research groups available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _researchGroups.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _researchGroups.length) {
                          final researchGroup = _researchGroups[index];
                          return VisibilityDetector(
                            key: Key('researchgroup_${researchGroup['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (researchGroup['image_url'] == null ||
                                      researchGroup['image_url'] == 'assets/placeholder_image.png') &&
                                  !researchGroup['_isImageLoading']) {
                                _fetchImageUrl(researchGroup);
                              }
                            },
                            child: _buildResearchGroupCard(researchGroup, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResearchGroupCard(
    Map<String, dynamic> researchGroup,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = researchGroup['fullname'] ?? 'Unknown';
    final String about = researchGroup['about'] ?? '';
    final String imageUrl = researchGroup['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, researchGroup),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.science,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.science,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.science,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}