// In android/build.gradle

buildscript {
    // These versions are updated to be compatible with modern libraries.
    // ext.kotlin_version = '1.9.22' // <-- UPDATED from 1.7.10   ITS NOW USING KOTLIN VERSION IN SETTINGS.GRADDLE
    repositories {
        google() 
        mavenCentral()
    }
dependencies {
    classpath 'com.android.tools.build:gradle:8.2.2'
    // The kotlin-gradle-plugin line has been removed.
    classpath 'com.google.gms:google-services:4.4.1'
}
}

// ===================================================================
// <<< --- THIS IS THE FIX FOR PLUGINS LIKE 'app_links' --- >>>
// This creates the global 'flutter' object that plugins expect to exist.
// No changes are needed here.
ext.flutter = [
    compileSdkVersion: 34,
    minSdkVersion:     21,
    targetSdkVersion:  34,
    ndkVersion:        "25.1.8937393"
]
// ===================================================================

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}