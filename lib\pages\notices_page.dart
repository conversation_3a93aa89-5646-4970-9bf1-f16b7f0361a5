import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:intl/intl.dart';
import 'dart:io';

import 'notice_detail_page.dart';
import 'login_page.dart';

class NoticesPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedNotices;
  final bool isFromDetailPage;

  const NoticesPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedNotices,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _NoticesPageState createState() => _NoticesPageState();
}

class _NoticesPageState extends State<NoticesPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('notices_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _notices = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 15;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("NoticesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedNotices != null && widget.preloadedNotices!.isNotEmpty) {
      print("Preloaded notices found, using them.");
      setState(() {
        _notices = List<Map<String, dynamic>>.from(widget.preloadedNotices!);
        _sortNotices();
        _hasMore = widget.preloadedNotices!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      await _checkForMoreData();
    } else {
      print("No preloaded notices or empty list, loading from database.");
      await _loadNoticesFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final noticesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_notices';
    try {
      final response = await Supabase.instance.client
          .from(noticesTableName)
          .select('id')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(_notices.length, _notices.length + _pageSize - 1);

      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadNoticesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }
    print("_loadNoticesFromSupabase called - initialLoad: $initialLoad, current page: $_page");

    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final noticesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_notices';

    try {
      int startRange;
      if (initialLoad) {
        startRange = 0;
        _page = 0;
      } else {
        startRange = _notices.length;
      }
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(noticesTableName)
          .select('*')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(startRange, endRange);

      final newNotices = List<Map<String, dynamic>>.from(response);

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _notices = newNotices;
          } else {
            _notices.addAll(newNotices);
          }
          _sortNotices();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = newNotices.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching notices: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final noticesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_notices';
    _realtimeChannel = Supabase.instance.client
        .channel('notices')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: noticesTableName,
      callback: (payload) async {
        if (!mounted) return;

        if (payload.eventType == PostgresChangeEvent.insert) {
          final newId = payload.newRecord['id'];
          final newResponse = await Supabase.instance.client
              .from(noticesTableName)
              .select('*')
              .eq('id', newId)
              .single();
          if (mounted) {
            setState(() {
              _notices.add(Map.from(newResponse));
              _sortNotices();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedId = payload.newRecord['id'];
          final updatedResponse = await Supabase.instance.client
              .from(noticesTableName)
              .select('*')
              .eq('id', updatedId)
              .single();
          if (mounted) {
            final updatedNotice = Map<String, dynamic>.from(updatedResponse);
            setState(() {
              _notices = _notices.map((n) {
                return n['id'] == updatedNotice['id'] ? updatedNotice : n;
              }).toList();
              _sortNotices();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _notices.removeWhere((n) => n['id'] == deletedId);
            });
          }
        }
      },
    ).subscribe();
  }

  void _sortNotices() {
    _notices.sort((a, b) {
      try {
        final dateA = DateTime(a['year'], a['month'], a['day']);
        final dateB = DateTime(b['year'], b['month'], b['day']);
        return dateB.compareTo(dateA); // Descending order
      } catch (e) {
        // Handle cases where date parts might be null or invalid
        return 0;
      }
    });
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("NoticesPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreNotices();
    }
  }

  Future<void> _loadMoreNotices() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more notices...");
      await _loadNoticesFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> notice) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => NoticeDetailPage(
            notice: notice,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Noticeboard',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadNoticesFromSupabase(initialLoad: true);
              },
              child: _notices.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No notices available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _notices.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _notices.length) {
                          final notice = _notices[index];
                          return VisibilityDetector(
                            key: Key('notice_${notice['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {},
                            child: _buildNoticeCard(notice, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(Icons.home_outlined, color: theme.colorScheme.onSurface),
                  onPressed: () => Navigator.of(context).popUntil((route) => route.isFirst),
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(Icons.person_outline, color: theme.colorScheme.onSurface),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoticeCard(
    Map<String, dynamic> notice,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = notice['fullname'] ?? 'Untitled Notice';
    final String about = notice['about'] ?? '';

    String dateText = 'Date not specified';
    if (notice['day'] != null && notice['month'] != null && notice['year'] != null) {
      try {
        final date = DateTime(notice['year'], notice['month'], notice['day']);
        dateText = DateFormat('MMMM d, yyyy').format(date);
      } catch (e) {
        print("Error parsing date for notice ${notice['id']}: $e");
        dateText = 'Invalid Date';
      }
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, notice),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.announcement_outlined,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        dateText,
                        style: TextStyle(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}