import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class PrintingDetailPage extends StatefulWidget {
  final Map<String, dynamic> printingLocation;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const PrintingDetailPage({
    Key? key,
    required this.printingLocation,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<PrintingDetailPage> createState() => _PrintingDetailPageState();
}

class _PrintingDetailPageState extends State<PrintingDetailPage> {
  late RealtimeChannel _printingRealtimeChannel;
  String? _imageUrl;
  bool _isLoadingImage = false;

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _printingRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.printingLocation['image_url'];
      _isLoadingImage = false;
    });
  }

  void _setupRealtimeListener() {
    _printingRealtimeChannel = Supabase.instance.client
        .channel('printing_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'printing',
      callback: (payload) async {
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.printingLocation['id']) {
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshPrintingLocation();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshPrintingLocation() async {
    try {
      final response = await Supabase.instance.client
          .from('printing')
          .select('*')
          .eq('id', widget.printingLocation['id'])
          .single();

      if (mounted) {
        setState(() {
          widget.printingLocation.clear();
          widget.printingLocation.addAll(response);
          _loadImageFromPreloadedData();
          _updatePrintingCache();
        });
      }
    } catch (e) {
      print("Error refreshing printing location: $e");
    }
  }

  Future<void> _updatePrintingCache() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final cacheKey = 'printing_locations';
    String? cachedPrintingJson = prefs.getString(cacheKey);

    if (cachedPrintingJson != null) {
      List<Map<String, dynamic>> cachedPrinting = 
          (jsonDecode(cachedPrintingJson) as List<dynamic>).cast<Map<String, dynamic>>();

      for (int i = 0; i < cachedPrinting.length; i++) {
        if (cachedPrinting[i]['id'] == widget.printingLocation['id']) {
          cachedPrinting[i] = widget.printingLocation;
          break;
        }
      }

      await prefs.setString(cacheKey, jsonEncode(cachedPrinting));
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    if (phoneNumber.isEmpty) return;
    final Uri telUri = Uri(scheme: 'tel', path: phoneNumber);
    await _launchUrlHelper(telUri, 'place a call');
  }

  Future<void> _launchNavigation(dynamic latitude, dynamic longitude) async {
    double? lat = double.tryParse(latitude?.toString() ?? '');
    double? lng = double.tryParse(longitude?.toString() ?? '');

    if (lat == null || lng == null) {
      _showErrorSnackbar('Location coordinates are invalid.');
      return;
    }
    final Uri mapUri = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$lat,$lng');
    await _launchUrlHelper(mapUri, 'open navigation');
  }

  Future<void> _launchWhatsapp(String phoneNumber) async {
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[+\s()-]'), '');
    final Uri whatsappUri = Uri.parse('https://wa.me/$cleanNumber');
    await _launchUrlHelper(whatsappUri, 'open WhatsApp');
  }

  Future<void> _launchUrlHelper(Uri url, String actionDescription) async {
    try {
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        throw Exception('Could not launch $url');
      }
    } catch (e) {
      _showErrorSnackbar('Could not $actionDescription.');
    }
  }

  void _showErrorSnackbar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.redAccent,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    // Extract location information
    final String fullname = widget.printingLocation['fullname'] ?? 'Unknown';
    final String building = widget.printingLocation['building'] ?? '';
    final String room = widget.printingLocation['room'] ?? '';
    final String hours = widget.printingLocation['hours'] ?? '';
    final String payment = widget.printingLocation['payment'] ?? '';
    final String about = widget.printingLocation['about'] ?? '';
    final String phone = widget.printingLocation['phone']?.toString() ?? '';
    final String whatsappNumber = widget.printingLocation['whatsapp']?.toString() ?? '';
    final dynamic latitude = widget.printingLocation['latitude'];
    final dynamic longitude = widget.printingLocation['longitude'];

    // Format location text
    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    // Check if contact methods are available
    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isWhatsappAvailable = whatsappNumber.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null &&
        double.tryParse(latitude.toString()) != null &&
        double.tryParse(longitude.toString()) != null;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top image section
            _isLoadingImage
                ? Container(
                    height: 200,
                    color: theme.colorScheme.surfaceVariant,
                    child: const Center(child: CircularProgressIndicator()),
                  )
                : (_imageUrl != null && _imageUrl!.isNotEmpty)
                    ? CachedNetworkImage(
                        imageUrl: _imageUrl!,
                        height: 200,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          height: 200,
                          color: theme.colorScheme.surfaceVariant,
                          child: const Center(child: CircularProgressIndicator()),
                        ),
                        errorWidget: (context, url, error) => _buildPlaceholderImage(theme),
                      )
                    : _buildPlaceholderImage(theme),
            
            // Main content card
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with name and location
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode
                                ? Colors.white.withOpacity(0.1)
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.print,
                              size: 30,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  fullname,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                if (locationText.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      locationText,
                                      style: TextStyle(
                                        color: theme.colorScheme.onSurfaceVariant,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Printing details
                      if (hours.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.access_time, 
                          'Hours', 
                          hours
                        ),
                      
                      if (payment.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.payment, 
                          'Payment Methods', 
                          payment
                        ),
                      
                      if (phone.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.phone, 
                          'Phone', 
                          phone, 
                          onTap: () => _launchDialer(phone),
                          canCopy: true,
                        ),
                      
                      if (whatsappNumber.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          FontAwesomeIcons.whatsapp, 
                          'WhatsApp', 
                          whatsappNumber, 
                          onTap: () => _launchWhatsapp(whatsappNumber),
                          canCopy: true,
                        ),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 24),
                        _buildSectionTitle(theme, Icons.info_outline, 'About this Location'),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 14,
                            height: 1.5,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      // Bottom action buttons
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: Icon(
                  Icons.call,
                  color: isPhoneAvailable
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withOpacity(0.5),
                ),
                onPressed: isPhoneAvailable ? () => _launchDialer(phone) : null,
                tooltip: isPhoneAvailable ? 'Call $phone' : 'Phone not available',
              ),
              IconButton(
                icon: Icon(
                  Icons.navigation,
                  color: isNavigationAvailable
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withOpacity(0.5),
                ),
                onPressed: isNavigationAvailable 
                  ? () => _launchNavigation(latitude, longitude) 
                  : null,
                tooltip: isNavigationAvailable ? 'Navigate' : 'Navigation not available',
              ),
              IconButton(
                icon: FaIcon(
                  FontAwesomeIcons.whatsapp,
                  color: isWhatsappAvailable
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withOpacity(0.5),
                ),
                onPressed: isWhatsappAvailable 
                  ? () => _launchWhatsapp(whatsappNumber) 
                  : null,
                tooltip: isWhatsappAvailable ? 'WhatsApp $whatsappNumber' : 'WhatsApp not available',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage(ThemeData theme) {
    return Container(
      height: 200,
      color: theme.colorScheme.surfaceVariant,
      child: Center(
        child: Icon(
          Icons.print,
          size: 50,
          color: theme.colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    ThemeData theme, 
    IconData icon, 
    String title, 
    dynamic value, {
    bool canCopy = false, 
    VoidCallback? onTap,
  }) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    final bool isClickable = onTap != null;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurface,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                    fontSize: 13,
                  ),
                ),
                const SizedBox(height: 4),
                InkWell(
                  onTap: onTap,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          value.toString(),
                          style: TextStyle(
                            color: theme.colorScheme.onSurface,
                            decoration: isClickable 
                                ? TextDecoration.underline 
                                : TextDecoration.none,
                            fontSize: 15,
                          ),
                        ),
                      ),
                      if (canCopy)
                        SizedBox(
                          width: 30,
                          height: 30,
                          child: IconButton(
                            icon: Icon(
                              Icons.content_copy,
                              size: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                            onPressed: () {
                              Clipboard.setData(ClipboardData(text: value.toString()));
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('$title copied to clipboard'),
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            },
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(ThemeData theme, IconData icon, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, top: 8.0),
      child: Row(
        children: [
          Icon(
            icon, 
            color: theme.colorScheme.onSurface, 
            size: 20
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}