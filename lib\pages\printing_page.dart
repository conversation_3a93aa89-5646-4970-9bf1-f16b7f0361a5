import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'printing_detail_page.dart';
import 'login_page.dart';

class PrintingPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedPrintingLocations;
  final bool isFromDetailPage;

  const PrintingPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedPrintingLocations,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _PrintingPageState createState() => _PrintingPageState();
}

class _PrintingPageState extends State<PrintingPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('printing_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _printingLocations = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 15;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("PrintingPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedPrintingLocations != null &&
        widget.preloadedPrintingLocations!.isNotEmpty) {
      print("Preloaded printing locations found, using them.");
      setState(() {
        _printingLocations = List<Map<String, dynamic>>.from(widget.preloadedPrintingLocations!);
        _sortLocations();
        _hasMore = widget.preloadedPrintingLocations!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      await _checkForMoreData();
    } else {
      print("No preloaded locations or empty list, loading from database.");
      await _loadPrintingLocationsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final printingTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_printing';
    try {
      final response = await Supabase.instance.client
          .from(printingTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_printingLocations.length, _printingLocations.length + _pageSize - 1);

      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadPrintingLocationsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }
    print("_loadPrintingLocationsFromSupabase called - initialLoad: $initialLoad, current page: $_page");

    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final printingTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_printing';

    try {
      int startRange;
      if (initialLoad) {
        startRange = 0;
        _page = 0;
      } else {
        startRange = _printingLocations.length;
      }
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(printingTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final newLocations = List<Map<String, dynamic>>.from(response);

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _printingLocations = newLocations;
          } else {
            _printingLocations.addAll(newLocations);
          }
          _sortLocations();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = newLocations.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching printing locations: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final printingTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_printing';
    _realtimeChannel = Supabase.instance.client
        .channel('printing')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: printingTableName,
      callback: (payload) async {
        if (!mounted) return;

        if (payload.eventType == PostgresChangeEvent.insert) {
          final newId = payload.newRecord['id'];
          final newResponse = await Supabase.instance.client
              .from(printingTableName)
              .select('*')
              .eq('id', newId)
              .single();
          if (mounted) {
            setState(() {
              _printingLocations.add(Map.from(newResponse));
              _sortLocations();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedId = payload.newRecord['id'];
          final updatedResponse = await Supabase.instance.client
              .from(printingTableName)
              .select('*')
              .eq('id', updatedId)
              .single();
          if (mounted) {
            final updatedLocation = Map<String, dynamic>.from(updatedResponse);
            setState(() {
              _printingLocations = _printingLocations.map((loc) {
                return loc['id'] == updatedLocation['id'] ? updatedLocation : loc;
              }).toList();
              _sortLocations();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _printingLocations.removeWhere((loc) => loc['id'] == deletedId);
            });
          }
        }
      },
    ).subscribe();
  }
  
  void _sortLocations() {
    _printingLocations.sort((a, b) =>
        (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("PrintingPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMorePrintingLocations();
    }
  }

  Future<void> _loadMorePrintingLocations() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more printing locations...");
      await _loadPrintingLocationsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> location) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PrintingDetailPage(
            printingLocation: location,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Printing Locations',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadPrintingLocationsFromSupabase(initialLoad: true);
              },
              child: _printingLocations.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No printing locations available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _printingLocations.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _printingLocations.length) {
                          final location = _printingLocations[index];
                          return VisibilityDetector(
                            key: Key('printing_${location['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {},
                            child: _buildPrintingCard(location, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(Icons.home_outlined, color: theme.colorScheme.onSurface),
                  onPressed: () => Navigator.of(context).popUntil((route) => route.isFirst),
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(Icons.person_outline, color: theme.colorScheme.onSurface),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPrintingCard(
    Map<String, dynamic> location,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = location['fullname'] ?? 'Unknown Location';
    final String building = location['building'] ?? '';
    final String room = location['room'] ?? '';
    final String hours = location['hours'] ?? '';

    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, location),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.print_outlined,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          hours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}