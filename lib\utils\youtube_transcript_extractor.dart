import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:html/parser.dart' as html_parser;
import 'dart:async';
import 'package:html/dom.dart';

// Custom exception class for better error handling
class TranscriptExtractionException implements Exception {
  final String message;
  final String? details;
  
  TranscriptExtractionException(this.message, [this.details]);
  
  @override
  String toString() {
    if (details != null) {
      return 'TranscriptExtractionException: $message\nDetails: $details';
    }
    return 'TranscriptExtractionException: $message';
  }
}

class YoutubeTranscriptExtractor {
  /// Extracts the video ID from a YouTube URL
  static String? extractVideoId(String url) {
    // Handle various YouTube URL formats
    RegExp regExp = RegExp(
      r'(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})',
      caseSensitive: false,
    );
    
    final match = regExp.firstMatch(url);
    return match?.group(1);
  }

  /// Checks if a URL is a YouTube video URL
  static bool isYoutubeUrl(String url) {
    return url.contains('youtube.com/watch') || 
           url.contains('youtu.be/') || 
           url.contains('youtube.com/embed/');
  }

  /// Extracts transcript from a YouTube video with improved error handling and fallbacks
  static Future<String> extractTranscript(String url) async {
    try {
      // Validate and extract video ID
      final videoId = extractVideoId(url);
      if (videoId == null) {
        throw TranscriptExtractionException('Could not extract YouTube video ID from URL');
      }

      // Track all errors for comprehensive reporting if all methods fail
      List<String> errorMessages = [];
      
      // First approach: Try to get transcript via YouTube's transcript API
      try {
        print('Attempting to extract transcript via API for video ID: $videoId');
        final transcript = await _getTranscriptViaApi(videoId);
        if (transcript.isNotEmpty) {
          print('Successfully extracted transcript via API');
          return transcript;
        }
        errorMessages.add('API returned empty transcript');
      } catch (e) {
        print('API transcript extraction failed: $e');
        errorMessages.add('API method: $e');
        // Continue to fallback method
      }

      // Second approach: Try to scrape the transcript from the HTML page
      try {
        print('Attempting to extract transcript via HTML scraping for video ID: $videoId');
        final transcript = await _scrapeTranscriptFromPage(videoId);
        if (transcript.isNotEmpty) {
          print('Successfully extracted transcript via HTML scraping');
          return transcript;
        }
        errorMessages.add('HTML scraping returned empty transcript');
      } catch (e) {
        print('HTML scraping transcript extraction failed: $e');
        errorMessages.add('HTML scraping method: $e');
      }

      // Third approach: Try to use the InnerTube API (YouTube's internal API)
      try {
        print('Attempting to extract transcript via InnerTube API for video ID: $videoId');
        final transcript = await _getTranscriptViaInnerTubeApi(videoId);
        if (transcript.isNotEmpty) {
          print('Successfully extracted transcript via InnerTube API');
          return transcript;
        }
        errorMessages.add('InnerTube API returned empty transcript');
      } catch (e) {
        print('InnerTube API transcript extraction failed: $e');
        errorMessages.add('InnerTube API method: $e');
      }

      // If all transcript methods fail, try to at least get video metadata
      print('All transcript extraction methods failed, falling back to video metadata');
      try {
        final metadata = await _getVideoMetadata(videoId);
        return metadata;
      } catch (e) {
        print('Video metadata extraction failed: $e');
        errorMessages.add('Metadata fallback: $e');
        
        // Last resort: Return a message with the video URL and all error details
        return '''YouTube Video: $url

Transcript could not be extracted. Please try one of the following:
1. Open the video on YouTube and check if it has captions/subtitles
2. Try a different video
3. Copy the content manually

Technical details: ${errorMessages.join(', ')}''';
      }
    } catch (e) {
      print('Fatal error in transcript extraction: $e');
      return '''YouTube Video: $url

Transcript extraction failed: $e

Please try a different video or copy the content manually.''';
    }
  }

  /// Attempts to get transcript via YouTube's transcript API with improved error handling
  static Future<String> _getTranscriptViaApi(String videoId) async {
    // Try multiple language options and formats
    final languageCodes = ['en', 'en-US', 'en-GB', 'auto', 'a.en'];
    final formats = ['', '&fmt=srv3', '&fmt=srv2', '&fmt=srv1'];
    String combinedErrors = '';

    for (final lang in languageCodes) {
      for (final format in formats) {
        try {
          // YouTube's transcript API endpoint with different formats
          final url = 'https://www.youtube.com/api/timedtext?lang=$lang&v=$videoId$format';
          print('Trying transcript API with language: $lang, format: $format');

          final response = await http.get(
            Uri.parse(url),
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
              'Accept': 'text/xml,application/xml,application/xhtml+xml,text/html;q=0.9,text/plain;q=0.8,image/png,*/*;q=0.5',
              'Accept-Language': 'en-US,en;q=0.5',
              'Accept-Encoding': 'gzip, deflate',
              'Connection': 'keep-alive',
            },
          );

          if (response.statusCode != 200) {
            combinedErrors += 'Lang $lang$format: HTTP ${response.statusCode}, ';
            continue;
          }

          // Check if response is empty or just contains empty XML
          if (response.body.trim().isEmpty ||
              response.body.trim() == '<?xml version="1.0" encoding="utf-8" ?><transcript/>' ||
              response.body.contains('<transcript/>')) {
            combinedErrors += 'Lang $lang$format: Empty response, ';
            continue;
          }

          // Try to parse as XML first
          try {
            final document = html_parser.parse(response.body);
            final textElements = document.querySelectorAll('text');

            if (textElements.isNotEmpty) {
              final StringBuffer transcriptBuffer = StringBuffer();
              for (var element in textElements) {
                final text = element.text.trim();
                if (text.isNotEmpty) {
                  transcriptBuffer.writeln(text);
                }
              }

              final result = transcriptBuffer.toString().trim();
              if (result.isNotEmpty) {
                print('Successfully extracted transcript via API with lang: $lang, format: $format');
                return result;
              }
            }
          } catch (xmlError) {
            print('XML parsing failed for $lang$format: $xmlError');
          }

          // Try to parse as JSON if XML parsing fails
          try {
            final jsonData = json.decode(response.body);
            if (jsonData is Map && jsonData.containsKey('events')) {
              final StringBuffer transcriptBuffer = StringBuffer();
              for (var event in jsonData['events']) {
                if (event['segs'] != null) {
                  for (var seg in event['segs']) {
                    if (seg['utf8'] != null) {
                      transcriptBuffer.write(seg['utf8']);
                    }
                  }
                  transcriptBuffer.writeln();
                }
              }

              final result = transcriptBuffer.toString().trim();
              if (result.isNotEmpty) {
                print('Successfully extracted transcript via JSON API with lang: $lang, format: $format');
                return result;
              }
            }
          } catch (jsonError) {
            print('JSON parsing failed for $lang$format: $jsonError');
          }

          combinedErrors += 'Lang $lang$format: No valid content found, ';
        } catch (e) {
          combinedErrors += 'Lang $lang$format: $e, ';
        }
      }
    }

    throw TranscriptExtractionException('No transcript found via API', combinedErrors);
  }

  /// Attempts to scrape transcript from the YouTube page with improved error handling
  static Future<String> _scrapeTranscriptFromPage(String videoId) async {
    // Use a proper User-Agent to avoid being blocked
    final headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept-Language': 'en-US,en;q=0.9',
    };
    
    final url = 'https://www.youtube.com/watch?v=$videoId';
    
    try {
      final response = await http.get(Uri.parse(url), headers: headers);
      if (response.statusCode != 200) {
        throw TranscriptExtractionException('Failed to fetch YouTube page', 'HTTP ${response.statusCode}');
      }

      // Look for transcript data in the page
      final pageContent = response.body;
      
      // Enhanced regex patterns to find transcript data
      final regexPatterns = [
        // Pattern 1: Look for transcript renderer data
        RegExp(r'"transcriptRenderer":\s*(\{[^}]*\})', dotAll: true),

        // Pattern 2: Look for transcript cue groups
        RegExp(r'"transcriptCueGroupRenderer":\s*(\{[^}]*\})', dotAll: true),

        // Pattern 3: Look for caption tracks (most reliable)
        RegExp(r'"captionTracks":\s*(\[[^\]]*\])', dotAll: true),

        // Pattern 4: Look for player caption tracks
        RegExp(r'"playerCaptionsTracklistRenderer":\s*\{[^}]*"captionTracks":\s*(\[[^\]]*\])', dotAll: true),

        // Pattern 5: Look for automatic captions
        RegExp(r'"automaticCaptions":\s*(\{[^}]*\})', dotAll: true),

        // Pattern 6: Look for timedtext data
        RegExp(r'"timedtext"[^}]*"baseUrl":\s*"([^"]*)"', dotAll: true),
      ];
      
      for (int i = 0; i < regexPatterns.length; i++) {
        final pattern = regexPatterns[i];
        final matches = pattern.allMatches(pageContent);

        for (final match in matches) {
          if (match.group(1) != null) {
            final jsonStr = match.group(1)!;

            try {
              // Handle different patterns
              if (i == 0) {
                // Handle transcriptRenderer format
                final jsonData = json.decode(jsonStr);
                final result = _extractFromTranscriptRenderer(jsonData);
                if (result.isNotEmpty) return result;
              } else if (i == 1) {
                // Handle transcriptCueGroupRenderer format
                final jsonData = json.decode(jsonStr);
                final result = _extractFromCueGroupRenderer(jsonData);
                if (result.isNotEmpty) return result;
              } else if (i == 2 || i == 3) {
                // Handle captionTracks format
                final jsonData = json.decode(jsonStr);
                final result = await _extractFromCaptionTracks(jsonData);
                if (result.isNotEmpty) return result;
              } else if (i == 4) {
                // Handle automaticCaptions format
                final jsonData = json.decode(jsonStr);
                final result = await _extractFromAutomaticCaptions(jsonData);
                if (result.isNotEmpty) return result;
              } else if (i == 5) {
                // Handle direct timedtext URL
                final timedtextUrl = jsonStr;
                final result = await _fetchFromTimedTextUrl(timedtextUrl);
                if (result.isNotEmpty) return result;
              }
            } catch (e) {
              print('Error parsing JSON from pattern $i: $e');
              continue;
            }
          }
        }
      }
      
      throw TranscriptExtractionException('No transcript data found in page');
    } catch (e) {
      if (e is TranscriptExtractionException) {
        rethrow;
      }
      throw TranscriptExtractionException('Failed to scrape transcript', e.toString());
    }
  }
  
  /// Helper method to extract transcript from transcriptRenderer format
  static String _extractFromTranscriptRenderer(dynamic jsonData) {
    final StringBuffer transcriptBuffer = StringBuffer();
    
    try {
      if (jsonData['content'] != null) {
        final content = jsonData['content'];
        if (content is List) {
          for (var item in content) {
            if (item['transcriptSegmentRenderer'] != null) {
              final segment = item['transcriptSegmentRenderer'];
              if (segment['snippet'] != null) {
                final text = segment['snippet']['simpleText'] ?? '';
                if (text.isNotEmpty) {
                  transcriptBuffer.writeln(text);
                }
              }
            }
          }
        }
      }
    } catch (e) {
      print('Error extracting from transcriptRenderer: $e');
    }
    
    return transcriptBuffer.toString().trim();
  }
  
  /// Helper method to extract transcript from cueGroupRenderer format
  static String _extractFromCueGroupRenderer(dynamic jsonData) {
    final StringBuffer transcriptBuffer = StringBuffer();
    
    try {
      if (jsonData['cues'] != null) {
        final cues = jsonData['cues'];
        if (cues is List) {
          for (var cue in cues) {
            if (cue['transcriptCueRenderer'] != null) {
              final renderer = cue['transcriptCueRenderer'];
              if (renderer['cue'] != null) {
                final cueData = renderer['cue'];
                String text = '';
                
                if (cueData['simpleText'] != null) {
                  text = cueData['simpleText'];
                } else if (cueData['runs'] != null) {
                  for (var run in cueData['runs']) {
                    text += run['text'] ?? '';
                  }
                }
                
                if (text.isNotEmpty) {
                  transcriptBuffer.writeln(text);
                }
              }
            }
          }
        }
      }
    } catch (e) {
      print('Error extracting from cueGroupRenderer: $e');
    }
    
    return transcriptBuffer.toString().trim();
  }
  
  /// Helper method to extract transcript from captionTracks format
  static Future<String> _extractFromCaptionTracks(dynamic jsonData) async {
    if (jsonData is! List || jsonData.isEmpty) {
      return '';
    }
    
    // Try to find English captions first
    var captionTrack = jsonData.firstWhere(
      (track) => track['languageCode'] == 'en',
      orElse: () => jsonData.first,
    );
    
    if (captionTrack['baseUrl'] == null) {
      return '';
    }
    
    final captionUrl = captionTrack['baseUrl'];
    try {
      final response = await http.get(Uri.parse(captionUrl));
      if (response.statusCode != 200) {
        return '';
      }
      
      // Parse XML response
      final document = html_parser.parse(response.body);
      final textElements = document.querySelectorAll('text');
      
      final StringBuffer transcriptBuffer = StringBuffer();
      for (var element in textElements) {
        final text = element.text.trim();
        if (text.isNotEmpty) {
          transcriptBuffer.writeln(text);
        }
      }
      
      return transcriptBuffer.toString().trim();
    } catch (e) {
      print('Error fetching caption track: $e');
      return '';
    }
  }

  /// Helper method to extract transcript from automaticCaptions format
  static Future<String> _extractFromAutomaticCaptions(dynamic jsonData) async {
    if (jsonData is! Map) {
      return '';
    }

    // Look for English automatic captions
    final languages = ['en', 'en-US', 'en-GB'];

    for (final lang in languages) {
      if (jsonData[lang] != null) {
        final langData = jsonData[lang];
        if (langData is List && langData.isNotEmpty) {
          final captionTrack = langData.first;
          if (captionTrack['baseUrl'] != null) {
            final captionUrl = captionTrack['baseUrl'];
            try {
              final response = await http.get(Uri.parse(captionUrl));
              if (response.statusCode == 200) {
                // Parse XML response
                final document = html_parser.parse(response.body);
                final textElements = document.querySelectorAll('text');

                final StringBuffer transcriptBuffer = StringBuffer();
                for (var element in textElements) {
                  final text = element.text.trim();
                  if (text.isNotEmpty) {
                    transcriptBuffer.writeln(text);
                  }
                }

                final result = transcriptBuffer.toString().trim();
                if (result.isNotEmpty) {
                  return result;
                }
              }
            } catch (e) {
              print('Error fetching automatic caption for $lang: $e');
            }
          }
        }
      }
    }

    return '';
  }

  /// Helper method to fetch transcript from a timedtext URL
  static Future<String> _fetchFromTimedTextUrl(String url) async {
    try {
      // Clean up the URL if needed
      String cleanUrl = url.replaceAll(r'\u0026', '&').replaceAll(r'\\', '');

      final response = await http.get(
        Uri.parse(cleanUrl),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
      );

      if (response.statusCode == 200) {
        // Parse XML response
        final document = html_parser.parse(response.body);
        final textElements = document.querySelectorAll('text');

        final StringBuffer transcriptBuffer = StringBuffer();
        for (var element in textElements) {
          final text = element.text.trim();
          if (text.isNotEmpty) {
            transcriptBuffer.writeln(text);
          }
        }

        return transcriptBuffer.toString().trim();
      }
    } catch (e) {
      print('Error fetching from timedtext URL: $e');
    }

    return '';
  }

  /// Attempts to get transcript via YouTube's InnerTube API
  static Future<String> _getTranscriptViaInnerTubeApi(String videoId) async {
    final url = 'https://www.youtube.com/youtubei/v1/get_transcript?key=AIzaSyAO_FJ2SlqU8Q4STEHLGCilw_Y9_11qcW8';
    
    // InnerTube API requires a specific payload format
    final payload = {
      'context': {
        'client': {
          'clientName': 'WEB',
          'clientVersion': '2.20210721.00.00',
        }
      },
      'params': base64Url.encode(utf8.encode('{"videoId":"$videoId"}')),
    };
    
    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
        body: json.encode(payload),
      );
      
      if (response.statusCode != 200) {
        throw TranscriptExtractionException('InnerTube API request failed', 'HTTP ${response.statusCode}');
      }
      
      final responseData = json.decode(response.body);
      
      // Extract transcript from the response
      final StringBuffer transcriptBuffer = StringBuffer();
      
      if (responseData['actions'] != null) {
        final actions = responseData['actions'];
        if (actions is List && actions.isNotEmpty) {
          for (var action in actions) {
            if (action['updateEngagementPanelAction'] != null) {
              final content = action['updateEngagementPanelAction']['content'];
              if (content['transcriptRenderer'] != null) {
                final transcriptRenderer = content['transcriptRenderer'];
                if (transcriptRenderer['body'] != null) {
                  final body = transcriptRenderer['body'];
                  if (body['transcriptBodyRenderer'] != null) {
                    final bodyRenderer = body['transcriptBodyRenderer'];
                    if (bodyRenderer['cueGroups'] != null) {
                      final cueGroups = bodyRenderer['cueGroups'];
                      for (var cueGroup in cueGroups) {
                        if (cueGroup['transcriptCueGroupRenderer'] != null) {
                          final cueGroupRenderer = cueGroup['transcriptCueGroupRenderer'];
                          if (cueGroupRenderer['cues'] != null) {
                            final cues = cueGroupRenderer['cues'];
                            for (var cue in cues) {
                              if (cue['transcriptCueRenderer'] != null) {
                                final cueRenderer = cue['transcriptCueRenderer'];
                                if (cueRenderer['cue'] != null) {
                                  final cueData = cueRenderer['cue'];
                                  String text = '';
                                  
                                  if (cueData['simpleText'] != null) {
                                    text = cueData['simpleText'];
                                  } else if (cueData['runs'] != null) {
                                    for (var run in cueData['runs']) {
                                      text += run['text'] ?? '';
                                    }
                                  }
                                  
                                  if (text.isNotEmpty) {
                                    transcriptBuffer.writeln(text);
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      
      return transcriptBuffer.toString().trim();
    } catch (e) {
      if (e is TranscriptExtractionException) {
        rethrow;
      }
      throw TranscriptExtractionException('InnerTube API extraction failed', e.toString());
    }
  }

  /// Fallback method to get basic video metadata if transcript extraction fails
  static Future<String> _getVideoMetadata(String videoId) async {
    final url = 'https://www.youtube.com/watch?v=$videoId';
    
    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
      );
      
      if (response.statusCode != 200) {
        throw TranscriptExtractionException('Failed to fetch YouTube page', 'HTTP ${response.statusCode}');
      }

      final document = html_parser.parse(response.body);
      
      // Extract title
      String title = '';
      final titleElement = document.querySelector('meta[property="og:title"]');
      if (titleElement != null) {
        title = titleElement.attributes['content'] ?? '';
      }
      
      // Extract description
      String description = '';
      final descElement = document.querySelector('meta[property="og:description"]');
      if (descElement != null) {
        description = descElement.attributes['content'] ?? '';
      }
      
      // Extract channel name
      String channelName = '';
      final channelElement = document.querySelector('link[itemprop="name"]');
      if (channelElement != null) {
        channelName = channelElement.attributes['content'] ?? '';
      }
      
      // Extract video duration
      String duration = '';
      final durationElement = document.querySelector('meta[itemprop="duration"]');
      if (durationElement != null) {
        duration = durationElement.attributes['content'] ?? '';
      }
      
      // Try to extract more content from the page
      String additionalContent = '';

      // Look for video description in the page content
      final descriptionRegex = RegExp(r'"shortDescription":"([^"]*)"', dotAll: true);
      final descMatch = descriptionRegex.firstMatch(response.body);
      if (descMatch != null && descMatch.group(1) != null) {
        final fullDescription = descMatch.group(1)!
            .replaceAll(r'\n', '\n')
            .replaceAll(r'\"', '"')
            .replaceAll(r'\\', '\\');
        if (fullDescription.isNotEmpty && fullDescription != description) {
          additionalContent += '\n\nFull Description:\n$fullDescription';
        }
      }

      // Look for video keywords/tags
      final keywordsRegex = RegExp(r'"keywords":\s*\[([^\]]*)\]', dotAll: true);
      final keywordsMatch = keywordsRegex.firstMatch(response.body);
      if (keywordsMatch != null && keywordsMatch.group(1) != null) {
        final keywordsStr = keywordsMatch.group(1)!;
        additionalContent += '\n\nKeywords: $keywordsStr';
      }

      return '''
YouTube Video: $url
Title: $title
Channel: $channelName
Duration: $duration
Description: $description$additionalContent

[Note: Full transcript could not be extracted. Processing with available metadata and description.]

This content includes the video title, description, and any available metadata that can be used for processing.
''';
    } catch (e) {
      throw TranscriptExtractionException('Failed to extract video metadata', e.toString());
    }
  }
}
