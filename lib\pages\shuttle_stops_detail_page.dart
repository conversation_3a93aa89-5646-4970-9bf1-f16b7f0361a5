import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:convert';
import 'dart:io';

import 'login_page.dart';

class ShuttleStopsDetailPage extends StatefulWidget {
  final Map<String, dynamic> shuttleStop;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;

  const ShuttleStopsDetailPage({
    Key? key,
    required this.shuttleStop,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
  }) : super(key: key);

  @override
  _ShuttleStopsDetailPageState createState() => _ShuttleStopsDetailPageState();
}

class _ShuttleStopsDetailPageState extends State<ShuttleStopsDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _shuttleStopRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupShuttleStopRealtimeListener();
  }

  @override
  void dispose() {
    _shuttleStopRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.shuttleStop['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupShuttleStopRealtimeListener() {
    final shuttleStopsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_shuttlestops';
    _shuttleStopRealtimeChannel = Supabase.instance.client
        .channel('shuttle_stop_detail_channel')
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: shuttleStopsTableName,
          callback: (payload) async {
            if (payload.newRecord['id'] == widget.shuttleStop['id']) {
              print("Realtime UPDATE event received for THIS shuttle stop: ${widget.shuttleStop['fullname']}");
              _fetchUpdatedShuttleStopData();
            } else {
              print("Realtime UPDATE event received for OTHER shuttle stop, ignoring.");
            }
          },
        )
        .subscribe();
  }

  Future<void> _fetchUpdatedShuttleStopData() async {
    final shuttleStopsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_shuttlestops';
    try {
      final updatedShuttleStopResponse = await Supabase.instance.client
          .from(shuttleStopsTableName)
          .select('*')
          .eq('id', widget.shuttleStop['id'])
          .single();

      if (mounted && updatedShuttleStopResponse != null) {
        Map<String, dynamic> updatedShuttleStop = Map.from(updatedShuttleStopResponse);
        setState(() {
          widget.shuttleStop.clear();
          widget.shuttleStop.addAll(updatedShuttleStop);
          _loadImageFromPreloadedData();
          print("Shuttle stop data updated in detail page for ${widget.shuttleStop['fullname']}");
          _updateShuttleStopsCache(updatedShuttleStop);
        });
      }
    } catch (error) {
      print("Error fetching updated shuttle stop data: $error");
    }
  }

  Future<void> _updateShuttleStopsCache(Map<String, dynamic> updatedShuttleStop) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForTable;
    final cacheKey = 'shuttlestops_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedShuttleStopsJson = prefs.getString(cacheKey);

    if (cachedShuttleStopsJson != null) {
      List<Map<String, dynamic>> cachedShuttleStops = (jsonDecode(cachedShuttleStopsJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      for (int i = 0; i < cachedShuttleStops.length; i++) {
        if (cachedShuttleStops[i]['id'] == updatedShuttleStop['id']) {
          cachedShuttleStops[i] = updatedShuttleStop;
          break;
        }
      }

      await prefs.setString(cacheKey, jsonEncode(cachedShuttleStops));
      print("Shuttle stops cache updated with realtime change for ${updatedShuttleStop['fullname']}");
    }
  }

  Future<void> _launchNavigation(double latitude, double longitude) async {
    final Uri url = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch navigation.')),
      );
    }
  }

  Future<void> _shareShuttleStop() async {
    final String fullname = widget.shuttleStop['fullname'] ?? 'Unknown Shuttle Stop';
    final String building = widget.shuttleStop['building'] ?? '';
    final String room = widget.shuttleStop['room'] ?? '';
    final String location = widget.shuttleStop['location'] ?? '';
    final String schedule = widget.shuttleStop['schedule'] ?? '';
    final String routes = widget.shuttleStop['routes'] ?? '';
    final double? latitude = widget.shuttleStop['latitude']?.toDouble();
    final double? longitude = widget.shuttleStop['longitude']?.toDouble();

    String shareText = 'Shuttle Stop: $fullname\n';
    
    if (building.isNotEmpty) shareText += 'Building: $building\n';
    if (room.isNotEmpty) shareText += 'Room: $room\n';
    if (location.isNotEmpty) shareText += 'Location: $location\n';
    if (schedule.isNotEmpty) shareText += 'Schedule: $schedule\n';
    if (routes.isNotEmpty) shareText += 'Routes: $routes\n';
    
    if (latitude != null && longitude != null) {
      shareText += 'Map: https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude\n';
    }

    await Share.share(shareText);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bool currentIsDarkMode = widget.isDarkMode;
    final latitude = widget.shuttleStop['latitude']?.toDouble();
    final longitude = widget.shuttleStop['longitude']?.toDouble();
    final building = widget.shuttleStop['building'] as String? ?? '';
    final room = widget.shuttleStop['room'] as String? ?? '';

    final bool isNavigationAvailable = latitude != null && longitude != null;
    final locationText = (building.isNotEmpty && room.isNotEmpty)
        ? '$building $room'
        : (building.isNotEmpty ? building : (room.isNotEmpty ? room : null));
    final bool hasMapCoordinates = latitude != null && longitude != null;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.shuttleStop['fullname'] ?? 'Shuttle Stop',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.share, color: theme.colorScheme.onSurface),
            onPressed: () => _shareShuttleStop(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image Section
            _isLoadingImage
                ? Container(
                    height: 200,
                    color: theme.colorScheme.surfaceVariant,
                    child: const Center(child: CircularProgressIndicator()),
                  )
                : (_imageUrl.isNotEmpty && _imageUrl != 'assets/placeholder_image.png')
                    ? CachedNetworkImage(
                        imageUrl: _imageUrl,
                        height: 200,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          height: 200,
                          color: theme.colorScheme.surfaceVariant,
                          child: const Center(child: CircularProgressIndicator()),
                        ),
                        errorWidget: (context, url, error) => Container(
                          height: 200,
                          color: theme.colorScheme.surfaceVariant,
                          child: Center(
                            child: Icon(
                              Icons.directions_bus,
                              size: 50,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      )
                    : Container(
                        height: 200,
                        color: theme.colorScheme.surfaceVariant,
                        child: Center(
                          child: Icon(
                            Icons.directions_bus,
                            size: 50,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
            
            // Content Section
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header with avatar and name
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CircleAvatar(
                                radius: 30,
                                backgroundColor: currentIsDarkMode
                                    ? Colors.white.withOpacity(0.1)
                                    : Colors.black.withOpacity(0.1),
                                backgroundImage: (_imageUrl.isNotEmpty && _imageUrl != 'assets/placeholder_image.png')
                                    ? NetworkImage(_imageUrl)
                                    : null,
                                child: (_imageUrl.isEmpty || _imageUrl == 'assets/placeholder_image.png')
                                    ? Icon(
                                        Icons.directions_bus,
                                        size: 30,
                                        color: theme.colorScheme.onSurface,
                                      )
                                    : null,
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      widget.shuttleStop['fullname'] ?? 'Unknown Shuttle Stop',
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    if (locationText != null)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: Text(
                                          locationText,
                                          style: TextStyle(
                                            color: theme.colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          
                          // Details Section
                          if (widget.shuttleStop['building'] != null && widget.shuttleStop['building'].toString().isNotEmpty)
                            _buildDetailRow(theme, Icons.business, 'Building', widget.shuttleStop['building']),
                          if (widget.shuttleStop['room'] != null && widget.shuttleStop['room'].toString().isNotEmpty)
                            _buildDetailRow(theme, Icons.meeting_room, 'Room', widget.shuttleStop['room']),
                          if (widget.shuttleStop['location'] != null && widget.shuttleStop['location'].toString().isNotEmpty)
                            _buildDetailRow(theme, Icons.location_on, 'Location', widget.shuttleStop['location']),
                          if (widget.shuttleStop['schedule'] != null && widget.shuttleStop['schedule'].toString().isNotEmpty)
                            _buildDetailRow(theme, Icons.schedule, 'Schedule', widget.shuttleStop['schedule']),
                          if (widget.shuttleStop['routes'] != null && widget.shuttleStop['routes'].toString().isNotEmpty)
                            _buildDetailRow(theme, Icons.route, 'Routes', widget.shuttleStop['routes']),
                          
                          // About Section
                          if (widget.shuttleStop['about'] != null && (widget.shuttleStop['about'] as String).isNotEmpty) ...[
                            const SizedBox(height: 16),
                            _buildSectionTitle(theme, Icons.info_outline, 'About'),
                            const SizedBox(height: 8),
                            Text(
                              widget.shuttleStop['about'] as String,
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                          
                          // Map Section
                          if (hasMapCoordinates) ...[
                            const SizedBox(height: 24),
                            _buildSectionTitle(theme, Icons.map_outlined, 'Location'),
                            const SizedBox(height: 8),
                            SizedBox(
                              height: 200,
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: FlutterMap(
                                  options: MapOptions(
                                    initialCenter: LatLng(latitude!, longitude!),
                                    initialZoom: 16.0,
                                  ),
                                  children: [
                                    TileLayer(
                                      urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                                      userAgentPackageName: 'com.example.yourapp',
                                    ),
                                    MarkerLayer(
                                      markers: [
                                        Marker(
                                          point: LatLng(latitude, longitude),
                                          width: 40,
                                          height: 40,
                                          child: Icon(
                                            Icons.directions_bus,
                                            color: Colors.red,
                                            size: 40,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: Icon(
                  Icons.home_outlined,
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: () {
                  Navigator.of(context).popUntil((route) => route.isFirst);
                },
                tooltip: 'Home',
              ),
              IconButton(
                icon: Icon(
                  Icons.navigation,
                  color: isNavigationAvailable
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withOpacity(0.5),
                ),
                onPressed: isNavigationAvailable ? () => _launchNavigation(latitude!, longitude!) : null,
                tooltip: isNavigationAvailable ? 'Navigate' : 'Navigation not available',
              ),
              IconButton(
                icon: Icon(
                  currentIsDarkMode
                      ? Icons.light_mode_outlined
                      : Icons.dark_mode_outlined,
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: widget.toggleTheme,
                tooltip: currentIsDarkMode ? 'Light Mode' : 'Dark Mode',
              ),
              IconButton(
                icon: Icon(
                  Icons.person_outline,
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LoginPage(
                        isDarkMode: currentIsDarkMode,
                        toggleTheme: widget.toggleTheme,
                      ),
                    ),
                  );
                },
                tooltip: 'Login',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value, {bool canCopy = false, VoidCallback? onTap}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    final bool isClickable = onTap != null;

    Widget content = Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: theme.colorScheme.onSurface,
          size: 20,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      value.toString(),
                      style: TextStyle(
                        color: isClickable ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                        decoration: isClickable ? TextDecoration.underline : TextDecoration.none,
                      ),
                    ),
                  ),
                  if (canCopy)
                    SizedBox(
                      width: 30,
                      height: 30,
                      child: IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy $title',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ],
    );

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: isClickable
          ? InkWell(
              onTap: onTap,
              child: content,
            )
          : content,
    );
  }

  Widget _buildSectionTitle(ThemeData theme, IconData icon, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, color: theme.colorScheme.onSurface, size: 20),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}