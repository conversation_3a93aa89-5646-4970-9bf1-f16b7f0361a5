import 'package:flutter/material.dart';
import 'login_page.dart';
import 'helpdesks_page.dart';
import 'links_page.dart';
import 'accessibility_page.dart';
import 'faqs_page.dart';
import 'construction_page.dart';
import 'printing_page.dart';
import 'daycares_page.dart';
import 'sustainability_page.dart';
import 'tertiary_connectivity_page.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

class TertiaryStartPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool hasHelpdesksPreloaded;
  final bool hasLinksPreloaded;
  final bool isFromDetailPage;

  const TertiaryStartPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.hasHelpdesksPreloaded = false,
    this.hasLinksPreloaded = false,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryStartPage> createState() => _TertiaryStartPageState();
}

class _TertiaryStartPageState extends State<TertiaryStartPage> {
  List<Map<String, dynamic>>? _cachedHelpdesks;
  List<Map<String, dynamic>>? _cachedLinks;
  List<Map<String, dynamic>>? _cachedAccessibilityFeatures;
  List<Map<String, dynamic>>? _cachedFAQs;
  List<Map<String, dynamic>>? _cachedConstructionProjects;
  List<Map<String, dynamic>>? _cachedPrintingLocations;
  List<Map<String, dynamic>>? _cachedDaycares;
  List<Map<String, dynamic>>? _cachedSustainabilityInitiatives;
  String? _lastCollegeName;
  bool _isLoadingHelpdesks = false;
  bool _isLoadingLinks = false;
  bool _isLoadingAccessibility = false;
  bool _isLoadingFAQs = false;
  bool _isLoadingConstruction = false;
  bool _isLoadingPrinting = false;
  bool _isLoadingDaycares = false;
  bool _isLoadingSustainability = false;
  late RealtimeChannel _helpdesksRealtimeChannel;
  late RealtimeChannel _linksRealtimeChannel;
  late RealtimeChannel _accessibilityRealtimeChannel;
  late RealtimeChannel _faqsRealtimeChannel;
  late RealtimeChannel _constructionRealtimeChannel;
  late RealtimeChannel _printingRealtimeChannel;
  late RealtimeChannel _daycaresRealtimeChannel;
  late RealtimeChannel _sustainabilityRealtimeChannel;

  @override
  void initState() {
    super.initState();
    print("TertiaryStartPage initState called for ${widget.institutionName}");
    _loadCachedHelpdesks();
    _loadCachedLinks();
    _loadCachedAccessibilityFeatures();
    _loadCachedFAQs();
    _loadCachedConstructionProjects();
    _loadCachedPrintingLocations();
    _loadCachedDaycares();
    _loadCachedSustainabilityInitiatives();

    _loadHelpdesksFromDatabaseAndCache();
    _loadLinksFromDatabaseAndCache();
    _loadAccessibilityFromDatabaseAndCache();
    _loadFAQsFromDatabaseAndCache();
    _loadConstructionProjectsFromDatabaseAndCache();
    _loadPrintingLocationsFromDatabaseAndCache();
    _loadDaycaresFromDatabaseAndCache();
    _loadSustainabilityInitiativesFromDatabaseAndCache();

    _setupHelpdesksRealtimeListener();
    _setupLinksRealtimeListener();
    _setupAccessibilityRealtimeListener();
    _setupFAQsRealtimeListener();
    _setupConstructionRealtimeListener();
    _setupPrintingRealtimeListener();
    _setupDaycaresRealtimeListener();
    _setupSustainabilityRealtimeListener();
  }

  @override
  void dispose() {
    _helpdesksRealtimeChannel.unsubscribe();
    _linksRealtimeChannel.unsubscribe();
    _accessibilityRealtimeChannel.unsubscribe();
    _faqsRealtimeChannel.unsubscribe();
    _constructionRealtimeChannel.unsubscribe();
    _printingRealtimeChannel.unsubscribe();
    _daycaresRealtimeChannel.unsubscribe();
    _sustainabilityRealtimeChannel.unsubscribe();
    super.dispose();
  }

  Future<void> _loadCachedHelpdesks() async {
    final cachedData = await _getCachedHelpdesks(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedHelpdesks = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded helpdesks from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadHelpdesksFromDatabaseAndCache() async {
    if (_isLoadingHelpdesks) {
      return;
    }

    setState(() {
      _isLoadingHelpdesks = true;
    });

    print("Fetching helpdesks for ${widget.institutionName} from database");
    final helpdesksTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_helpdesks';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(helpdesksTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedHelpdesks = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingHelpdesks = false;
          _cacheHelpdesks(widget.institutionName, response);
          print("Helpdesks fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingHelpdesks = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingHelpdesks = false;
          _cachedHelpdesks = [];
          print("Error fetching helpdesks for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingHelpdesks = false;
      }
    }
  }

  void _setupHelpdesksRealtimeListener() {
    final helpdesksTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_helpdesks';
    _helpdesksRealtimeChannel = Supabase.instance.client
        .channel('helpdesks_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: helpdesksTableName,
      callback: (payload) async {
        print(
            "Realtime update received for helpdesks of ${widget.institutionName}: ${payload.eventType}");
        _loadHelpdesksFromDatabaseAndCache();
      },
    ).subscribe();
  }

  Widget _buildGridItem(
      BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    bool isFeatureEnabled = true;

    if (title == 'Helpdesks') {
      isFeatureEnabled = widget.hasHelpdesksPreloaded;
    } else if (title == 'Links to Resources' || title == 'Accessibility' ||
               title == 'Connectivity' || title == 'Construction & Maintenance' ||
               title == 'Printing' || title == 'Daycares' || title == 'Sustainability') {
      isFeatureEnabled = true; // Always enable these features
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: isFeatureEnabled
            ? () {
                if (title == 'Helpdesks') {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => HelpdesksPage(
                        collegeNameForTable: widget.institutionName,
                        isDarkMode: isDarkMode,
                        toggleTheme: widget.toggleTheme,
                        preloadedHelpdesks: _cachedHelpdesks,
                        isFromDetailPage: widget.isFromDetailPage,
                      ),
                    ),
                  );
                } else if (title == 'Links to Resources') {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LinksPage(
                        collegeNameForTable: widget.institutionName,
                        isDarkMode: isDarkMode,
                        toggleTheme: widget.toggleTheme,
                        preloadedLinks: _cachedLinks,
                        isFromDetailPage: widget.isFromDetailPage,
                      ),
                    ),
                  );
                } else if (title == 'Accessibility') {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AccessibilityPage(
                        collegeNameForTable: widget.institutionName,
                        isDarkMode: isDarkMode,
                        toggleTheme: widget.toggleTheme,
                        preloadedAccessibilityFeatures: _cachedAccessibilityFeatures,
                        isFromDetailPage: widget.isFromDetailPage,
                      ),
                    ),
                  );
                } else if (title == 'Connectivity') {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => TertiaryConnectivityPage(
                        isDarkMode: isDarkMode,
                        toggleTheme: widget.toggleTheme,
                        collegeData: widget.collegeData ?? {},
                        institutionName: widget.institutionName,
                      ),
                    ),
                  );
                } else if (title == 'Construction & Maintenance') {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ConstructionPage(
                        collegeNameForTable: widget.institutionName,
                        isDarkMode: isDarkMode,
                        toggleTheme: widget.toggleTheme,
                        preloadedConstructionProjects: _cachedConstructionProjects,
                        isFromDetailPage: widget.isFromDetailPage,
                      ),
                    ),
                  );
                } else if (title == 'Printing') {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PrintingPage(
                        collegeNameForTable: widget.institutionName,
                        isDarkMode: isDarkMode,
                        toggleTheme: widget.toggleTheme,
                        preloadedPrintingLocations: _cachedPrintingLocations,
                        isFromDetailPage: widget.isFromDetailPage,
                      ),
                    ),
                  );
                } else if (title == 'Daycares') {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => DaycaresPage(
                        collegeNameForTable: widget.institutionName,
                        isDarkMode: isDarkMode,
                        toggleTheme: widget.toggleTheme,
                        preloadedDaycares: _cachedDaycares,
                        isFromDetailPage: widget.isFromDetailPage,
                      ),
                    ),
                  );
                } else if (title == 'Sustainability') {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => SustainabilityPage(
                        collegeNameForTable: widget.institutionName,
                        isDarkMode: isDarkMode,
                        toggleTheme: widget.toggleTheme,
                        preloadedSustainabilityInitiatives: _cachedSustainabilityInitiatives,
                        isFromDetailPage: widget.isFromDetailPage,
                      ),
                    ),
                  );
                }
              }
            : null,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isFeatureEnabled
                    ? (isDarkMode
                        ? theme.colorScheme.secondary
                        : Colors.black)
                    : Colors.grey,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: isFeatureEnabled
                        ? theme.colorScheme.onSurface
                        : Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  } 

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    // Define all grid items with their title and icon.
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'Connectivity', 'icon': Icons.wifi},
      {'title': 'Helpdesks', 'icon': Icons.help_center},
      {'title': 'Accessibility', 'icon': Icons.accessible},
      {'title': 'Links to Resources', 'icon': Icons.link},
      {'title': 'Construction & Maintenance', 'icon': Icons.construction},
      {'title': 'Printing', 'icon': Icons.print},
      {'title': 'Daycares', 'icon': Icons.child_care},
      {'title': 'Sustainability', 'icon': Icons.eco},
    ];

    // Filter out 'Construction & Maintenance' and 'Daycares' if not coming from a detail page.
    final filteredGridItems = gridItems.where((item) {
      if (!widget.isFromDetailPage &&
          (item['title'] == 'Construction & Maintenance' ||
              item['title'] == 'Daycares')) {
        return false;
      }
      return true;
    }).toList();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Start',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, BoxConstraints constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: filteredGridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<List<Map<String, dynamic>>?> _getCachedHelpdesks(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? helpdesksJson = prefs.getString(
        'helpdesks_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (helpdesksJson != null) {
      List<dynamic> decodedList = jsonDecode(helpdesksJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheHelpdesks(String collegeName, List<Map<String, dynamic>> helpdesks) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String helpdesksJson = jsonEncode(helpdesks);
    await prefs.setString(
        'helpdesks_${collegeName.toLowerCase().replaceAll(' ', '')}', helpdesksJson);
    print('Helpdesks cached for $collegeName.');
  }

  // Links methods
  Future<void> _loadCachedLinks() async {
    final cachedData = await _getCachedLinks(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedLinks = cachedData;
        print("Loaded links from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadLinksFromDatabaseAndCache() async {
    if (_isLoadingLinks) {
      return;
    }

    setState(() {
      _isLoadingLinks = true;
    });

    print("Fetching links for ${widget.institutionName} from database");
    final linksTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_links';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(linksTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedLinks = response;
          _isLoadingLinks = false;
          _cacheLinks(widget.institutionName, response);
          print("Links fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingLinks = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingLinks = false;
          _cachedLinks = [];
          print("Error fetching links for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingLinks = false;
      }
    }
  }

  void _setupLinksRealtimeListener() {
    final linksTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_links';
    _linksRealtimeChannel = Supabase.instance.client
        .channel('links_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: linksTableName,
      callback: (payload) async {
        print(
            "Realtime update received for links of ${widget.institutionName}: ${payload.eventType}");
        _loadLinksFromDatabaseAndCache();
      },
    ).subscribe();
  }

  Future<List<Map<String, dynamic>>?> _getCachedLinks(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? linksJson = prefs.getString(
        'links_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (linksJson != null) {
      List<dynamic> decodedList = jsonDecode(linksJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheLinks(String collegeName, List<Map<String, dynamic>> links) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String linksJson = jsonEncode(links);
    await prefs.setString(
        'links_${collegeName.toLowerCase().replaceAll(' ', '')}', linksJson);
    print('Links cached for $collegeName.');
  }

  Future<void> _loadCachedAccessibilityFeatures() async {
    final cachedData = await _getCachedAccessibilityFeatures(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedAccessibilityFeatures = cachedData;
        print("Loaded accessibility features from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadAccessibilityFromDatabaseAndCache() async {
    if (_isLoadingAccessibility) {
      return;
    }

    setState(() {
      _isLoadingAccessibility = true;
    });

    print("Fetching accessibility features for ${widget.institutionName} from database");
    final accessibilityTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_accessibility';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(accessibilityTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedAccessibilityFeatures = response;
          _isLoadingAccessibility = false;
          _cacheAccessibilityFeatures(widget.institutionName, response);
          print("Accessibility features fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingAccessibility = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingAccessibility = false;
          _cachedAccessibilityFeatures = [];
          print("Error fetching accessibility features for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingAccessibility = false;
      }
    }
  }

  void _setupAccessibilityRealtimeListener() {
    final accessibilityTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_accessibility';
    _accessibilityRealtimeChannel = Supabase.instance.client
        .channel('accessibility_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: accessibilityTableName,
      callback: (payload) async {
        print(
            "Realtime update received for accessibility features of ${widget.institutionName}: ${payload.eventType}");
        _loadAccessibilityFromDatabaseAndCache();
      },
    ).subscribe();
  }

  Future<List<Map<String, dynamic>>?> _getCachedAccessibilityFeatures(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? accessibilityJson = prefs.getString(
        'accessibility_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (accessibilityJson != null) {
      List<dynamic> decodedList = jsonDecode(accessibilityJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheAccessibilityFeatures(String collegeName, List<Map<String, dynamic>> accessibilityFeatures) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String accessibilityJson = jsonEncode(accessibilityFeatures);
    await prefs.setString(
        'accessibility_${collegeName.toLowerCase().replaceAll(' ', '')}', accessibilityJson);
    print('Accessibility features cached for $collegeName.');
  }

  // FAQs methods
  Future<List<Map<String, dynamic>>?> _getCachedFAQs(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? faqsJson = prefs.getString(
        'faqs_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (faqsJson != null) {
      List<dynamic> decodedList = jsonDecode(faqsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheFAQs(String collegeName, List<Map<String, dynamic>> faqs) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String faqsJson = jsonEncode(faqs);
    await prefs.setString(
        'faqs_${collegeName.toLowerCase().replaceAll(' ', '')}', faqsJson);
    print('FAQs cached for $collegeName.');
  }

  Future<void> _loadCachedFAQs() async {
    final cachedData = await _getCachedFAQs(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedFAQs = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded FAQs from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadFAQsFromDatabaseAndCache() async {
    if (_isLoadingFAQs) {
      return;
    }

    setState(() {
      _isLoadingFAQs = true;
    });

    print("Fetching FAQs for ${widget.institutionName} from database");
    final faqsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_faqs';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(faqsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedFAQs = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingFAQs = false;
          _cacheFAQs(widget.institutionName, response);
          print("FAQs fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingFAQs = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingFAQs = false;
          _cachedFAQs = [];
          print("Error fetching FAQs for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingFAQs = false;
      }
    }
  }

  void _setupFAQsRealtimeListener() {
    final faqsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_faqs';
    _faqsRealtimeChannel = Supabase.instance.client
        .channel('faqs_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: faqsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for FAQs of ${widget.institutionName}: ${payload.eventType}");
        _loadFAQsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Construction methods
  Future<List<Map<String, dynamic>>?> _getCachedConstructionProjects(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? constructionJson = prefs.getString(
        'construction_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (constructionJson != null) {
      List<dynamic> decodedList = jsonDecode(constructionJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheConstructionProjects(String collegeName, List<Map<String, dynamic>> projects) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String constructionJson = jsonEncode(projects);
    await prefs.setString(
        'construction_${collegeName.toLowerCase().replaceAll(' ', '')}', constructionJson);
    print('Construction projects cached for $collegeName.');
  }

  Future<void> _loadCachedConstructionProjects() async {
    final cachedData = await _getCachedConstructionProjects(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedConstructionProjects = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded construction projects from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadConstructionProjectsFromDatabaseAndCache() async {
    if (_isLoadingConstruction) {
      return;
    }

    setState(() {
      _isLoadingConstruction = true;
    });

    print("Fetching construction projects for ${widget.institutionName} from database");
    final constructionTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_construction';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(constructionTableName)
          .select('*')
          .order('startdate', ascending: false);

      if (mounted) {
        setState(() {
          _cachedConstructionProjects = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingConstruction = false;
          _cacheConstructionProjects(widget.institutionName, response);
          print("Construction projects fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingConstruction = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingConstruction = false;
          _cachedConstructionProjects = [];
          print("Error fetching construction projects for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingConstruction = false;
      }
    }
  }

  void _setupConstructionRealtimeListener() {
    final constructionTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_construction';
    _constructionRealtimeChannel = Supabase.instance.client
        .channel('construction_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: constructionTableName,
      callback: (payload) async {
        print(
            "Realtime update received for construction projects of ${widget.institutionName}: ${payload.eventType}");
        _loadConstructionProjectsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Printing methods
  Future<List<Map<String, dynamic>>?> _getCachedPrintingLocations(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? printingJson = prefs.getString(
        'printing_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (printingJson != null) {
      List<dynamic> decodedList = jsonDecode(printingJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cachePrintingLocations(String collegeName, List<Map<String, dynamic>> locations) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String printingJson = jsonEncode(locations);
    await prefs.setString(
        'printing_${collegeName.toLowerCase().replaceAll(' ', '')}', printingJson);
    print('Printing locations cached for $collegeName.');
  }

  Future<void> _loadCachedPrintingLocations() async {
    final cachedData = await _getCachedPrintingLocations(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedPrintingLocations = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded printing locations from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadPrintingLocationsFromDatabaseAndCache() async {
    if (_isLoadingPrinting) {
      return;
    }

    setState(() {
      _isLoadingPrinting = true;
    });

    print("Fetching printing locations for ${widget.institutionName} from database");
    final printingTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_printing';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(printingTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedPrintingLocations = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingPrinting = false;
          _cachePrintingLocations(widget.institutionName, response);
          print("Printing locations fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingPrinting = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingPrinting = false;
          _cachedPrintingLocations = [];
          print("Error fetching printing locations for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingPrinting = false;
      }
    }
  }

  void _setupPrintingRealtimeListener() {
    final printingTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_printing';
    _printingRealtimeChannel = Supabase.instance.client
        .channel('printing_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: printingTableName,
      callback: (payload) async {
        print(
            "Realtime update received for printing locations of ${widget.institutionName}: ${payload.eventType}");
        _loadPrintingLocationsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Daycares methods
  Future<List<Map<String, dynamic>>?> _getCachedDaycares(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? daycaresJson = prefs.getString(
        'daycares_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (daycaresJson != null) {
      List<dynamic> decodedList = jsonDecode(daycaresJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheDaycares(String collegeName, List<Map<String, dynamic>> daycares) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String daycaresJson = jsonEncode(daycares);
    await prefs.setString(
        'daycares_${collegeName.toLowerCase().replaceAll(' ', '')}', daycaresJson);
    print('Daycares cached for $collegeName.');
  }

  Future<void> _loadCachedDaycares() async {
    final cachedData = await _getCachedDaycares(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedDaycares = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded daycares from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadDaycaresFromDatabaseAndCache() async {
    if (_isLoadingDaycares) {
      return;
    }

    setState(() {
      _isLoadingDaycares = true;
    });

    print("Fetching daycares for ${widget.institutionName} from database");
    final daycaresTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_daycares';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(daycaresTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedDaycares = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingDaycares = false;
          _cacheDaycares(widget.institutionName, response);
          print("Daycares fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingDaycares = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingDaycares = false;
          _cachedDaycares = [];
          print("Error fetching daycares for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingDaycares = false;
      }
    }
  }

  void _setupDaycaresRealtimeListener() {
    final daycaresTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_daycares';
    _daycaresRealtimeChannel = Supabase.instance.client
        .channel('daycares_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: daycaresTableName,
      callback: (payload) async {
        print(
            "Realtime update received for daycares of ${widget.institutionName}: ${payload.eventType}");
        _loadDaycaresFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Sustainability methods
  Future<List<Map<String, dynamic>>?> _getCachedSustainabilityInitiatives(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? sustainabilityJson = prefs.getString(
        'sustainability_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (sustainabilityJson != null) {
      List<dynamic> decodedList = jsonDecode(sustainabilityJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheSustainabilityInitiatives(String collegeName, List<Map<String, dynamic>> initiatives) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String sustainabilityJson = jsonEncode(initiatives);
    await prefs.setString(
        'sustainability_${collegeName.toLowerCase().replaceAll(' ', '')}', sustainabilityJson);
    print('Sustainability initiatives cached for $collegeName.');
  }

  Future<void> _loadCachedSustainabilityInitiatives() async {
    final cachedData = await _getCachedSustainabilityInitiatives(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedSustainabilityInitiatives = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded sustainability initiatives from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadSustainabilityInitiativesFromDatabaseAndCache() async {
    if (_isLoadingSustainability) {
      return;
    }

    setState(() {
      _isLoadingSustainability = true;
    });

    print("Fetching sustainability initiatives for ${widget.institutionName} from database");
    final sustainabilityTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_sustainability';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(sustainabilityTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedSustainabilityInitiatives = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingSustainability = false;
          _cacheSustainabilityInitiatives(widget.institutionName, response);
          print("Sustainability initiatives fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingSustainability = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingSustainability = false;
          _cachedSustainabilityInitiatives = [];
          print("Error fetching sustainability initiatives for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingSustainability = false;
      }
    }
  }

  void _setupSustainabilityRealtimeListener() {
    final sustainabilityTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_sustainability';
    _sustainabilityRealtimeChannel = Supabase.instance.client
        .channel('sustainability_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: sustainabilityTableName,
      callback: (payload) async {
        print(
            "Realtime update received for sustainability initiatives of ${widget.institutionName}: ${payload.eventType}");
        _loadSustainabilityInitiativesFromDatabaseAndCache();
      },
    ).subscribe();
  }
}
