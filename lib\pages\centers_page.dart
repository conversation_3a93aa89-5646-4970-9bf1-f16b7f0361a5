import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'center_detail_page.dart';
import 'login_page.dart';

class CentersPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedCenters;
  final bool isFromDetailPage;

  const CentersPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedCenters,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _CentersPageState createState() => _CentersPageState();
}

class _CentersPageState extends State<CentersPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('centers_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _centers = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("CentersPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedCenters != null && widget.preloadedCenters!.isNotEmpty) {
      print("Preloaded centers found, using them.");
      setState(() {
        _centers = List<Map<String, dynamic>>.from(widget.preloadedCenters!);
        _centers.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedCenters!.length >= _pageSize;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded centers or empty list, loading from database.");
      await _loadCentersFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final centersTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_centers';

    try {
      final response = await Supabase.instance.client
          .from(centersTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_centers.length, _centers.length + _pageSize - 1);

      if (!mounted) return;
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadCentersFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadCentersFromSupabase called - initialLoad: $initialLoad");

    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final centersTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_centers';

    try {
      int startRange;

      if (initialLoad) {
        startRange = 0;
      } else {
        startRange = _centers.length;
      }
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(centersTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      if (mounted) {
        setState(() {
          final newItems = List<Map<String, dynamic>>.from(response);
          if (initialLoad) {
            _centers = newItems;
          } else {
            _centers.addAll(newItems);
          }
          _centers.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') ||
            errorStr.contains('offline')) {
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') &&
            errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching centers: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final centersTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_centers';
    _realtimeChannel = Supabase.instance.client
        .channel('centers_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: centersTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        print("Realtime update received for centers: ${payload.eventType}");

        if (payload.eventType == PostgresChangeEvent.insert) {
          final newCenterId = payload.newRecord['id'];
          final newCenterResponse = await Supabase.instance.client
              .from(centersTableName)
              .select('*')
              .eq('id', newCenterId)
              .single();
          if (mounted) {
            setState(() {
              _centers.add(Map.from(newCenterResponse));
              _centers.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedCenterId = payload.newRecord['id'];
          final updatedCenterResponse = await Supabase.instance.client
              .from(centersTableName)
              .select('*')
              .eq('id', updatedCenterId)
              .single();
          if (mounted) {
            final updatedCenter = Map<String, dynamic>.from(updatedCenterResponse);
            setState(() {
              _centers = _centers.map((center) {
                return center['id'] == updatedCenter['id'] ? updatedCenter : center;
              }).toList();
              _centers.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedCenterId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _centers.removeWhere((center) => center['id'] == deletedCenterId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("CentersPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreCenters();
    }
  }

  Future<void> _loadMoreCenters() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more centers...");
      await _loadCentersFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> center) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CenterDetailPage(
            center: center,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  void _showOfflineSnackbar() {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("CentersPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Centers',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _hasMore = true;
                });
                await _loadCentersFromSupabase(initialLoad: true);
              },
              child: _centers.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No centers available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _centers.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _centers.length) {
                          final center = _centers[index];
                          return _buildCenterCard(
                              center, theme, currentIsDarkMode);
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCenterCard(
    Map<String, dynamic> center,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = center['fullname'] ?? 'Unknown';
    final String building = center['building'] ?? '';
    final String room = center['room'] ?? '';
    final String hours = center['hours'] ?? '';
    final String about = center['about'] ?? '';

    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, center),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.business_center,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          hours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}