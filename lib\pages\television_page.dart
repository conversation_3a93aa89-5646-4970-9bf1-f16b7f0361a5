// television_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'television_detail_page.dart';

class TelevisionPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedTelevisionStations;
  final bool isFromDetailPage;

  const TelevisionPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedTelevisionStations,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TelevisionPage> createState() => _TelevisionPageState();
}

class _TelevisionPageState extends State<TelevisionPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('television_list');
  List<Map<String, dynamic>> _televisionStations = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("TelevisionPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant TelevisionPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("TelevisionPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("TelevisionPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedTelevisionStations != null && widget.preloadedTelevisionStations!.isNotEmpty) {
      print("Preloaded television stations found, using them.");
      setState(() {
        _televisionStations = List<Map<String, dynamic>>.from(widget.preloadedTelevisionStations!);
        _televisionStations.forEach((station) {
          station['_isImageLoading'] = false;
        });
        _televisionStations.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded television stations or empty list, loading from database.");
      await _loadTelevisionStationsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final televisionTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_television';
    
    try {
      final response = await Supabase.instance.client
          .from(televisionTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_televisionStations.length, _televisionStations.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadTelevisionStationsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadTelevisionStationsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final televisionTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_television';

    try {
      int startRange = initialLoad ? 0 : _televisionStations.length;
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(televisionTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedTelevisionStations =
          await _updateTelevisionStationImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _televisionStations = updatedTelevisionStations;
          } else {
            _televisionStations.addAll(updatedTelevisionStations);
          }
          _televisionStations.forEach((station) {
            station['_isImageLoading'] = false;
          });
          _televisionStations.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
      _cacheTelevisionStations(_televisionStations);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching television stations: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateTelevisionStationImageUrls(
      List<Map<String, dynamic>> televisionStations) async {
    List<Future<void>> futures = [];
    for (final station in televisionStations) {
      if (station['image_url'] == null ||
          station['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(station));
      }
    }
    await Future.wait(futures);
    return televisionStations;
  }
  
  void _setupRealtime() {
    final televisionTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_television';
    _realtimeChannel = Supabase.instance.client
        .channel('television')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: televisionTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newStationId = payload.newRecord['id'];
          final newStationResponse = await Supabase.instance.client
              .from(televisionTableName)
              .select('*')
              .eq('id', newStationId)
              .single();
          if (mounted) {
            Map<String, dynamic> newStation = Map.from(newStationResponse);
            final updatedStation = await _updateTelevisionStationImageUrls([newStation]);
            setState(() {
              _televisionStations.add(updatedStation.first);
              updatedStation.first['_isImageLoading'] = false;
              _televisionStations.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedStationId = payload.newRecord['id'];
          final updatedStationResponse = await Supabase.instance.client
              .from(televisionTableName)
              .select('*')
              .eq('id', updatedStationId)
              .single();
          if (mounted) {
            final updatedStation = Map<String, dynamic>.from(updatedStationResponse);
            setState(() {
              _televisionStations = _televisionStations.map((station) {
                return station['id'] == updatedStation['id'] ? updatedStation : station;
              }).toList();
              _televisionStations.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedStationId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _televisionStations.removeWhere((station) => station['id'] == deletedStationId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("TelevisionPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreTelevisionStations();
    }
  }

  Future<void> _loadMoreTelevisionStations() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more television stations...");
      await _loadTelevisionStationsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _cacheTelevisionStations(List<Map<String, dynamic>> televisionStations) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String televisionStationsJson = jsonEncode(televisionStations);
      await prefs.setString(
          'television_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          televisionStationsJson);
    } catch (e) {
      print('Error caching television stations: $e');
    }
  }
  
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> station) async {
    if (station['_isImageLoading'] == true) return;
    if (station['image_url'] != null && station['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => station['_isImageLoading'] = true);

    final fullname = station['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeTelevisionBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/television';

    try {
      final file = await Supabase.instance.client.storage.from(collegeTelevisionBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeTelevisionBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        station['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        station['_isImageLoading'] = false;
      });
    } else {
      station['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> station) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TelevisionDetailPage(
            televisionStation: station,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("TelevisionPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Television',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadTelevisionStationsFromSupabase(initialLoad: true);
              },
              child: _televisionStations.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No television stations available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _televisionStations.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _televisionStations.length) {
                          final station = _televisionStations[index];
                          return VisibilityDetector(
                            key: Key('television_${station['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (station['image_url'] == null ||
                                      station['image_url'] == 'assets/placeholder_image.png') &&
                                  !station['_isImageLoading']) {
                                _fetchImageUrl(station);
                              }
                            },
                            child: _buildTelevisionCard(station, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTelevisionCard(
    Map<String, dynamic> station,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = station['fullname'] ?? 'Unknown';
    final String about = station['about'] ?? '';
    final String imageUrl = station['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, station),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.tv,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.tv,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.tv,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}