// tertiary_connectivity_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'connectivity_detail_page.dart';
import 'login_page.dart';

class Connectivity {
  final int id;
  final String fullname;
  final String about;

  Connectivity({
    required this.id,
    required this.fullname,
    required this.about,
  });

  factory Connectivity.fromJson(Map<String, dynamic> json) {
    return Connectivity(
      id: json['id'] ?? 0,
      fullname: json['fullname'] ?? 'Unnamed Connectivity',
      about: json['about'] ?? 'No description available',
    );
  }

  // Helper method to get wifi icon for all connectivity items
  IconData getIcon() {
    return Icons.wifi;
  }
}

class TertiaryConnectivityPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final List<Map<String, dynamic>>? preloadedConnectivity;
  final bool isFromDetailPage;

  const TertiaryConnectivityPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeData,
    required this.institutionName,
    this.preloadedConnectivity,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryConnectivityPage> createState() => _TertiaryConnectivityPageState();
}

class _TertiaryConnectivityPageState extends State<TertiaryConnectivityPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('connectivity_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _connectivityItems = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("TertiaryConnectivityPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant TertiaryConnectivityPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("TertiaryConnectivityPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("TertiaryConnectivityPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedConnectivity != null && widget.preloadedConnectivity!.isNotEmpty) {
      print("Preloaded connectivity found, using them.");
      setState(() {
        _connectivityItems = List<Map<String, dynamic>>.from(widget.preloadedConnectivity!);
        _connectivityItems.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedConnectivity!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      // Don't load more from database if we have preloaded data
      // But check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded connectivity or empty list, loading from database.");
      await _loadConnectivityFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final connectivityTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_connectivity';
    
    try {
      final response = await Supabase.instance.client
          .from(connectivityTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_connectivityItems.length, _connectivityItems.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadConnectivityFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadConnectivityFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final connectivityTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_connectivity';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _connectivityItems.length;
        endRange = _connectivityItems.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(connectivityTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final List<Map<String, dynamic>> connectivityItems = List<Map<String, dynamic>>.from(response);

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _connectivityItems = connectivityItems;
          } else {
            _connectivityItems.addAll(connectivityItems);
          }
          _connectivityItems.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching connectivity information: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final connectivityTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_connectivity';
    _realtimeChannel = Supabase.instance.client
        .channel('connectivity')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: connectivityTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newConnectivityId = payload.newRecord['id'];
          final newConnectivityResponse = await Supabase.instance.client
              .from(connectivityTableName)
              .select('*')
              .eq('id', newConnectivityId)
              .single();
          if (mounted) {
            Map<String, dynamic> newConnectivity = Map.from(newConnectivityResponse);
            setState(() {
              _connectivityItems.add(newConnectivity);
              _connectivityItems.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedConnectivityId = payload.newRecord['id'];
          final updatedConnectivityResponse = await Supabase.instance.client
              .from(connectivityTableName)
              .select('*')
              .eq('id', updatedConnectivityId)
              .single();
          if (mounted) {
            final updatedConnectivity = Map<String, dynamic>.from(updatedConnectivityResponse);
            setState(() {
              _connectivityItems = _connectivityItems.map((connectivity) {
                return connectivity['id'] == updatedConnectivity['id'] ? updatedConnectivity : connectivity;
              }).toList();
              _connectivityItems.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedConnectivityId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _connectivityItems.removeWhere((connectivity) => connectivity['id'] == deletedConnectivityId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("TertiaryConnectivityPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreConnectivity();
    }
  }

  Future<void> _loadMoreConnectivity() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more connectivity...");
      await _loadConnectivityFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> connectivity) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ConnectivityDetailPage(
            connectivity: connectivity,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            institutionName: widget.institutionName,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("TertiaryConnectivityPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Connectivity',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadConnectivityFromSupabase(initialLoad: true);
              },
              child: _connectivityItems.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No connectivity information available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _connectivityItems.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _connectivityItems.length) {
                          final connectivity = _connectivityItems[index];
                          return VisibilityDetector(
                            key: Key('connectivity_${connectivity['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              // Keep this for consistency with helpdesks page structure
                            },
                            child: _buildConnectivityCard(connectivity, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildConnectivityCard(
    Map<String, dynamic> connectivity,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = connectivity['fullname'] ?? 'Unknown';
    final String about = connectivity['about'] ?? '';
    final connectivityObj = Connectivity.fromJson(connectivity);

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, connectivity),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  connectivityObj.getIcon(),
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}