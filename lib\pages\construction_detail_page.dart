import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class ConstructionDetailPage extends StatefulWidget {
  final Map<String, dynamic> constructionProject;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;

  const ConstructionDetailPage({
    Key? key,
    required this.constructionProject,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
  }) : super(key: key);

  @override
  State<ConstructionDetailPage> createState() => _ConstructionDetailPageState();
}

class _ConstructionDetailPageState extends State<ConstructionDetailPage> {
  late RealtimeChannel _constructionRealtimeChannel;
  late Map<String, dynamic> _currentProjectData;
  String? _imageUrl;
  bool _isLoadingImage = false;

  String get _constructionTableName =>
      '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_construction';

  @override
  void initState() {
    super.initState();
    _currentProjectData = Map<String, dynamic>.from(widget.constructionProject);
    _loadImageFromPreloadedData();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _constructionRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = _currentProjectData['image_url'];
      _isLoadingImage = false;
    });
  }

  void _setupRealtimeListener() {
    _constructionRealtimeChannel = Supabase.instance.client
        .channel('public:$_constructionTableName:id=eq.${_currentProjectData['id']}')
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: _constructionTableName,
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'id',
            value: _currentProjectData['id'],
          ),
          callback: (payload) async {
            if (mounted && payload.newRecord['id'] == _currentProjectData['id']) {
              await _refreshConstructionProjectData();
              _updateConstructionCache();
            }
          },
        )
        .subscribe((status, [_]) {
          if (status == RealtimeSubscribeStatus.channelError || 
              status == RealtimeSubscribeStatus.timedOut) {
            _showErrorSnackbar("Error with real-time updates ($status)");
          }
        });
  }

  Future<void> _refreshConstructionProjectData() async {
    try {
      final response = await Supabase.instance.client
          .from(_constructionTableName)
          .select('*')
          .eq('id', _currentProjectData['id'])
          .single();

      if (mounted) {
        setState(() {
          _currentProjectData = Map<String, dynamic>.from(response);
          _loadImageFromPreloadedData();
        });
      }
    } catch (error) {
      _showErrorSnackbar("Could not refresh project: $error");
    }
  }

  Future<void> _updateConstructionCache() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final cacheKey = 'construction_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedConstructionsJson = prefs.getString(cacheKey);

    if (cachedConstructionsJson != null) {
      List<Map<String, dynamic>> cachedConstructions = 
          (jsonDecode(cachedConstructionsJson) as List<dynamic>).cast<Map<String, dynamic>>();

      for (int i = 0; i < cachedConstructions.length; i++) {
        if (cachedConstructions[i]['id'] == _currentProjectData['id']) {
          cachedConstructions[i] = _currentProjectData;
          break;
        }
      }

      await prefs.setString(cacheKey, jsonEncode(cachedConstructions));
    }
  }

  void _showErrorSnackbar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.redAccent,
        ),
      );
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    if (phoneNumber.isEmpty) return;
    final Uri telUri = Uri(scheme: 'tel', path: phoneNumber);
    await _launchUrlHelper(telUri, 'place a call');
  }

  Future<void> _launchNavigation(dynamic latitude, dynamic longitude) async {
    double? lat = double.tryParse(latitude?.toString() ?? '');
    double? lng = double.tryParse(longitude?.toString() ?? '');

    if (lat == null || lng == null) {
      _showErrorSnackbar('Location coordinates are invalid.');
      return;
    }
    final Uri mapUri = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$lat,$lng');
    await _launchUrlHelper(mapUri, 'open navigation');
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    if (whatsappNumber.isEmpty) return;
    final cleanNumber = whatsappNumber.replaceAll(RegExp(r'[+\s()-]'), '');
    final Uri whatsappUri = Uri.parse('https://wa.me/$cleanNumber');
    await _launchUrlHelper(whatsappUri, 'open WhatsApp');
  }

  Future<void> _launchUrlHelper(Uri url, String actionDescription) async {
    try {
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        throw Exception('Could not launch $url');
      }
    } catch (e) {
      _showErrorSnackbar('Could not $actionDescription.');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final String fullname = _currentProjectData['fullname'] ?? 'Unnamed Project';
    final String location = _currentProjectData['location'] ?? '';
    final String about = _currentProjectData['about'] ?? '';
    final String phone = _currentProjectData['phone']?.toString() ?? '';
    final String whatsapp = _currentProjectData['whatsapp']?.toString() ?? '';
    final dynamic latitude = _currentProjectData['latitude'];
    final dynamic longitude = _currentProjectData['longitude'];

    // Format dates
    String startDateFormatted = '';
    if (_currentProjectData['startdate'] != null) {
      try {
        final startDate = DateTime.parse(_currentProjectData['startdate']);
        startDateFormatted = DateFormat('MMMM d, yyyy').format(startDate);
      } catch (e) {}
    }
    
    String endDateFormatted = '';
    if (_currentProjectData['enddate'] != null) {
      try {
        final endDate = DateTime.parse(_currentProjectData['enddate']);
        endDateFormatted = DateFormat('MMMM d, yyyy').format(endDate);
      } catch (e) {}
    }

    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isWhatsappAvailable = whatsapp.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null &&
        double.tryParse(latitude.toString()) != null &&
        double.tryParse(longitude.toString()) != null;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top image section
            _isLoadingImage
                ? Container(
                    height: 200,
                    color: theme.colorScheme.surfaceVariant,
                    child: const Center(child: CircularProgressIndicator()),
                  )
                : (_imageUrl != null && _imageUrl!.isNotEmpty)
                    ? CachedNetworkImage(
                        imageUrl: _imageUrl!,
                        height: 200,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          height: 200,
                          color: theme.colorScheme.surfaceVariant,
                          child: const Center(child: CircularProgressIndicator()),
                        ),
                        errorWidget: (context, url, error) => _buildPlaceholderImage(theme),
                      )
                    : _buildPlaceholderImage(theme),
            
            // Main content card
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with name and location
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: theme.brightness == Brightness.dark
                                ? Colors.white.withOpacity(0.1)
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.construction,
                              size: 30,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  fullname,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                if (location.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      location,
                                      style: TextStyle(
                                        color: theme.colorScheme.onSurfaceVariant,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Project details
                      if (startDateFormatted.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.calendar_today_outlined, 
                          'Start Date', 
                          startDateFormatted
                        ),
                      
                      if (endDateFormatted.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.event_outlined, 
                          'End Date', 
                          endDateFormatted
                        ),
                      
                      if (phone.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.phone_outlined, 
                          'Contact Phone', 
                          phone, 
                          onTap: () => _launchDialer(phone),
                          canCopy: true,
                        ),
                      
                      if (whatsapp.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          FontAwesomeIcons.whatsapp, 
                          'WhatsApp Contact', 
                          whatsapp, 
                          onTap: () => _launchWhatsapp(whatsapp),
                          canCopy: true,
                        ),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 24),
                        _buildSectionTitle(theme, Icons.info_outline, 'About this Project'),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 14,
                            height: 1.5,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      // Bottom action buttons
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: Icon(
                  Icons.call,
                  color: isPhoneAvailable
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withOpacity(0.5),
                ),
                onPressed: isPhoneAvailable ? () => _launchDialer(phone) : null,
                tooltip: isPhoneAvailable ? 'Call $phone' : 'Phone not available',
              ),
              IconButton(
                icon: Icon(
                  Icons.navigation,
                  color: isNavigationAvailable
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withOpacity(0.5),
                ),
                onPressed: isNavigationAvailable 
                  ? () => _launchNavigation(latitude, longitude) 
                  : null,
                tooltip: isNavigationAvailable ? 'Navigate' : 'Navigation not available',
              ),
              IconButton(
                icon: FaIcon(
                  FontAwesomeIcons.whatsapp,
                  color: isWhatsappAvailable
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withOpacity(0.5),
                ),
                onPressed: isWhatsappAvailable 
                  ? () => _launchWhatsapp(whatsapp) 
                  : null,
                tooltip: isWhatsappAvailable ? 'WhatsApp $whatsapp' : 'WhatsApp not available',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage(ThemeData theme) {
    return Container(
      height: 200,
      color: theme.colorScheme.surfaceVariant,
      child: Center(
        child: Icon(
          Icons.construction,
          size: 50,
          color: theme.colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    ThemeData theme, 
    IconData icon, 
    String title, 
    dynamic value, {
    bool canCopy = false, 
    VoidCallback? onTap,
  }) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    final bool isClickable = onTap != null;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurface,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                    fontSize: 13,
                  ),
                ),
                const SizedBox(height: 4),
                InkWell(
                  onTap: onTap,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          value.toString(),
                          style: TextStyle(
                            // Use onSurface color for both normal and tappable text
                            color: theme.colorScheme.onSurface,
                            decoration: isClickable 
                                ? TextDecoration.underline 
                                : TextDecoration.none,
                            fontSize: 15,
                          ),
                        ),
                      ),
                      if (canCopy)
                        SizedBox(
                          width: 30,
                          height: 30,
                          child: IconButton(
                            icon: Icon(
                              Icons.content_copy,
                              size: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                            onPressed: () {
                              Clipboard.setData(ClipboardData(text: value.toString()));
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('$title copied to clipboard'),
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            },
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(ThemeData theme, IconData icon, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, top: 8.0),
      child: Row(
        children: [
          Icon(
            icon, 
            color: theme.colorScheme.onSurface, 
            size: 20
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}