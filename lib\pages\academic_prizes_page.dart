import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'academic_prize_detail_page.dart';
import 'login_page.dart';

class AcademicPrizesPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedAcademicPrizes;
  final bool isFromDetailPage;

  const AcademicPrizesPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedAcademicPrizes,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _AcademicPrizesPageState createState() => _AcademicPrizesPageState();
}

class _AcademicPrizesPageState extends State<AcademicPrizesPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('academic_prizes_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _academicPrizes = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("AcademicPrizesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant AcademicPrizesPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("AcademicPrizesPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("AcademicPrizesPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedAcademicPrizes != null && widget.preloadedAcademicPrizes!.isNotEmpty) {
      print("Preloaded academic prizes found, using them.");
      setState(() {
        _academicPrizes = List<Map<String, dynamic>>.from(widget.preloadedAcademicPrizes!);
        _academicPrizes.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedAcademicPrizes!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      // Don't load more from database if we have preloaded data
      // But check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded academic prizes or empty list, loading from database.");
      await _loadAcademicPrizesFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final academicPrizesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academicprizes';
    
    try {
      final response = await Supabase.instance.client
          .from(academicPrizesTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_academicPrizes.length, _academicPrizes.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadAcademicPrizesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadAcademicPrizesFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final academicPrizesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academicprizes';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _academicPrizes.length;
        endRange = _academicPrizes.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(academicPrizesTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _academicPrizes = List<Map<String, dynamic>>.from(response);
          } else {
            _academicPrizes.addAll(List<Map<String, dynamic>>.from(response));
          }
          _academicPrizes.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching academic prizes: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final academicPrizesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academicprizes';
    _realtimeChannel = Supabase.instance.client
        .channel('academic_prizes')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: academicPrizesTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newPrizeId = payload.newRecord['id'];
          final newPrizeResponse = await Supabase.instance.client
              .from(academicPrizesTableName)
              .select('*')
              .eq('id', newPrizeId)
              .single();
          if (mounted) {
            Map<String, dynamic> newPrize = Map.from(newPrizeResponse);
            setState(() {
              _academicPrizes.add(newPrize);
              _academicPrizes.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedPrizeId = payload.newRecord['id'];
          final updatedPrizeResponse = await Supabase.instance.client
              .from(academicPrizesTableName)
              .select('*')
              .eq('id', updatedPrizeId)
              .single();
          if (mounted) {
            final updatedPrize = Map<String, dynamic>.from(updatedPrizeResponse);
            setState(() {
              _academicPrizes = _academicPrizes.map((prize) {
                return prize['id'] == updatedPrize['id'] ? updatedPrize : prize;
              }).toList();
              _academicPrizes.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedPrizeId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _academicPrizes.removeWhere((prize) => prize['id'] == deletedPrizeId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("AcademicPrizesPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreAcademicPrizes();
    }
  }

  Future<void> _loadMoreAcademicPrizes() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more academic prizes...");
      await _loadAcademicPrizesFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> prize) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AcademicPrizeDetailPage(
            prize: prize,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("AcademicPrizesPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Academic Prizes',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadAcademicPrizesFromSupabase(initialLoad: true);
              },
              child: _academicPrizes.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No academic prizes available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _academicPrizes.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _academicPrizes.length) {
                          final prize = _academicPrizes[index];
                          return VisibilityDetector(
                            key: Key('academic_prize_${prize['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              // No image loading needed for academic prizes
                            },
                            child: _buildAcademicPrizeCard(prize, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAcademicPrizeCard(
    Map<String, dynamic> prize,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = prize['fullname'] ?? 'Unknown';
    final String about = prize['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, prize),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.emoji_events,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}