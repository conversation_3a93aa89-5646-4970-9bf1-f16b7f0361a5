import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AiCreditsWalletPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AiCreditsWalletPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<AiCreditsWalletPage> createState() => _AiCreditsWalletPageState();
}

class _AiCreditsWalletPageState extends State<AiCreditsWalletPage> {
  int _credits = 0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCredits();
  }

  Future<void> _loadCredits() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading credits from a database
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _credits = 0; // Default to 0 credits for new users
      _isLoading = false;
    });
  }

  Future<void> _buyCredits(int amount) async {
    setState(() {
      _isLoading = true;
    });

    // Simulate payment processing
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _credits += amount;
      _isLoading = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Successfully purchased $amount credits!'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    // Use theme colors for buttons for better consistency
    final buttonBackground = theme.colorScheme.onSurface;
    final buttonTextColor = theme.colorScheme.surface;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'AI Credits Wallet',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              widget.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SafeArea(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return SingleChildScrollView(
                    padding: EdgeInsets.all(size.width * 0.04), // Responsive padding
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: constraints.maxHeight - (size.width * 0.08),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Credits card
                          Card(
                            color: theme.colorScheme.surface,
                            elevation: 2,
                            child: Padding(
                              padding: const EdgeInsets.all(20),
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.account_balance_wallet,
                                    size: 48,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Your Credits',
                                    style: GoogleFonts.notoSans(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  FittedBox(
                                    child: Text(
                                      '$_credits',
                                      style: GoogleFonts.notoSans(
                                        fontSize: 36,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Credits are used for AI processing tasks',
                                    style: GoogleFonts.notoSans(
                                      fontSize: 14,
                                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Buy credits section
                          Card(
                            color: theme.colorScheme.surface,
                            elevation: 2,
                            child: Padding(
                              padding: const EdgeInsets.all(20),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Buy Credits',
                                    style: GoogleFonts.notoSans(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                  const SizedBox(height: 16),

                                  // Credit packages
                                  _buildCreditPackage(
                                    context,
                                    amount: 100,
                                    price: '4.99',
                                    isPopular: false,
                                    buttonBackground: buttonBackground,
                                    buttonTextColor: buttonTextColor,
                                  ),

                                  const SizedBox(height: 12),

                                  _buildCreditPackage(
                                    context,
                                    amount: 500,
                                    price: '19.99',
                                    isPopular: true,
                                    buttonBackground: buttonBackground,
                                    buttonTextColor: buttonTextColor,
                                  ),

                                  const SizedBox(height: 12),

                                  _buildCreditPackage(
                                    context,
                                    amount: 1000,
                                    price: '34.99',
                                    isPopular: false,
                                    buttonBackground: buttonBackground,
                                    buttonTextColor: buttonTextColor,
                                  ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Usage information
                          Card(
                            color: theme.colorScheme.surface,
                            elevation: 2,
                            child: Padding(
                              padding: const EdgeInsets.all(20),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Credit Usage',
                                    style: GoogleFonts.notoSans(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                  const SizedBox(height: 16),

                                  _buildUsageItem(
                                    'Generate Notes',
                                    '5 credits',
                                  ),
                                  _buildUsageItem(
                                    'Create Flashcards',
                                    '3 credits',
                                  ),
                                  _buildUsageItem(
                                    'Generate Quiz',
                                    '4 credits',
                                  ),
                                  _buildUsageItem(
                                    'Create Exam',
                                    '6 credits',
                                  ),
                                  _buildUsageItem(
                                    'Interactive Lesson',
                                    '8 credits',
                                  ),
                                  _buildUsageItem(
                                    'Scheme of Work',
                                    '10 credits',
                                  ),
                                ],
                              ),
                            ),
                          ),
                          
                          // Add bottom padding to ensure content isn't cut off
                          SizedBox(height: size.height * 0.02),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
    );
  }

  Widget _buildCreditPackage(
    BuildContext context, {
    required int amount,
    required String price,
    required bool isPopular,
    required Color buttonBackground,
    required Color buttonTextColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          // FIX: Removed conditional logic. All borders are now the same color and width.
          color: Theme.of(context).dividerColor,
          width: 1, 
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Check if we need to stack vertically on very small screens
            final isSmallScreen = constraints.maxWidth < 280;
            
            if (isSmallScreen) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPackageInfo(amount, price, isPopular),
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: _buildBuyButton(amount, buttonBackground, buttonTextColor),
                  ),
                ],
              );
            }
            
            return Row(
              children: [
                Expanded(
                  flex: 3,
                  child: _buildPackageInfo(amount, price, isPopular),
                ),
                const SizedBox(width: 12),
                Flexible(
                  flex: 1,
                  child: _buildBuyButton(amount, buttonBackground, buttonTextColor),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildPackageInfo(int amount, String price, bool isPopular) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          crossAxisAlignment: WrapCrossAlignment.center,
          spacing: 8.0, // Added spacing for better layout
          runSpacing: 4.0,
          children: [
            Text(
              '$amount Credits',
              style: GoogleFonts.notoSans(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            if (isPopular)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.onSurface,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'POPULAR',
                  style: GoogleFonts.notoSans(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.surface,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          '\$$price',
          style: GoogleFonts.notoSans(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildBuyButton(int amount, Color buttonBackground, Color buttonTextColor) {
    return ElevatedButton(
      onPressed: _isLoading ? null : () => _buyCredits(amount),
      style: ElevatedButton.styleFrom(
        backgroundColor: buttonBackground,
        foregroundColor: buttonTextColor,
        disabledBackgroundColor: buttonBackground.withOpacity(0.5),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        minimumSize: const Size(80, 44),
      ),
      child: FittedBox(
        child: Text(
          'Buy Now',
          style: GoogleFonts.notoSans(
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildUsageItem(String task, String credits) {
    final itemTextColor = Theme.of(context).colorScheme.onSurface;
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            child: Text(
              task,
              style: GoogleFonts.notoSans(
                fontSize: 14,
                color: itemTextColor,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            credits,
            style: GoogleFonts.notoSans(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: itemTextColor,
            ),
          ),
        ],
      ),
    );
  }
}