import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'signature_event_detail_page.dart';

class SignatureEventsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedSignatureEvents;
  final bool isFromDetailPage;

  const SignatureEventsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedSignatureEvents,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<SignatureEventsPage> createState() => _SignatureEventsPageState();
}

class _SignatureEventsPageState extends State<SignatureEventsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('signature_events_list');
  List<Map<String, dynamic>> _signatureEvents = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("SignatureEventsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant SignatureEventsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("SignatureEventsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("SignatureEventsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedSignatureEvents != null && widget.preloadedSignatureEvents!.isNotEmpty) {
      print("Preloaded signature events found, using them.");
      setState(() {
        _signatureEvents = List<Map<String, dynamic>>.from(widget.preloadedSignatureEvents!);
        _signatureEvents.forEach((event) {
          event['_isImageLoading'] = false;
        });
        _signatureEvents.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded signature events or empty list, loading from database.");
      await _loadSignatureEventsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final signatureEventsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_signatureevents';
    
    try {
      final response = await Supabase.instance.client
          .from(signatureEventsTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_signatureEvents.length, _signatureEvents.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadSignatureEventsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadSignatureEventsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final signatureEventsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_signatureevents';

    try {
      int startRange = initialLoad ? 0 : _signatureEvents.length;
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(signatureEventsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedEvents =
          await _updateEventImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _signatureEvents = updatedEvents;
          } else {
            _signatureEvents.addAll(updatedEvents);
          }
          _signatureEvents.forEach((event) {
            event['_isImageLoading'] = false;
          });
          _signatureEvents.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
      _cacheSignatureEvents(_signatureEvents);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching signature events: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateEventImageUrls(
      List<Map<String, dynamic>> events) async {
    List<Future<void>> futures = [];
    for (final event in events) {
      if (event['image_url'] == null ||
          event['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(event));
      }
    }
    await Future.wait(futures);
    return events;
  }

  void _setupRealtime() {
    final signatureEventsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_signatureevents';
    _realtimeChannel = Supabase.instance.client
        .channel('signature_events')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: signatureEventsTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newEventId = payload.newRecord['id'];
          final newEventResponse = await Supabase.instance.client
              .from(signatureEventsTableName)
              .select('*')
              .eq('id', newEventId)
              .single();
          if (mounted) {
            Map<String, dynamic> newEvent = Map.from(newEventResponse);
            final updatedEvent = await _updateEventImageUrls([newEvent]);
            setState(() {
              _signatureEvents.add(updatedEvent.first);
              updatedEvent.first['_isImageLoading'] = false;
              _signatureEvents.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedEventId = payload.newRecord['id'];
          final updatedEventResponse = await Supabase.instance.client
              .from(signatureEventsTableName)
              .select('*')
              .eq('id', updatedEventId)
              .single();
          if (mounted) {
            final updatedEvent = Map<String, dynamic>.from(updatedEventResponse);
            setState(() {
              _signatureEvents = _signatureEvents.map((event) {
                return event['id'] == updatedEvent['id'] ? updatedEvent : event;
              }).toList();
              _signatureEvents.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedEventId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _signatureEvents.removeWhere((event) => event['id'] == deletedEventId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("SignatureEventsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreEvents();
    }
  }

  Future<void> _loadMoreEvents() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more events...");
      await _loadSignatureEventsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _cacheSignatureEvents(List<Map<String, dynamic>> signatureEvents) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String signatureEventsJson = jsonEncode(signatureEvents);
      await prefs.setString(
          'signatureevents_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          signatureEventsJson);
    } catch (e) {
      print('Error caching signature events: $e');
    }
  }

  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> event) async {
    if (event['_isImageLoading'] == true) return;
    if (event['image_url'] != null && event['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => event['_isImageLoading'] = true);

    final fullname = event['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeEventBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/signatureevents';

    try {
      final file = await Supabase.instance.client.storage.from(collegeEventBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeEventBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        event['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        event['_isImageLoading'] = false;
      });
    } else {
      event['_isImageLoading'] = false;
    }
  }

  void _navigateToDetail(BuildContext context, Map<String, dynamic> event) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SignatureEventDetailPage(
            event: event,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("SignatureEventsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Signature Events',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadSignatureEventsFromSupabase(initialLoad: true);
              },
              child: _signatureEvents.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No signature events available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _signatureEvents.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _signatureEvents.length) {
                          final event = _signatureEvents[index];
                          return VisibilityDetector(
                            key: Key('event_${event['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (event['image_url'] == null ||
                                      event['image_url'] == 'assets/placeholder_image.png') &&
                                  !event['_isImageLoading']) {
                                _fetchImageUrl(event);
                              }
                            },
                            child: _buildEventCard(event, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEventCard(
    Map<String, dynamic> event,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = event['fullname'] ?? 'Unknown';
    final String about = event['about'] ?? '';
    final String imageUrl = event['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, event),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.event,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.event,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.event,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}