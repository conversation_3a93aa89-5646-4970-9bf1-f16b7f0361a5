import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'school_detail_page.dart';
import 'login_page.dart';

class SchoolsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedSchools;
  final bool isFromDetailPage;

  const SchoolsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedSchools,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _SchoolsPageState createState() => _SchoolsPageState();
}

class _SchoolsPageState extends State<SchoolsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('schools_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _schools = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("SchoolsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant SchoolsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("SchoolsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("SchoolsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedSchools != null && widget.preloadedSchools!.isNotEmpty) {
      print("Preloaded schools found, using them.");
      setState(() {
        _schools = List<Map<String, dynamic>>.from(widget.preloadedSchools!);
        _schools.forEach((school) {
          school['_isImageLoading'] = false;
        });
        _schools.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedSchools!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      // Don't load more from database if we have preloaded data
      // But check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded schools or empty list, loading from database.");
      await _loadSchoolsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final schoolsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_schools';
    
    try {
      final response = await Supabase.instance.client
          .from(schoolsTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_schools.length, _schools.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadSchoolsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadSchoolsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final schoolsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_schools';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _schools.length;
        endRange = _schools.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(schoolsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedSchools =
          await _updateSchoolImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _schools = updatedSchools;
          } else {
            _schools.addAll(updatedSchools);
          }
          _schools.forEach((school) {
            school['_isImageLoading'] = false;
          });
          _schools.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching schools: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateSchoolImageUrls(
      List<Map<String, dynamic>> schools) async {
    List<Future<void>> futures = [];
    for (final school in schools) {
      if (school['image_url'] == null ||
          school['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(school));
      }
    }
    await Future.wait(futures);
    return schools;
  }

  void _setupRealtime() {
    final schoolsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_schools';
    _realtimeChannel = Supabase.instance.client
        .channel('schools')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: schoolsTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newSchoolId = payload.newRecord['id'];
          final newSchoolResponse = await Supabase.instance.client
              .from(schoolsTableName)
              .select('*')
              .eq('id', newSchoolId)
              .single();
          if (mounted) {
            Map<String, dynamic> newSchool = Map.from(newSchoolResponse);
            final updatedSchool = await _updateSchoolImageUrls([newSchool]);
            setState(() {
              _schools.add(updatedSchool.first);
              updatedSchool.first['_isImageLoading'] = false;
              _schools.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedSchoolId = payload.newRecord['id'];
          final updatedSchoolResponse = await Supabase.instance.client
              .from(schoolsTableName)
              .select('*')
              .eq('id', updatedSchoolId)
              .single();
          if (mounted) {
            final updatedSchool = Map<String, dynamic>.from(updatedSchoolResponse);
            setState(() {
              _schools = _schools.map((school) {
                return school['id'] == updatedSchool['id'] ? updatedSchool : school;
              }).toList();
              _schools.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedSchoolId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _schools.removeWhere((school) => school['id'] == deletedSchoolId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("SchoolsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreSchools();
    }
  }

  Future<void> _loadMoreSchools() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more schools...");
      await _loadSchoolsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> school) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SchoolDetailPage(
            school: school,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("SchoolsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Schools',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadSchoolsFromSupabase(initialLoad: true);
              },
              child: _schools.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No schools available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _schools.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _schools.length) {
                          final school = _schools[index];
                          return VisibilityDetector(
                            key: Key('school_${school['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (school['image_url'] == null ||
                                      school['image_url'] == 'assets/placeholder_image.png') &&
                                  !school['_isImageLoading']) {
                                _fetchImageUrl(school);
                              }
                            },
                            child: _buildSchoolCard(school, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> school) async {
    if (school['_isImageLoading'] == true) {
      print('Image loading already in progress for ${school['fullname']}, skipping.');
      return;
    }
    if (school['image_url'] != null &&
        school['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${school['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      school['_isImageLoading'] = true;
    });

    final fullname = school['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeSchoolBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/schools';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeSchoolBucket');
    print('Image URL before fetch: ${school['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeSchoolBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeSchoolBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        school['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        school['_isImageLoading'] = false;
        print('Setting image_url for ${school['fullname']} to: ${school['image_url']}');
      });
    } else {
      school['_isImageLoading'] = false;
    }
  }

  Widget _buildSchoolCard(
    Map<String, dynamic> school,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = school['fullname'] ?? 'Unknown';
    final String building = school['building'] ?? '';
    final String room = school['room'] ?? '';
    final String about = school['about'] ?? '';
    final String imageUrl = school['image_url'] ?? '';

    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, school),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.school,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.school,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.school,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}