import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart'; // Import shared_preferences
import 'dart:convert';

// Import for Map
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import 'login_page.dart';
import 'helpdesk_services_list_page.dart';
import 'helpdesk_services_detail_page.dart';
import 'helpdesk_people_list_page.dart';
import 'helpdesk_people_detail_page.dart';
import 'helpdesk_links_list_page.dart';

class HelpdeskDetailPage extends StatefulWidget {
  final Map<String, dynamic> helpdesk;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const HelpdeskDetailPage({
    Key? key,
    required this.helpdesk,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<HelpdeskDetailPage> createState() => _HelpdeskDetailPageState();
}

class _HelpdeskDetailPageState extends State<HelpdeskDetailPage> with TickerProviderStateMixin {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _helpdeskRealtimeChannel; // Realtime channel for helpdesk updates
  
  // Tab controller
  late TabController _tabController;
  final List<Widget> _tabs = [];
  bool _isLoadingTabs = true;

  // Data lists for services, people, and links
  List<Map<String, dynamic>> _services = [];
  List<Map<String, dynamic>> _people = [];
  List<Map<String, dynamic>> _links = [];

  // Loading states
  bool _isLoadingServices = false;
  bool _isLoadingPeople = false;
  bool _isLoadingLinks = false;

  // Tab availability flags
  bool _hasServices = false;
  bool _hasPeople = false;
  bool _hasLinks = false;
  bool _hasLocation = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 1, vsync: this);
    _initializePage();
  }

  Future<void> _initializePage() async {
    _loadImageFromPreloadedData();
    _setupHelpdeskRealtimeListener();

    // Load data from database
    await Future.wait([
      _loadServicesData(),
      _loadPeopleData(),
      _loadLinksData(),
    ]);

    _checkLocationData();
    _setupTabs();

    if (mounted) {
      setState(() {
        _isLoadingTabs = false;
      });
    }
  }

  void _checkLocationData() {
    final latitude = double.tryParse(widget.helpdesk['latitude']?.toString() ?? '');
    final longitude = double.tryParse(widget.helpdesk['longitude']?.toString() ?? '');
    _hasLocation = latitude != null && longitude != null;
  }

  void _setupTabs() {
    if (mounted) {
      _tabController.dispose();
    }

    _tabs.clear();
    
    // Overview tab (always present)
    _tabs.add(const Tab(icon: Icon(Icons.visibility)));

    // People tab (comes after overview)
    if (_hasPeople) {
      _tabs.add(const Tab(icon: Icon(Icons.person)));
    }

    // Services tab
    if (_hasServices) {
      _tabs.add(const Tab(icon: Icon(Icons.grain)));
    }

    // Links tab
    if (_hasLinks) {
      _tabs.add(const Tab(icon: Icon(Icons.link)));
    }

    // Location tab
    if (_hasLocation) {
      _tabs.add(const Tab(icon: Icon(Icons.map_outlined)));
    }

    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _helpdeskRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    _tabController.dispose();
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.helpdesk['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupHelpdeskRealtimeListener() {
    final helpdesksTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_helpdesks';
    _helpdeskRealtimeChannel = Supabase.instance.client
        .channel('helpdesk_detail_channel')
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: helpdesksTableName,
          callback: (payload) async {
            if (payload.newRecord['id'] == widget.helpdesk['id']) {
              print("Realtime UPDATE event received for THIS helpdesk: ${widget.helpdesk['fullname']}");
              _fetchUpdatedHelpdeskData();
            } else {
              print("Realtime UPDATE event received for OTHER helpdesk, ignoring.");
            }
          },
        )
        .subscribe();
  }

  Future<void> _fetchUpdatedHelpdeskData() async {
    final helpdesksTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_helpdesks';
    try {
      final updatedHelpdeskResponse = await Supabase.instance.client
          .from(helpdesksTableName)
          .select('*')
          .eq('id', widget.helpdesk['id'])
          .single();

      if (mounted && updatedHelpdeskResponse != null) {
        Map<String, dynamic> updatedHelpdesk = Map.from(updatedHelpdeskResponse);
        setState(() {
          widget.helpdesk.clear();
          widget.helpdesk.addAll(updatedHelpdesk);
          _loadImageFromPreloadedData();
          print("Helpdesk data updated in detail page for ${widget.helpdesk['fullname']}");
          _updateHelpdesksCache(updatedHelpdesk);
        });
      }
    } catch (error) {
      print("Error fetching updated helpdesk data: $error");
    }
  }

  Future<void> _updateHelpdesksCache(Map<String, dynamic> updatedHelpdesk) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'helpdesks_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedHelpdesksJson = prefs.getString(cacheKey);

    if (cachedHelpdesksJson != null) {
      List<Map<String, dynamic>> cachedHelpdesks = (jsonDecode(cachedHelpdesksJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      for (int i = 0; i < cachedHelpdesks.length; i++) {
        if (cachedHelpdesks[i]['id'] == updatedHelpdesk['id']) {
          cachedHelpdesks[i] = updatedHelpdesk;
          break;
        }
      }

      await prefs.setString(cacheKey, jsonEncode(cachedHelpdesks));
      print("Helpdesks cache updated with realtime change for ${updatedHelpdesk['fullname']}");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchNavigation(double latitude, double longitude) async {
    final Uri url = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch navigation.')),
      );
    }
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    final cleanNumber = whatsappNumber.replaceAll(RegExp(r'[+\s]'), '');
    final Uri url = Uri.parse('https://wa.me/$cleanNumber');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch WhatsApp.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email.')),
      );
    }
  }

  void _launchLink(String url) async {
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Link is empty.')),
      );
      return;
    }
    Uri? uri = Uri.tryParse(url);
    if (uri == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Invalid link format.')),
      );
      return;
    }
    if (!uri.hasScheme) {
      uri = Uri.parse('https://$url');
    }

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not launch link: ${uri.toString()}')),
        );
      }
    }
  }

  void _navigateToServicesPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HelpdeskServicesListPage(
          helpdeskName: widget.helpdesk['fullname'] ?? '',
          services: _services,
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
          collegeNameForTable: widget.collegeNameForBucket,
        ),
      ),
    );
  }

  void _navigateToPeoplePage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HelpdeskPeopleListPage(
          helpdeskName: widget.helpdesk['fullname'] ?? '',
          people: _people,
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
          collegeNameForTable: widget.collegeNameForBucket,
        ),
      ),
    );
  }

  void _navigateToLinksPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HelpdeskLinksListPage(
          helpdeskName: widget.helpdesk['fullname'] ?? '',
          links: _links,
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
          collegeNameForTable: widget.collegeNameForBucket,
        ),
      ),
    );
  }

  void _navigateToServiceDetailPage(Map<String, dynamic> service) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HelpdeskServicesDetailPage(
          serviceDetailName: service['fullname'] ?? '',
          detailPageName: service['id']?.toString() ?? '',
          helpdeskName: widget.helpdesk['fullname'] ?? '',
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
          service: service,
        ),
      ),
    );
  }

  void _navigateToPeopleDetailPage(Map<String, dynamic> person) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HelpdeskPeopleDetailPage(
          personName: person['fullname'] ?? '',
          personTitle: person['title'] ?? '',
          detailPageName: person['id']?.toString() ?? '',
          helpdeskName: widget.helpdesk['fullname'] ?? '',
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
          person: person,
        ),
      ),
    );
  }

  Future<void> _loadServicesData() async {
    if (_isLoadingServices) return;

    setState(() {
      _isLoadingServices = true;
    });

    final servicesTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_services';
    final helpdeskName = widget.helpdesk['fullname'] ?? '';

    try {
      final response = await Supabase.instance.client
          .from(servicesTableName)
          .select('*')
          .eq('department', helpdeskName)
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _services = List<Map<String, dynamic>>.from(response);
          _hasServices = _services.isNotEmpty;
          _isLoadingServices = false;
        });
      }
    } catch (error) {
      print('Error loading services: $error');
      if (mounted) {
        setState(() {
          _hasServices = false;
          _isLoadingServices = false;
        });
      }
    }
  }

  Future<void> _loadPeopleData() async {
    if (_isLoadingPeople) return;

    setState(() {
      _isLoadingPeople = true;
    });

    final peopleTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_people';
    final helpdeskName = widget.helpdesk['fullname'] ?? '';

    try {
      final response = await Supabase.instance.client
          .from(peopleTableName)
          .select('*')
          .eq('department', helpdeskName)
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _people = List<Map<String, dynamic>>.from(response);
          _hasPeople = _people.isNotEmpty;
          _isLoadingPeople = false;
        });
      }
    } catch (error) {
      print('Error loading people: $error');
      if (mounted) {
        setState(() {
          _hasPeople = false;
          _isLoadingPeople = false;
        });
      }
    }
  }

  Future<void> _loadLinksData() async {
    if (_isLoadingLinks) return;

    setState(() {
      _isLoadingLinks = true;
    });

    final linksTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_links';
    final helpdeskName = widget.helpdesk['fullname'] ?? '';

    try {
      final response = await Supabase.instance.client
          .from(linksTableName)
          .select('*')
          .eq('department', helpdeskName)
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _links = List<Map<String, dynamic>>.from(response);
          _hasLinks = _links.isNotEmpty;
          _isLoadingLinks = false;
        });
      }
    } catch (error) {
      print('Error loading links: $error');
      if (mounted) {
        setState(() {
          _hasLinks = false;
          _isLoadingLinks = false;
        });
      }
    }
  }

  List<Widget> _getTabViews() {
    final views = <Widget>[];
    
    // Overview tab (always present)
    views.add(_buildOverviewTab());

    // People tab (comes after overview)
    if (_hasPeople) {
      views.add(_buildPeopleTab());
    }

    // Services tab
    if (_hasServices) {
      views.add(_buildServicesTab());
    }

    // Links tab
    if (_hasLinks) {
      views.add(_buildLinksTab());
    }

    // Location tab
    if (_hasLocation) {
      views.add(_buildLocationTab());
    }

    return views;
  }

Widget _buildOverviewTab() {
  final theme = Theme.of(context);
  final bool currentIsDarkMode = widget.isDarkMode;
  final phone = widget.helpdesk['phone'] as String? ?? '';
  final whatsappNumber = widget.helpdesk['whatsapp'] as String? ?? '';
  final building = widget.helpdesk['building'] as String? ?? '';
  final room = widget.helpdesk['room'] as String? ?? '';

  final locationText = (building.isNotEmpty && room.isNotEmpty)
      ? '$building $room'
      : (building.isNotEmpty ? building : (room.isNotEmpty ? room : null));

  return SingleChildScrollView(
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Hero Image
        _isLoadingImage
            ? Container(
                height: 200,
                color: theme.colorScheme.surfaceVariant,
                child: const Center(child: CircularProgressIndicator()),
              )
            : (_imageUrl.isNotEmpty && _imageUrl != 'assets/placeholder_image.png')
                ? CachedNetworkImage(
                    imageUrl: _imageUrl,
                    height: 200,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      height: 200,
                      color: theme.colorScheme.surfaceVariant,
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                    errorWidget: (context, url, error) => Container(
                      height: 200,
                      color: theme.colorScheme.surfaceVariant,
                      child: Center(
                        child: Icon(
                          Icons.help_center,
                          size: 50,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                  )
                : Container(
                    height: 200,
                    color: theme.colorScheme.surfaceVariant,
                    child: Center(
                      child: Icon(
                        Icons.help_center,
                        size: 50,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
        
        // Content Card - using padding to match other tabs
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            margin: EdgeInsets.zero,
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: currentIsDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        backgroundImage: (_imageUrl.isNotEmpty && _imageUrl != 'assets/placeholder_image.png')
                            ? NetworkImage(_imageUrl)
                            : null,
                        child: (_imageUrl.isEmpty || _imageUrl == 'assets/placeholder_image.png')
                            ? Icon(
                                Icons.help_center,
                                size: 30,
                                color: theme.colorScheme.onSurface,
                              )
                            : null,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.helpdesk['fullname'] ?? 'Unknown',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            if (locationText != null)
                              Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Text(
                                  locationText,
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (widget.helpdesk['about'] != null && (widget.helpdesk['about'] as String).isNotEmpty) ...[
                    _buildSectionTitle(theme, Icons.info_outline, 'About'),
                    const SizedBox(height: 8),
                    Text(
                      widget.helpdesk['about'] as String,
                      style: TextStyle(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                  if (widget.helpdesk['hours'] != null && (widget.helpdesk['hours'] as String).isNotEmpty)
                    _buildDetailRow(theme, Icons.access_time, 'Hours', widget.helpdesk['hours'], canCopy: false),
                  if (widget.helpdesk['specialhours'] != null && (widget.helpdesk['specialhours'] as String).isNotEmpty)
                    _buildDetailRow(theme, Icons.timer, 'Special Hours', widget.helpdesk['specialhours'], canCopy: false),
                  if (widget.helpdesk['payment'] != null && (widget.helpdesk['payment'] as String).isNotEmpty)
                    _buildDetailRow(theme, Icons.payment, 'Payment Methods', widget.helpdesk['payment'], canCopy: false),
                  if (phone.isNotEmpty) _buildDetailRow(theme, Icons.phone, 'Phone', phone, canCopy: true),
                  if (widget.helpdesk['email'] != null && (widget.helpdesk['email'] as String).isNotEmpty)
                    _buildDetailRow(theme, Icons.email, 'Email', widget.helpdesk['email'], canCopy: true),
                  if (widget.helpdesk['fax'] != null && (widget.helpdesk['fax'] as String).isNotEmpty)
                    _buildDetailRow(theme, Icons.fax, 'Fax', widget.helpdesk['fax'], canCopy: true),
                  if (whatsappNumber.isNotEmpty)
                    _buildDetailRow(theme, FontAwesomeIcons.whatsapp, 'WhatsApp', whatsappNumber, canCopy: true),
                ],
              ),
            ),
          ),
        ),
      ],
    ),
  );
}

  Widget _buildServicesTab() {
    final theme = Theme.of(context);
    
    if (_isLoadingServices) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_services.isEmpty) {
      return Center(
        child: Text(
          'No services found.',
          style: TextStyle(color: theme.colorScheme.onSurface),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _services.length,
      itemBuilder: (context, index) {
        final service = _services[index];
        return Card(
          color: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          margin: const EdgeInsets.only(bottom: 8.0),
          child: ListTile(
            leading: Icon(Icons.grain, color: theme.colorScheme.onSurface),
            title: Text(
              service['fullname'] ?? 'Service Name',
              style: TextStyle(color: theme.colorScheme.onSurface),
            ),
            trailing: Icon(Icons.chevron_right, color: theme.colorScheme.onSurface),
            onTap: () => _navigateToServiceDetailPage(service),
          ),
        );
      },
    );
  }

  Widget _buildPeopleTab() {
    final theme = Theme.of(context);
    
    if (_isLoadingPeople) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_people.isEmpty) {
      return Center(
        child: Text(
          'No people found.',
          style: TextStyle(color: theme.colorScheme.onSurface),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _people.length,
      itemBuilder: (context, index) {
        final person = _people[index];
        return Card(
          color: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          margin: const EdgeInsets.only(bottom: 8.0),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: widget.isDarkMode
                  ? Colors.white.withOpacity(0.1)
                  : Colors.black.withOpacity(0.1),
              child: Icon(
                Icons.person,
                color: theme.colorScheme.onSurface,
              ),
            ),
            title: Text(
              person['fullname'] ?? 'Person Name',
              style: TextStyle(color: theme.colorScheme.onSurface),
            ),
            subtitle: person['title'] != null && (person['title'] as String).isNotEmpty
                ? Text(
                    person['title'] as String,
                    style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                  )
                : null,
            trailing: Icon(Icons.chevron_right, color: theme.colorScheme.onSurface),
            onTap: () => _navigateToPeopleDetailPage(person),
          ),
        );
      },
    );
  }

  Widget _buildLinksTab() {
    final theme = Theme.of(context);
    
    if (_isLoadingLinks) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_links.isEmpty) {
      return Center(
        child: Text(
          'No links found.',
          style: TextStyle(color: theme.colorScheme.onSurface),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _links.length,
      itemBuilder: (context, index) {
        final link = _links[index];
        return Card(
          color: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          margin: const EdgeInsets.only(bottom: 8.0),
          child: ListTile(
            leading: Icon(Icons.link, color: theme.colorScheme.onSurface),
            title: Text(
              link['fullname'] ?? 'Link Name',
              style: TextStyle(
                color: theme.colorScheme.onSurface,
              ),
            ),
            trailing: Icon(Icons.open_in_new, color: theme.colorScheme.onSurface),
            onTap: () => _launchLink(link['link'] ?? ''),
          ),
        );
      },
    );
  }

  Widget _buildLocationTab() {
    final theme = Theme.of(context);
    final latitude = double.tryParse(widget.helpdesk['latitude']?.toString() ?? '');
    final longitude = double.tryParse(widget.helpdesk['longitude']?.toString() ?? '');

    if (latitude == null || longitude == null) {
      return Center(
        child: Text(
          'Location information not available.',
          style: TextStyle(color: theme.colorScheme.onSurface),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: FlutterMap(
          options: MapOptions(
            initialCenter: LatLng(latitude, longitude),
            initialZoom: 16.0,
          ),
          children: [
            TileLayer(
              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
              userAgentPackageName: 'com.example.yourapp',
            ),
            MarkerLayer(
              markers: [
                Marker(
                  point: LatLng(latitude, longitude),
                  width: 40,
                  height: 40,
                  child: Icon(
                    Icons.location_on,
                    color: Colors.red,
                    size: 40,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value, {bool canCopy = false, VoidCallback? onTap}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    final bool isClickable = onTap != null;

    Widget content = Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: theme.colorScheme.onSurface,
          size: 20,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      value.toString(),
                      style: TextStyle(
                        color: isClickable ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                        decoration: isClickable ? TextDecoration.underline : TextDecoration.none,
                      ),
                    ),
                  ),
                  if (canCopy)
                    SizedBox(
                      width: 30,
                      height: 30,
                      child: IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy $title',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ],
    );

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: isClickable
          ? InkWell(
              onTap: onTap,
              child: content,
            )
          : content,
    );
  }

  Widget _buildSectionTitle(ThemeData theme, IconData icon, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, color: theme.colorScheme.onSurface, size: 20),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItem(ThemeData theme, String title, VoidCallback onTap, {bool isLink = false}) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            Icon(
              isLink ? Icons.open_in_new : Icons.chevron_right,
              size: 18,
              color: theme.colorScheme.onSurface,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: theme.colorScheme.onSurface,
                  decoration: isLink ? TextDecoration.underline : TextDecoration.none,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeopleListItem(ThemeData theme, String name, String title, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: widget.isDarkMode
                  ? Colors.white.withOpacity(0.1)
                  : Colors.black.withOpacity(0.1),
              child: Icon(
                Icons.person,
                size: 20,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: TextStyle(color: theme.colorScheme.onSurface, fontWeight: FontWeight.bold),
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (title.isNotEmpty)
                    Text(
                      title,
                      style: TextStyle(color: theme.colorScheme.onSurfaceVariant, fontSize: 13),
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: theme.colorScheme.onSurface,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSeeMoreButton(ThemeData theme, String label, VoidCallback onPressed) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.only(top: 4.0, left: 26.0),
        child: TextButton(
          onPressed: onPressed,
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: Size(50, 30),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            alignment: Alignment.centerLeft,
          ),
          child: Text(
            label,
            style: TextStyle(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final phone = widget.helpdesk['phone'] as String? ?? '';
    final whatsappNumber = widget.helpdesk['whatsapp'] as String? ?? '';
    final latitude = double.tryParse(widget.helpdesk['latitude']?.toString() ?? '');
    final longitude = double.tryParse(widget.helpdesk['longitude']?.toString() ?? '');

    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isWhatsappAvailable = whatsappNumber.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.helpdesk['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoadingTabs
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Container(
                  color: theme.colorScheme.surface,
                  child: TabBar(
                    controller: _tabController,
                    isScrollable: false,
                    labelColor: theme.colorScheme.onSurface,
                    unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.7),
                    indicatorColor: theme.colorScheme.onSurface,
                    indicatorWeight: 3.0,
                    dividerColor: Colors.transparent,
                    tabs: _tabs,
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: _getTabViews(),
                  ),
                ),
              ],
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: Icon(
                  Icons.call,
                  color: isPhoneAvailable
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withOpacity(0.5),
                ),
                onPressed: isPhoneAvailable ? () => _launchDialer(phone) : null,
                tooltip: isPhoneAvailable ? 'Call $phone' : 'Phone not available',
              ),
              IconButton(
                icon: Icon(
                  Icons.navigation,
                  color: isNavigationAvailable
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withOpacity(0.5),
                ),
                onPressed: isNavigationAvailable ? () => _launchNavigation(latitude!, longitude!) : null,
                tooltip: isNavigationAvailable ? 'Navigate' : 'Navigation not available',
              ),
              IconButton(
                icon: FaIcon(
                  FontAwesomeIcons.whatsapp,
                  color: isWhatsappAvailable
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withOpacity(0.5),
                ),
                onPressed: isWhatsappAvailable ? () => _launchWhatsapp(whatsappNumber) : null,
                tooltip: isWhatsappAvailable ? 'WhatsApp $whatsappNumber' : 'WhatsApp not available',
              ),
            ],
          ),
        ),
      ),
    );
  }
}