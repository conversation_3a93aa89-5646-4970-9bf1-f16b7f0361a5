import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'login_page.dart';
import 'class_detail_page.dart';

// Helper class for a unified calendar item, mirroring TertiaryDetailPage
class _UnifiedCalendarItem {
  final String type; // 'class' in this context
  final String title;
  final String timeString;
  final String location;
  final Map<String, dynamic> sourceData;

  _UnifiedCalendarItem({
    required this.type,
    required this.title,
    required this.timeString,
    required this.location,
    required this.sourceData,
  });
}

class ClassSchedulePage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ClassSchedulePage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<ClassSchedulePage> createState() => _ClassSchedulePageState();
}

class _ClassSchedulePageState extends State<ClassSchedulePage> {
  late CalendarFormat _calendarFormat;
  late DateTime _focusedDay;
  late DateTime _selectedDay;
  late Map<DateTime, List<_UnifiedCalendarItem>> _events;
  late List<_UnifiedCalendarItem> _selectedEvents;
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _calendarFormat = CalendarFormat.week;
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
    _events = {};
    _selectedEvents = [];
    _fetchAndProcessSchedules();
  }

  Future<void> _fetchAndProcessSchedules() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_classschedules';
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('fullname', ascending: true);

      final classSchedules = List<Map<String, dynamic>>.from(response);
      final Map<DateTime, List<_UnifiedCalendarItem>> calendarEvents = {};
      _processItems(calendarEvents, classSchedules, 'class');

      if (mounted) {
        setState(() {
          _events = calendarEvents;
          _selectedEvents = _getEventsForDay(_selectedDay);
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error loading class schedules: $e';
        });
      }
      print('Error fetching class schedules: $e');
    }
  }

  void _processItems(Map<DateTime, List<_UnifiedCalendarItem>> dataMap, List<Map<String, dynamic>> items, String type) {
    for (var item in items) {
      try {
        final startDay = int.tryParse(item['startday']?.toString() ?? '');
        final startMonth = int.tryParse(item['startmonth']?.toString() ?? '');
        final startYear = int.tryParse(item['startyear']?.toString() ?? '');

        if (startDay == null || startMonth == null || startYear == null) continue;
        final startDate = DateTime(startYear, startMonth, startDay);

        final endDay = int.tryParse(item['endday']?.toString() ?? '');
        final endMonth = int.tryParse(item['endmonth']?.toString() ?? '');
        final endYear = int.tryParse(item['endyear']?.toString() ?? '');

        final endDate = (endDay != null && endMonth != null && endYear != null)
            ? DateTime(endYear, endMonth, endDay)
            : startDate;

        DateTime currentDate = startDate;
        while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
          bool shouldAdd = false;
          // Check if weekday flags are present. If not, add the event regardless of the day of the week.
          if (item['_mon'] == null && item['_tue'] == null && item['_wed'] == null && item['_thur'] == null && item['_fri'] == null && item['_sat'] == null && item['_sun'] == null) {
              shouldAdd = true;
          } else {
            switch (currentDate.weekday) {
              case DateTime.monday: shouldAdd = item['_mon'] == true; break;
              case DateTime.tuesday: shouldAdd = item['_tue'] == true; break;
              case DateTime.wednesday: shouldAdd = item['_wed'] == true; break;
              case DateTime.thursday: shouldAdd = item['_thur'] == true; break;
              case DateTime.friday: shouldAdd = item['_fri'] == true; break;
              case DateTime.saturday: shouldAdd = item['_sat'] == true; break;
              case DateTime.sunday: shouldAdd = item['_sun'] == true; break;
            }
          }

          if (shouldAdd) {
            final dayKey = DateTime.utc(currentDate.year, currentDate.month, currentDate.day);
            if (dataMap[dayKey] == null) dataMap[dayKey] = [];
            dataMap[dayKey]!.add(_createUnifiedItem(item, type));
          }
          currentDate = currentDate.add(const Duration(days: 1));
        }
      } catch (e) {
        print("Could not process item of type '$type': ${item['fullname']}. Error: $e");
      }
    }
  }

  _UnifiedCalendarItem _createUnifiedItem(Map<String, dynamic> item, String type) {
    String startTime = item['starttime'] ?? '';
    String endTime = item['endtime'] ?? '';
    String timeText = '';
    if (startTime.isNotEmpty && endTime.isNotEmpty) {
      timeText = '$startTime - $endTime';
    } else if (startTime.isNotEmpty) {
      timeText = 'Starts at $startTime';
    } else if (endTime.isNotEmpty) {
      timeText = 'Ends at $endTime';
    }

    String building = item['building'] ?? '';
    String room = item['room'] ?? '';
    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return _UnifiedCalendarItem(
        type: type,
        title: item['fullname'] ?? 'Untitled Class',
        timeString: timeText,
        location: locationText,
        sourceData: item,
    );
  }

  List<_UnifiedCalendarItem> _getEventsForDay(DateTime day) {
    final normalizedDay = DateTime.utc(day.year, day.month, day.day);
    final items = _events[normalizedDay] ?? [];
    items.sort((a, b) {
      final startTimeA = a.sourceData['starttime'] as String? ?? '23:59';
      final startTimeB = b.sourceData['starttime'] as String? ?? '23:59';
      return startTimeA.compareTo(startTimeB);
    });
    return items;
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (!mounted) return;
    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
      _selectedEvents = _getEventsForDay(selectedDay);
    });
  }

  Widget _buildClassCard(
    _UnifiedCalendarItem item,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String instructor = item.sourceData['instructor'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ClassDetailPage(
                classData: item.sourceData,
                institutionName: widget.institutionName,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.school,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (instructor.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          instructor,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (item.location.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          item.location,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (item.timeString.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          item.timeString,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Class Schedules',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline, size: 48, color: Colors.red),
                        const SizedBox(height: 16),
                        Text(
                          _errorMessage,
                          style: TextStyle(color: theme.colorScheme.error),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _fetchAndProcessSchedules,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                )
              : Column(
                  children: [
                    Container(
                      margin: const EdgeInsets.all(16.0),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surface,
                        borderRadius: BorderRadius.circular(12.0),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TableCalendar(
                        firstDay: DateTime.utc(2020, 1, 1),
                        lastDay: DateTime.utc(2030, 12, 31),
                        focusedDay: _focusedDay,
                        calendarFormat: _calendarFormat,
                        eventLoader: _getEventsForDay,
                        selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
                        onDaySelected: _onDaySelected,
                        onFormatChanged: (format) {
                          if (_calendarFormat != format) {
                            setState(() => _calendarFormat = format);
                          }
                        },
                        onPageChanged: (focusedDay) {
                          _focusedDay = focusedDay;
                        },
                        calendarStyle: CalendarStyle(
                          outsideDaysVisible: false,
                          markersMaxCount: 1,
                          markerDecoration: BoxDecoration(
                            color: currentIsDarkMode ? Colors.white : Colors.black,
                            shape: BoxShape.circle,
                          ),
                          todayDecoration: BoxDecoration(
                            color: (currentIsDarkMode ? Colors.white : Colors.black).withOpacity(0.3),
                            shape: BoxShape.circle,
                          ),
                          selectedDecoration: BoxDecoration(
                            color: currentIsDarkMode ? Colors.white : Colors.black,
                            shape: BoxShape.circle,
                          ),
                          defaultTextStyle: TextStyle(color: theme.colorScheme.onSurface),
                          weekendTextStyle: TextStyle(color: theme.colorScheme.onSurface),
                          outsideTextStyle: TextStyle(color: theme.colorScheme.onSurface.withOpacity(0.6)),
                          selectedTextStyle: TextStyle(
                            color: currentIsDarkMode ? Colors.black : Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                          todayTextStyle: TextStyle(
                            color: theme.colorScheme.onSurface,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        headerStyle: HeaderStyle(
                          formatButtonVisible: true,
                          titleCentered: true,
                          formatButtonShowsNext: false,
                          formatButtonDecoration: BoxDecoration(
                            color: theme.colorScheme.surface,
                            borderRadius: BorderRadius.circular(16.0),
                          ),
                          formatButtonTextStyle: TextStyle(color: theme.colorScheme.onSurface),
                          titleTextStyle: TextStyle(
                            color: theme.colorScheme.onSurface,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          leftChevronIcon: Icon(Icons.chevron_left, color: theme.colorScheme.onSurface),
                          rightChevronIcon: Icon(Icons.chevron_right, color: theme.colorScheme.onSurface),
                        ),
                        daysOfWeekStyle: DaysOfWeekStyle(
                          weekdayStyle: TextStyle(color: theme.colorScheme.onSurface),
                          weekendStyle: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: _selectedEvents.isEmpty
                          ? Center(
                              child: Text(
                                'No classes scheduled for this day',
                                style: TextStyle(color: theme.colorScheme.onSurface),
                              ),
                            )
                          : ListView.builder(
                              padding: const EdgeInsets.symmetric(horizontal: 16.0),
                              itemCount: _selectedEvents.length,
                              itemBuilder: (context, index) {
                                final item = _selectedEvents[index];
                                return _buildClassCard(item, theme, currentIsDarkMode);
                              },
                            ),
                    ),
                  ],
                ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(Icons.home_outlined, color: theme.colorScheme.onSurface),
                  onPressed: () => Navigator.of(context).popUntil((route) => route.isFirst),
                ),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                IconButton(
                  icon: Icon(Icons.person_outline, color: theme.colorScheme.onSurface),
                  onPressed: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LoginPage(
                        isDarkMode: currentIsDarkMode,
                        toggleTheme: widget.toggleTheme,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}