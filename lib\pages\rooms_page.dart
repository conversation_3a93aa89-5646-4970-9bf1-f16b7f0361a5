// rooms_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'room_detail_page.dart';

class RoomsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedRooms;
  final bool isFromDetailPage;
  final String? buildingFilter;

  const RoomsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedRooms,
    this.isFromDetailPage = false,
    this.buildingFilter,
  }) : super(key: key);

  @override
  State<RoomsPage> createState() => _RoomsPageState();
}

class _RoomsPageState extends State<RoomsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('rooms_list');
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _rooms = [];
  List<Map<String, dynamic>> _filteredRooms = [];
  List<String> _availableBuildings = [];
  String? _currentBuildingFilter;
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;
  bool _isSearching = false;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    print("RoomsPage initState called");
    _currentBuildingFilter = widget.buildingFilter;
    _loadInitialData();
    _loadAvailableBuildings();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void didUpdateWidget(covariant RoomsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.collegeNameForTable != widget.collegeNameForTable || oldWidget.buildingFilter != widget.buildingFilter) {
      _currentBuildingFilter = widget.buildingFilter;
      _loadInitialData();
      _loadAvailableBuildings();
    }
    print("RoomsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("RoomsPage didChangeDependencies called");
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _filterRooms();
    });
  }

  void _filterRooms() {
    if (_searchQuery.isEmpty) {
      _filteredRooms = List.from(_rooms);
    } else {
      _filteredRooms = _rooms.where((room) {
        final fullname = (room['fullname'] ?? '').toLowerCase();
        final building = (room['building'] ?? '').toLowerCase();
        final roomType = (room['roomtype'] ?? '').toLowerCase();
        
        return fullname.contains(_searchQuery) ||
               building.contains(_searchQuery) ||
               roomType.contains(_searchQuery);
      }).toList();
    }
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
        _searchQuery = '';
        _filterRooms();
      }
    });
  }

  Future<void> _loadAvailableBuildings() async {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_rooms';
    
    try {
      final response = await Supabase.instance.client
          .from(tableName)
          .select('building')
          .not('building', 'is', null);
      
      if (mounted) {
        final buildings = response
            .map((row) => row['building'] as String?)
            .where((building) => building != null && building.isNotEmpty)
            .cast<String>()
            .toSet()
            .toList();
        
        buildings.sort();
        
        setState(() {
          _availableBuildings = buildings;
        });
      }
    } catch (error) {
      print("Error loading available buildings: $error");
    }
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedRooms != null && widget.preloadedRooms!.isNotEmpty && _currentBuildingFilter == null) {
      print("Preloaded rooms found, using them.");
      setState(() {
        _rooms = List<Map<String, dynamic>>.from(widget.preloadedRooms!);
        _rooms.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _filteredRooms = List.from(_rooms);
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded rooms or empty list, loading from database.");
      await _loadRoomsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_rooms';
    
    try {
      var query = Supabase.instance.client.from(tableName).select('id');
      if (_currentBuildingFilter != null) {
        query = query.eq('building', _currentBuildingFilter!);
      }
      
      final response = await query
          .order('fullname', ascending: true)
          .range(_rooms.length, _rooms.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadRoomsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadRoomsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_rooms';

    try {
      int startRange = initialLoad ? 0 : _rooms.length;
      int endRange = startRange + _pageSize - 1;

      var query = Supabase.instance.client.from(tableName).select('*');
      if (_currentBuildingFilter != null) {
        query = query.eq('building', _currentBuildingFilter!);
      }

      final response = await query
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      if (mounted) {
        setState(() {
          final newRooms = List<Map<String, dynamic>>.from(response);
          if (initialLoad) {
            _rooms = newRooms;
          } else {
            _rooms.addAll(newRooms);
          }
          _rooms.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _filteredRooms = List.from(_rooms);
          _filterRooms();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching rooms: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_rooms';
    _realtimeChannel = Supabase.instance.client
        .channel('rooms_realtime')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: tableName,
      callback: (payload) async {
        if (!mounted) return;
        await _loadRoomsFromSupabase(initialLoad: true);
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    print("RoomsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed && !_isLoadingMore && _hasMore && _scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 100) {
      _loadMoreRooms();
    }
  }

  Future<void> _loadMoreRooms() async {
    if (!_isLoadingMore && _hasMore && _searchQuery.isEmpty) {
      print("Loading more rooms...");
      await _loadRoomsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _showOfflineSnackbar() {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _navigateToDetail(BuildContext context, Map<String, dynamic> room) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RoomDetailPage(
            room: room,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForTable: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  void _showFilterModal() {
    String? tempBuildingFilter = _currentBuildingFilter;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: StatefulBuilder(
            builder: (BuildContext context, StateSetter setModalState) {
              final theme = Theme.of(context);
              final isDarkMode = theme.brightness == Brightness.dark;
              
              return Container(
                width: MediaQuery.of(context).size.width * 0.85,
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.6,
                ),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Text(
                        'Filter by Building',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                    
                    Flexible(
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Building',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                              const SizedBox(height: 12),
                              
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(color: theme.colorScheme.outline),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<String>(
                                    value: tempBuildingFilter,
                                    isExpanded: true,
                                    hint: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 12),
                                      child: Text(
                                        'Select Building',
                                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    items: [
                                      DropdownMenuItem<String>(
                                        value: null,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 12),
                                          child: Text(
                                            'All Buildings',
                                            style: TextStyle(color: theme.colorScheme.onSurface),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ),
                                      ..._availableBuildings.map((building) => DropdownMenuItem<String>(
                                        value: building,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 12),
                                          child: Text(
                                            building,
                                            style: TextStyle(color: theme.colorScheme.onSurface),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      )),
                                    ],
                                    onChanged: (String? newValue) {
                                      setModalState(() {
                                        tempBuildingFilter = newValue;
                                      });
                                    },
                                  ),
                                ),
                              ),
                              const SizedBox(height: 24),
                            ],
                          ),
                        ),
                      ),
                    ),
                    
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                setModalState(() {
                                  tempBuildingFilter = null;
                                });
                              },
                              style: OutlinedButton.styleFrom(
                                foregroundColor: theme.colorScheme.onSurface,
                                side: BorderSide(color: theme.colorScheme.outline),
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: const Text('Clear'),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                setState(() {
                                  _currentBuildingFilter = tempBuildingFilter;
                                  _page = 0;
                                  _hasMore = true;
                                });
                                Navigator.pop(context);
                                _loadRoomsFromSupabase(initialLoad: true);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: isDarkMode ? Colors.white : Colors.black,
                                foregroundColor: isDarkMode ? Colors.black : Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: const Text('Apply'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    print("RoomsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String title = _currentBuildingFilter != null 
        ? 'Rooms in $_currentBuildingFilter' 
        : 'All Rooms';
    
    final roomsToShow = _searchQuery.isEmpty ? _rooms : _filteredRooms;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: _isSearching
            ? TextField(
                controller: _searchController,
                autofocus: true,
                decoration: InputDecoration(
                  hintText: 'Search rooms...',
                  hintStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                  border: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                ),
                style: TextStyle(
                  fontSize: 16,
                  color: theme.colorScheme.onSurface,
                ),
              )
            : Text(
                title,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
        actions: [
          IconButton(
            icon: Icon(
              _isSearching ? Icons.close : Icons.search,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _toggleSearch,
          ),
          IconButton(
            icon: Icon(
              Icons.filter_list,
              color: _currentBuildingFilter != null 
                  ? theme.colorScheme.primary 
                  : theme.colorScheme.onSurface,
            ),
            onPressed: _showFilterModal,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadRoomsFromSupabase(initialLoad: true);
              },
              child: roomsToShow.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: Center(
                              child: Text(
                                _searchQuery.isNotEmpty 
                                    ? 'No rooms found matching "$_searchQuery"'
                                    : 'No rooms available.',
                              ),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: roomsToShow.length + (_hasMore && _searchQuery.isEmpty ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < roomsToShow.length) {
                          final room = roomsToShow[index];
                          return _buildRoomCard(room, theme, currentIsDarkMode);
                        } else if (_hasMore && _searchQuery.isEmpty) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRoomCard(
    Map<String, dynamic> room,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = room['fullname'] ?? 'Unknown';
    final String building = room['building'] ?? '';
    final String roomType = room['roomtype'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, room),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.meeting_room,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (building.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          building,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (roomType.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          roomType,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}