import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'login_page.dart';
import 'rental_detail_page.dart';
import 'tertiary_rentals_page.dart';

class EquipmentRentalsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const EquipmentRentalsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<EquipmentRentalsPage> createState() => _EquipmentRentalsPageState();
}

class _EquipmentRentalsPageState extends State<EquipmentRentalsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('equipment_rentals_list');
  bool _isDisposed = false;
  
  List<Rental> _equipmentRentals = [];
  List<Rental> _filteredRentals = [];
  bool _isLoading = false;
  String _errorMessage = '';
  
  // Filter variables
  String _selectedDepartment = 'All';
  List<String> _departments = ['All'];
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    print("EquipmentRentalsPage initState called");
    _fetchEquipmentRentals();
  }

  @override
  void didUpdateWidget(covariant EquipmentRentalsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("EquipmentRentalsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("EquipmentRentalsPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.dispose();
    print("EquipmentRentalsPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _fetchEquipmentRentals() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_rentals';
      
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .eq('equipmentrental', true)
          .order('fullname', ascending: true);

      final List<Rental> rentals = List<Map<String, dynamic>>.from(response)
          .map((json) => Rental.fromJson(json))
          .toList();
      
      // Extract unique departments for filters
      final Set<String> departments = {'All'};
      for (var rental in rentals) {
        if (rental.department.isNotEmpty) {
          departments.add(rental.department);
        }
      }

      if (mounted) {
        setState(() {
          _equipmentRentals = rentals;
          _filteredRentals = List.from(rentals);
          _departments = departments.toList();
          _isLoading = false;
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching equipment rentals: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _errorMessage = errorMsg;
        });
      }
    }
  }

  void _filterRentals() {
    setState(() {
      _filteredRentals = _equipmentRentals.where((rental) {
        // Filter by department
        if (_selectedDepartment != 'All' && rental.department != _selectedDepartment) {
          return false;
        }
        
        return true;
      }).toList();
    });
  }

  void _navigateToDetail(BuildContext context, Rental rental) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RentalDetailPage(
            rental: rental,
            institutionName: widget.institutionName,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("EquipmentRentalsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Equipment Rentals',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          if (_departments.length > 1)
            IconButton(
              icon: Icon(
                Icons.filter_list,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                setState(() {
                  _showFilters = !_showFilters;
                });
              },
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: _fetchEquipmentRentals,
              child: Column(
                children: [
                  // Filters section
                  if (_showFilters && _departments.length > 1)
                    Container(
                      width: double.infinity,
                      color: theme.colorScheme.surface,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Department filter
                            Text(
                              'Department:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 8),
                            SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                children: _departments.map((department) {
                                  return Padding(
                                    padding: const EdgeInsets.only(right: 8.0),
                                    child: FilterChip(
                                      label: Text(department),
                                      selected: _selectedDepartment == department,
                                      onSelected: (selected) {
                                        if (selected) {
                                          setState(() {
                                            _selectedDepartment = department;
                                            _filterRentals();
                                          });
                                        }
                                      },
                                      backgroundColor: theme.colorScheme.surface,
                                      selectedColor: currentIsDarkMode ? Colors.white : Colors.black,
                                      labelStyle: TextStyle(
                                        color: _selectedDepartment == department
                                            ? (currentIsDarkMode ? Colors.black : Colors.white)
                                            : theme.colorScheme.onSurface,
                                      ),
                                      side: BorderSide(
                                        color: _selectedDepartment == department
                                            ? (currentIsDarkMode ? Colors.white : Colors.black)
                                            : theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  
                  // List section
                  Expanded(
                    child: _filteredRentals.isEmpty
                        ? LayoutBuilder(
                            builder: (BuildContext context, BoxConstraints constraints) {
                              return SingleChildScrollView(
                                physics: const AlwaysScrollableScrollPhysics(),
                                child: SizedBox(
                                  height: constraints.maxHeight,
                                  child: Center(
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.devices,
                                          size: 64,
                                          color: theme.colorScheme.onSurface.withOpacity(0.5),
                                        ),
                                        const SizedBox(height: 16),
                                        Text(
                                          _equipmentRentals.isEmpty
                                              ? 'No equipment rentals available.'
                                              : 'No equipment matches your filters.',
                                          style: TextStyle(
                                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                                            fontSize: 16,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          )
                        : ListView.builder(
                            key: _listKey,
                            controller: _scrollController,
                            shrinkWrap: true,
                            physics: const AlwaysScrollableScrollPhysics(),
                            padding: const EdgeInsets.all(16),
                            itemCount: _filteredRentals.length,
                            itemBuilder: (context, index) {
                              final rental = _filteredRentals[index];
                              return _buildRentalCard(rental, theme, currentIsDarkMode);
                            },
                          ),
                  ),
                ],
              ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRentalCard(
    Rental rental,
    ThemeData theme,
    bool isDarkMode,
  ) {
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, rental),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.devices,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      rental.fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (rental.about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          rental.about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (rental.department.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Department: ${rental.department}',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (rental.dimensions.isNotEmpty && rental.dimensions != 'Not specified')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Specifications: ${rental.dimensions}',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (rental.pricing.isNotEmpty && rental.pricing != 'Not specified')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Pricing: ${rental.pricing}',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}