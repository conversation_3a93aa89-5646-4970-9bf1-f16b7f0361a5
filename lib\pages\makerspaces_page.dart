// makerspaces_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'makerspace_detail_page.dart';

class MakerspacesPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedMakerspaces;
  final bool isFromDetailPage;

  const MakerspacesPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedMakerspaces,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<MakerspacesPage> createState() => _MakerspacesPageState();
}

class _MakerspacesPageState extends State<MakerspacesPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('makerspaces_list');
  List<Map<String, dynamic>> _makerspaces = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("MakerspacesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant MakerspacesPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("MakerspacesPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("MakerspacesPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedMakerspaces != null && widget.preloadedMakerspaces!.isNotEmpty) {
      print("Preloaded makerspaces found, using them.");
      setState(() {
        _makerspaces = List<Map<String, dynamic>>.from(widget.preloadedMakerspaces!);
        _makerspaces.forEach((makerspace) {
          makerspace['_isImageLoading'] = false;
        });
        _makerspaces.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded makerspaces or empty list, loading from database.");
      await _loadMakerspacesFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final makerspacesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_makerspaces';
    
    try {
      final response = await Supabase.instance.client
          .from(makerspacesTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_makerspaces.length, _makerspaces.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadMakerspacesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadMakerspacesFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final makerspacesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_makerspaces';

    try {
      int startRange = initialLoad ? 0 : _makerspaces.length;
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(makerspacesTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedMakerspaces =
          await _updateMakerspaceImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _makerspaces = updatedMakerspaces;
          } else {
            _makerspaces.addAll(updatedMakerspaces);
          }
          _makerspaces.forEach((makerspace) {
            makerspace['_isImageLoading'] = false;
          });
          _makerspaces.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
      _cacheMakerspaces(_makerspaces);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching makerspaces: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateMakerspaceImageUrls(
      List<Map<String, dynamic>> makerspaces) async {
    List<Future<void>> futures = [];
    for (final makerspace in makerspaces) {
      if (makerspace['image_url'] == null ||
          makerspace['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(makerspace));
      }
    }
    await Future.wait(futures);
    return makerspaces;
  }
  
  void _setupRealtime() {
    final makerspacesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_makerspaces';
    _realtimeChannel = Supabase.instance.client
        .channel('makerspaces')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: makerspacesTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newMakerspaceId = payload.newRecord['id'];
          final newMakerspaceResponse = await Supabase.instance.client
              .from(makerspacesTableName)
              .select('*')
              .eq('id', newMakerspaceId)
              .single();
          if (mounted) {
            Map<String, dynamic> newMakerspace = Map.from(newMakerspaceResponse);
            final updatedMakerspace = await _updateMakerspaceImageUrls([newMakerspace]);
            setState(() {
              _makerspaces.add(updatedMakerspace.first);
              updatedMakerspace.first['_isImageLoading'] = false;
              _makerspaces.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedMakerspaceId = payload.newRecord['id'];
          final updatedMakerspaceResponse = await Supabase.instance.client
              .from(makerspacesTableName)
              .select('*')
              .eq('id', updatedMakerspaceId)
              .single();
          if (mounted) {
            final updatedMakerspace = Map<String, dynamic>.from(updatedMakerspaceResponse);
            setState(() {
              _makerspaces = _makerspaces.map((makerspace) {
                return makerspace['id'] == updatedMakerspace['id'] ? updatedMakerspace : makerspace;
              }).toList();
              _makerspaces.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedMakerspaceId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _makerspaces.removeWhere((makerspace) => makerspace['id'] == deletedMakerspaceId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("MakerspacesPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreMakerspaces();
    }
  }

  Future<void> _loadMoreMakerspaces() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more makerspaces...");
      await _loadMakerspacesFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _cacheMakerspaces(List<Map<String, dynamic>> makerspaces) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String makerspacesJson = jsonEncode(makerspaces);
      await prefs.setString(
          'makerspaces_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          makerspacesJson);
    } catch (e) {
      print('Error caching makerspaces: $e');
    }
  }
  
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> makerspace) async {
    if (makerspace['_isImageLoading'] == true) return;
    if (makerspace['image_url'] != null && makerspace['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => makerspace['_isImageLoading'] = true);

    final fullname = makerspace['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeMakerspaceBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/makerspaces';

    try {
      final file = await Supabase.instance.client.storage.from(collegeMakerspaceBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeMakerspaceBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        makerspace['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        makerspace['_isImageLoading'] = false;
      });
    } else {
      makerspace['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> makerspace) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MakerspaceDetailPage(
            makerspace: makerspace,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("MakerspacesPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Makerspaces',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadMakerspacesFromSupabase(initialLoad: true);
              },
              child: _makerspaces.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No makerspaces available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _makerspaces.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _makerspaces.length) {
                          final makerspace = _makerspaces[index];
                          return VisibilityDetector(
                            key: Key('makerspace_${makerspace['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (makerspace['image_url'] == null ||
                                      makerspace['image_url'] == 'assets/placeholder_image.png') &&
                                  !makerspace['_isImageLoading']) {
                                _fetchImageUrl(makerspace);
                              }
                            },
                            child: _buildMakerspaceCard(makerspace, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMakerspaceCard(
    Map<String, dynamic> makerspace,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = makerspace['fullname'] ?? 'Unknown';
    final String building = makerspace['building'] ?? '';
    final String room = makerspace['room'] ?? '';
    final String about = makerspace['about'] ?? '';
    final String imageUrl = makerspace['image_url'] ?? '';

    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, makerspace),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.build,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.build,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.build,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}