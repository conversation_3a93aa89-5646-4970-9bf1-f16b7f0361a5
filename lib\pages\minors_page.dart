import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'minor_detail_page.dart';
import 'login_page.dart';

class MinorsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedMinors;
  final bool isFromDetailPage;

  const MinorsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedMinors,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _MinorsPageState createState() => _MinorsPageState();
}

class _MinorsPageState extends State<MinorsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('minors_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _minors = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("MinorsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedMinors != null && widget.preloadedMinors!.isNotEmpty) {
      print("Preloaded minors found, using them.");
      setState(() {
        _minors = List<Map<String, dynamic>>.from(widget.preloadedMinors!);
        _minors.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedMinors!.length >= _pageSize;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded minors or empty list, loading from database.");
      await _loadMinorsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final minorsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_minors';

    try {
      final response = await Supabase.instance.client
          .from(minorsTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_minors.length, _minors.length + _pageSize - 1);

      if (!mounted) return;
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadMinorsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadMinorsFromSupabase called - initialLoad: $initialLoad");

    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final minorsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_minors';

    try {
      int startRange;

      if (initialLoad) {
        startRange = 0;
      } else {
        startRange = _minors.length;
      }
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(minorsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      if (mounted) {
        setState(() {
          final newItems = List<Map<String, dynamic>>.from(response);
          if (initialLoad) {
            _minors = newItems;
          } else {
            _minors.addAll(newItems);
          }
          _minors.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') ||
            errorStr.contains('offline')) {
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') &&
            errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching minors: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final minorsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_minors';
    _realtimeChannel = Supabase.instance.client
        .channel('minors_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: minorsTableName,
      callback: (payload) async {
        if (!mounted) return;
        print("Realtime update received for minors: ${payload.eventType}");
        await _loadMinorsFromSupabase(initialLoad: true);
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("MinorsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreMinors();
    }
  }

  Future<void> _loadMoreMinors() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more minors...");
      await _loadMinorsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> minor) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MinorDetailPage(
            minor: minor,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  void _showOfflineSnackbar() {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("MinorsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Minors',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _hasMore = true;
                });
                await _loadMinorsFromSupabase(initialLoad: true);
              },
              child: _minors.isEmpty
                  ? LayoutBuilder(
                      builder:
                          (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No minors available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _minors.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _minors.length) {
                          final minor = _minors[index];
                          return _buildMinorCard(minor, theme);
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMinorCard(
    Map<String, dynamic> minor,
    ThemeData theme,
  ) {
    final String fullname = minor['fullname'] ?? 'Unknown';
    final String department = minor['department'] ?? '';
    final String school = minor['school'] ?? '';
    final String duration = minor['duration'] ?? '';
    final String about = minor['about'] ?? '';
    final bool isAccredited = minor['accredited'] == true;
    final bool isDarkMode = theme.brightness == Brightness.dark;

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, minor),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.school,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Text(
                            fullname,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                        if (isAccredited)
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.onSurface,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Accredited',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.surface,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (department.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Department: $department',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (school.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'School: $school',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (duration.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Duration: $duration',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}