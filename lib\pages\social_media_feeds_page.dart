import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart'; // <-- 1. IMPORT ADDED
import 'package:visibility_detector/visibility_detector.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:io';

import 'social_media_feed_detail_page.dart';
import 'login_page.dart';

class SocialMediaFeedsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedSocialMediaFeeds;
  final bool isFromDetailPage;

  const SocialMediaFeedsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedSocialMediaFeeds,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _SocialMediaFeedsPageState createState() => _SocialMediaFeedsPageState();
}

class _SocialMediaFeedsPageState extends State<SocialMediaFeedsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('social_media_feeds_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _socialMediaFeeds = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 15;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("SocialMediaFeedsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedSocialMediaFeeds != null &&
        widget.preloadedSocialMediaFeeds!.isNotEmpty) {
      print("Preloaded social media feeds found, using them.");
      setState(() {
        _socialMediaFeeds =
            List<Map<String, dynamic>>.from(widget.preloadedSocialMediaFeeds!);
        _sortFeeds();
        _hasMore = widget.preloadedSocialMediaFeeds!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      await _checkForMoreData();
    } else {
      print("No preloaded feeds or empty list, loading from database.");
      await _loadSocialMediaFeedsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final socialMediaFeedsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_socialmediafeeds';
    try {
      final response = await Supabase.instance.client
          .from(socialMediaFeedsTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_socialMediaFeeds.length,
              _socialMediaFeeds.length + _pageSize - 1);

      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadSocialMediaFeedsFromSupabase(
      {bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadSocialMediaFeedsFromSupabase called - initialLoad: $initialLoad, current page: $_page");

    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final socialMediaFeedsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_socialmediafeeds';

    try {
      int startRange;
      if (initialLoad) {
        startRange = 0;
        _page = 0;
      } else {
        startRange = _socialMediaFeeds.length;
      }
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(socialMediaFeedsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final newFeeds = List<Map<String, dynamic>>.from(response);

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _socialMediaFeeds = newFeeds;
          } else {
            _socialMediaFeeds.addAll(newFeeds);
          }
          _sortFeeds();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = newFeeds.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') &&
            errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching social media feeds: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final socialMediaFeedsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_socialmediafeeds';
    _realtimeChannel = Supabase.instance.client
        .channel('social_media_feeds')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: socialMediaFeedsTableName,
      callback: (payload) async {
        if (!mounted) return;

        if (payload.eventType == PostgresChangeEvent.insert) {
          final newId = payload.newRecord['id'];
          final newResponse = await Supabase.instance.client
              .from(socialMediaFeedsTableName)
              .select('*')
              .eq('id', newId)
              .single();
          if (mounted) {
            setState(() {
              _socialMediaFeeds.add(Map.from(newResponse));
              _sortFeeds();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedId = payload.newRecord['id'];
          final updatedResponse = await Supabase.instance.client
              .from(socialMediaFeedsTableName)
              .select('*')
              .eq('id', updatedId)
              .single();
          if (mounted) {
            final updatedFeed = Map<String, dynamic>.from(updatedResponse);
            setState(() {
              _socialMediaFeeds = _socialMediaFeeds.map((feed) {
                return feed['id'] == updatedFeed['id'] ? updatedFeed : feed;
              }).toList();
              _sortFeeds();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _socialMediaFeeds.removeWhere((feed) => feed['id'] == deletedId);
            });
          }
        }
      },
    ).subscribe();
  }

  void _sortFeeds() {
    _socialMediaFeeds.sort((a, b) => (a['fullname'] ?? '')
        .toLowerCase()
        .compareTo((b['fullname'] ?? '').toLowerCase()));
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("SocialMediaFeedsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreSocialMediaFeeds();
    }
  }

  Future<void> _loadMoreSocialMediaFeeds() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more social media feeds...");
      await _loadSocialMediaFeedsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;
  
  // --- 2. NEW METHOD TO LAUNCH URL ---
  Future<void> _launchSocialMediaLink(String urlStr) async {
    if (urlStr.isEmpty) {
      _showErrorSnackbar('No link available.');
      return;
    }

    // Prepend 'https://' if the scheme is missing, which is common for social media links
    if (!urlStr.startsWith('http://') && !urlStr.startsWith('https://')) {
      urlStr = 'https://$urlStr';
    }
    
    final uri = Uri.tryParse(urlStr);
    
    if (uri != null && await canLaunchUrl(uri)) {
      // Launch with default mode (in-app browser)
      await launchUrl(uri);
    } else {
      _showErrorSnackbar('Could not launch link: $urlStr');
    }
  }

  void _navigateToDetail(
      BuildContext context, Map<String, dynamic> feed) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SocialMediaFeedDetailPage(
            socialMediaFeed: feed,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  void _showOfflineSnackbar() {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  IconData _getPlatformIcon(String platform) {
    switch (platform.toLowerCase()) {
      case 'facebook': return FontAwesomeIcons.facebook;
      case 'twitter': case 'x': return FontAwesomeIcons.xTwitter;
      case 'instagram': return FontAwesomeIcons.instagram;
      case 'linkedin': return FontAwesomeIcons.linkedin;
      case 'youtube': return FontAwesomeIcons.youtube;
      case 'tiktok': return FontAwesomeIcons.tiktok;
      case 'snapchat': return FontAwesomeIcons.snapchat;
      case 'pinterest': return FontAwesomeIcons.pinterest;
      case 'reddit': return FontAwesomeIcons.redditAlien;
      case 'whatsapp': return FontAwesomeIcons.whatsapp;
      case 'telegram': return FontAwesomeIcons.telegram;
      case 'discord': return FontAwesomeIcons.discord;
      case 'github': return FontAwesomeIcons.github;
      case 'medium': return FontAwesomeIcons.medium;
      default: return FontAwesomeIcons.globe;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Social Media',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadSocialMediaFeedsFromSupabase(initialLoad: true);
              },
              child: _socialMediaFeeds.isEmpty
                  ? LayoutBuilder(
                      builder:
                          (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No social media feeds available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount:
                          _socialMediaFeeds.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _socialMediaFeeds.length) {
                          final feed = _socialMediaFeeds[index];
                          return _buildSocialMediaFeedCard(
                              feed, theme, currentIsDarkMode);
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(Icons.home_outlined,
                      color: theme.colorScheme.onSurface),
                  onPressed: () =>
                      Navigator.of(context).popUntil((route) => route.isFirst),
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(Icons.person_outline,
                      color: theme.colorScheme.onSurface),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // --- 3. WIDGET MODIFIED TO MAKE LINK CLICKABLE ---
  Widget _buildSocialMediaFeedCard(
    Map<String, dynamic> feed,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = feed['fullname'] ?? 'Unnamed Feed';
    final String platform = feed['platform'] ?? 'Website';
    final String link = feed['link'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, feed),
        borderRadius: BorderRadius.circular(12), // Match card's border radius
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: FaIcon(
                  _getPlatformIcon(platform),
                  color: isDarkMode ? Colors.white : Colors.black,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (platform.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          platform,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    if (link.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        // Wrap the Text widget with an InkWell to make it tappable
                        child: InkWell(
                          onTap: () => _launchSocialMediaLink(link),
                          child: Text(
                            link,
                            style: TextStyle(
                              color: theme.colorScheme.primary, // Use primary color for links
                              decoration: TextDecoration.underline, // Underline to show it's a link
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}