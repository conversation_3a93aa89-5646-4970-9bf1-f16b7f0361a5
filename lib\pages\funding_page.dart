import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'funding_detail_page.dart';
import 'login_page.dart';

class FundingPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedFunding;
  final bool isFromDetailPage;

  const FundingPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedFunding,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _FundingPageState createState() => _FundingPageState();
}

class _FundingPageState extends State<FundingPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('funding_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _funding = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    if (widget.preloadedFunding != null &&
        widget.preloadedFunding!.isNotEmpty) {
      setState(() {
        _funding = List.from(widget.preloadedFunding!);
        _funding.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedFunding!.length >= _pageSize;
        _page = 0;
      });
    } else {
      await _loadFundingFromSupabase(initialLoad: true);
    }
  }

  void _setupRealtime() {
    final fundingTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_funding';
    _realtimeChannel = Supabase.instance.client
        .channel('funding_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: fundingTableName,
      callback: (payload) async {
        if (!mounted) return;

        if (payload.eventType == PostgresChangeEvent.insert) {
          final newRecord = Map<String, dynamic>.from(payload.newRecord);
          setState(() {
            _funding.add(newRecord);
            _funding.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          });
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedRecord = Map<String, dynamic>.from(payload.newRecord);
          setState(() {
            _funding = _funding.map((fund) {
              return fund['id'] == updatedRecord['id'] ? updatedRecord : fund;
            }).toList();
            _funding.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          });
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedRecordId = payload.oldRecord['id'];
          setState(() {
            _funding.removeWhere((fund) => fund['id'] == deletedRecordId);
          });
        }
      },
    ).subscribe();
  }

  Future<void> _loadFundingFromSupabase({bool initialLoad = false}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) return;

    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    try {
      final fundingTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_funding';
      final start = initialLoad ? 0 : _funding.length;
      final end = start + _pageSize - 1;

      final response = await Supabase.instance.client
          .from(fundingTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(start, end);

      if (_isDisposed) return;

      setState(() {
        if (initialLoad) {
          _funding = List<Map<String, dynamic>>.from(response);
        } else {
          _funding.addAll(List<Map<String, dynamic>>.from(response));
        }

        _hasMore = response.length == _pageSize;
        if (initialLoad) _page = 0; else _page++;
      });
    } on SocketException {
      if (_isDisposed) return;
      _showErrorSnackbar("Offline. Please check your internet connection.");
    } catch (e) {
      if (_isDisposed) return;
      final errorStr = e.toString().toLowerCase();
      if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
        _showErrorSnackbar("Almost all data for this institution hasn't been added yet.");
      } else {
        _showErrorSnackbar('Error loading funding: $e');
      }
      setState(() => _hasMore = false);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
        });
      }
    }
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadFundingFromSupabase(initialLoad: false);
    }
  }

  Future<void> _reloadFunding() async {
    setState(() {
      _page = 0;
      _hasMore = true;
    });
    await _loadFundingFromSupabase(initialLoad: true);
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Scholarships & Funding',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _reloadFunding,
              child: _funding.isEmpty
                  ? LayoutBuilder(
                      builder: (context, constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: Center(
                              child: Text(
                                'No scholarships or funding found.',
                                style: TextStyle(color: theme.colorScheme.onSurface),
                              ),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: _funding.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index == _funding.length) {
                          return _isLoadingMore
                              ? const Center(
                                  child: Padding(
                                    padding: EdgeInsets.all(8.0),
                                    child: CircularProgressIndicator(),
                                  ),
                                )
                              : const SizedBox.shrink();
                        }
                        return _buildFundingCard(
                          _funding[index],
                          theme,
                          currentIsDarkMode,
                        );
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFundingCard(
    Map<String, dynamic> funding,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = funding['fullname'] ?? 'Unknown';
    final String major = funding['major'] ?? '';
    final String about = funding['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => FundingDetailPage(
                funding: funding,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.monetization_on,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (major.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'For: $major',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}