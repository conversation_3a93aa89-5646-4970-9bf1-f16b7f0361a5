import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:intl/intl.dart';
import 'dart:io';

import 'construction_detail_page.dart';
import 'login_page.dart';

class ConstructionPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedConstructionProjects;
  final bool isFromDetailPage;

  const ConstructionPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedConstructionProjects,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _ConstructionPageState createState() => _ConstructionPageState();
}

class _ConstructionPageState extends State<ConstructionPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('construction_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _constructionProjects = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 10; // Page size for construction projects
  bool _hasMore = true;

  String get _constructionTableName =>
      '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_construction';

  @override
  void initState() {
    super.initState();
    print("ConstructionPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant ConstructionPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("ConstructionPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("ConstructionPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedConstructionProjects != null &&
        widget.preloadedConstructionProjects!.isNotEmpty) {
      print("Preloaded construction projects found, using them.");
      setState(() {
        _constructionProjects = List<Map<String, dynamic>>.from(widget.preloadedConstructionProjects!);
        _sortProjects();
        _hasMore = widget.preloadedConstructionProjects!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      await _checkForMoreData();
    } else {
      print("No preloaded projects or empty list, loading from database.");
      await _loadConstructionProjectsFromSupabase(initialLoad: true);
    }
  }
  
  Future<void> _checkForMoreData() async {
    try {
      final response = await Supabase.instance.client
          .from(_constructionTableName)
          .select('id')
          .order('startdate', ascending: false)
          .order('id', ascending: false)
          .range(_constructionProjects.length, _constructionProjects.length + _pageSize - 1);

      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadConstructionProjectsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }
    print("_loadConstructionProjectsFromSupabase called - initialLoad: $initialLoad, current page: $_page");

    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    try {
      int startRange;
      if (initialLoad) {
        startRange = 0;
        _page = 0;
      } else {
        startRange = _constructionProjects.length;
      }
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(_constructionTableName)
          .select('*')
          .order('startdate', ascending: false)
          .order('id', ascending: false) // Secondary sort for stable pagination
          .range(startRange, endRange);

      final newProjects = List<Map<String, dynamic>>.from(response);

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _constructionProjects = newProjects;
          } else {
            _constructionProjects.addAll(newProjects);
          }
          _sortProjects();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = newProjects.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching construction projects: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    _realtimeChannel = Supabase.instance.client
        .channel('construction')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: _constructionTableName,
      callback: (payload) async {
        if (!mounted) return;

        if (payload.eventType == PostgresChangeEvent.insert) {
          final newProjectId = payload.newRecord['id'];
          final newProjectResponse = await Supabase.instance.client
              .from(_constructionTableName)
              .select('*')
              .eq('id', newProjectId)
              .single();
          if (mounted) {
            Map<String, dynamic> newProject = Map.from(newProjectResponse);
            setState(() {
              _constructionProjects.add(newProject);
              _sortProjects();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedProjectId = payload.newRecord['id'];
          final updatedProjectResponse = await Supabase.instance.client
              .from(_constructionTableName)
              .select('*')
              .eq('id', updatedProjectId)
              .single();
          if (mounted) {
            final updatedProject = Map<String, dynamic>.from(updatedProjectResponse);
            setState(() {
              _constructionProjects = _constructionProjects.map((project) {
                return project['id'] == updatedProject['id'] ? updatedProject : project;
              }).toList();
              _sortProjects();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedProjectId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _constructionProjects.removeWhere((project) => project['id'] == deletedProjectId);
            });
          }
        }
      },
    ).subscribe();
  }

  void _sortProjects() {
    _constructionProjects.sort((a, b) {
      final DateTime? dateA = a['startdate'] != null ? DateTime.tryParse(a['startdate']) : null;
      final DateTime? dateB = b['startdate'] != null ? DateTime.tryParse(b['startdate']) : null;
      if (dateA == null && dateB == null) return 0;
      if (dateA == null) return 1; // Projects without start date go to the end
      if (dateB == null) return -1;
      return dateB.compareTo(dateA); // Sort descending by start date
    });
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("ConstructionPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreConstructionProjects();
    }
  }

  Future<void> _loadMoreConstructionProjects() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more construction projects...");
      await _loadConstructionProjectsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> project) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ConstructionDetailPage(
            constructionProject: project,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForTable: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("ConstructionPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Construction',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadConstructionProjectsFromSupabase(initialLoad: true);
              },
              child: _constructionProjects.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No construction projects available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _constructionProjects.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _constructionProjects.length) {
                          final project = _constructionProjects[index];
                          return VisibilityDetector(
                            key: Key('construction_${project['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {},
                            child: _buildConstructionCard(project, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildConstructionCard(
    Map<String, dynamic> project,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = project['fullname'] ?? 'Unnamed Project';
    final String location = project['location'] ?? '';
    final String description = project['description'] ?? '';
    
    String dateInfo = 'Date not specified';
    if (project['startdate'] != null) {
      try {
        final startDate = DateTime.parse(project['startdate']);
        final DateFormat formatter = DateFormat('MMM d, yyyy');
        
        if (project['enddate'] != null) {
          final endDate = DateTime.parse(project['enddate']);
          if (endDate.isAfter(startDate)) {
             dateInfo = '${formatter.format(startDate)} - ${formatter.format(endDate)}';
          } else {
             dateInfo = 'Starts: ${formatter.format(startDate)}';
          }
        } else {
            dateInfo = 'Starts: ${formatter.format(startDate)}';
        }
      } catch (e) {
        print("Error parsing date for project ${project['id']}: $e");
        dateInfo = "Date parsing error";
      }
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, project),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.construction_outlined,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (description.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          description,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (location.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          location,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        dateInfo,
                        style: TextStyle(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}