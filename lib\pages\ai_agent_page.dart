import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:speech_to_text/speech_recognition_error.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'dart:async';
import 'dart:io';
import 'package:collection/collection.dart';

// --- NEW IMPORT for our cache warmer service ---
import '../services/ai_cache_warmer.dart';

// --- UPDATED ChatMessage Class ---
class ChatMessage {
  final String text;
  final bool isUser;
  final bool isTyping;
  final bool isErrorOrFailure; // This flag is crucial for memory management

  ChatMessage({
    required this.text,
    required this.isUser,
    this.isTyping = false,
    this.isErrorOrFailure = false, // Default to false
  });

  @override bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChatMessage &&
          runtimeType == other.runtimeType &&
          text == other.text &&
          isUser == other.isUser &&
          isTyping == other.isTyping &&
          isErrorOrFailure == other.isErrorOrFailure;

  @override
  int get hashCode =>
      text.hashCode ^
      isUser.hashCode ^
      isTyping.hashCode ^
      isErrorOrFailure.hashCode;
}

class ConversationMemory {
  List<String> mentionedTopics = [];
  Map<String, String> establishedFacts = {};
  String? studentYear;
  String? studentMajor;
  List<String> concerns = [];
  String? studentName;
  Map<String, int> topicFrequency = {};

  void updateFromMessage(String message, bool isUser) {
    if (isUser) {
      final yearMatch = RegExp(r'\b(freshman|sophomore|junior|senior|first.year|second.year|third.year|fourth.year)\b', caseSensitive: false).firstMatch(message);
      if (yearMatch != null) studentYear = yearMatch.group(1)?.toLowerCase();

      final majorKeywords = ['major', 'studying', 'degree', 'program'];
      for (String keyword in majorKeywords) {
        if (message.toLowerCase().contains(keyword)) {
          final words = message.toLowerCase().split(' ');
          final keywordIndex = words.indexOf(keyword);
          if (keywordIndex != -1 && keywordIndex < words.length - 1) studentMajor = words[keywordIndex + 1];
        }
      }

      final concernPatterns = [
        r'\b(worried|concerned|confused|unsure|help|stuck|lost|overwhelmed)\b',
        r'\b(don.t know|not sure|unclear|difficult|hard|struggling)\b'
      ];
      for (String pattern in concernPatterns) {
        if (RegExp(pattern, caseSensitive: false).hasMatch(message)) {
          concerns.add(message);
          break;
        }
      }

      final topics = ['admissions', 'housing', 'tuition', 'classes', 'financial aid', 'scholarship', 'graduation', 'transcript'];
      for (String topic in topics) {
        if (message.toLowerCase().contains(topic)) {
          mentionedTopics.add(topic);
          topicFrequency[topic] = (topicFrequency[topic] ?? 0) + 1;
        }
      }
    }
  }

  String getContextSummary() {
    List<String> contextParts = [];
    if (studentYear != null) contextParts.add("Student level: $studentYear");
    if (studentMajor != null) contextParts.add("Studying: $studentMajor");
    if (concerns.isNotEmpty) contextParts.add("Has expressed concerns");
    if (mentionedTopics.isNotEmpty) contextParts.add("Interested in: ${mentionedTopics.toSet().take(3).join(', ')}");
    return contextParts.join('; ');
  }

  void clear() {
    mentionedTopics.clear();
    establishedFacts.clear();
    studentYear = null;
    studentMajor = null;
    concerns.clear();
    studentName = null;
    topicFrequency.clear();
  }
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;
  final ThemeData theme;
  const ChatBubble({ Key? key, required this.message, required this.isDarkMode, required this.theme }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (message.isTyping) {
       return Align( alignment: Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
            decoration: BoxDecoration( color: theme.colorScheme.surfaceVariant.withOpacity(0.8), borderRadius: const BorderRadius.only( topLeft: Radius.circular(4.0), topRight: Radius.circular(18.0), bottomLeft: Radius.circular(18.0), bottomRight: Radius.circular(18.0) ) ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [ Text( message.text, style: TextStyle( color: isDarkMode ? Colors.white70 : Colors.black54, fontStyle: FontStyle.italic, fontSize: 14 ) ) ],
            ),
          ),
       );
    }
    final bubbleColor = message.isUser ? (isDarkMode ? Colors.grey[800]! : Colors.grey[300]!) : theme.colorScheme.surfaceVariant;
    final textColor = isDarkMode ? Colors.white : Colors.black;
    final borderRadius = BorderRadius.only( topLeft: Radius.circular(message.isUser ? 18.0 : 4.0), topRight: Radius.circular(message.isUser ? 4.0 : 18.0), bottomLeft: const Radius.circular(18.0), bottomRight: const Radius.circular(18.0) );

    return Align( alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 5.0, horizontal: 8.0),
        padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 10.0),
        constraints: BoxConstraints( maxWidth: MediaQuery.of(context).size.width * 0.8 ),
        decoration: BoxDecoration( color: bubbleColor, borderRadius: borderRadius, boxShadow: [ BoxShadow( color: Colors.black.withOpacity(0.06), blurRadius: 3, offset: const Offset(1, 2) ) ] ),
        // The replaceAll is a fallback to guarantee the correct bullet point format.
        child: SelectableText( message.text.replaceAll('*', '•'), style: TextStyle( color: textColor, fontSize: 15, height: 1.35 ) ),
      ),
    );
  }
}

// --- Main Widget ---
class AiAgentPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final Map<String, dynamic>? collegeData;

  const AiAgentPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    this.collegeData,
  }) : super(key: key);

  @override
  _AiAgentPageState createState() => _AiAgentPageState();
}

enum ProcessingStep { idle, processing }

class _AiAgentPageState extends State<AiAgentPage> {
  // --- UI and Core State ---
  final TextEditingController _messageController = TextEditingController();
  final List<ChatMessage> _messages = [];
  ProcessingStep _processingStep = ProcessingStep.idle;
  final ScrollController _scrollController = ScrollController();
  bool _isCacheReady = false;

  // --- Enhanced Memory and Context ---
  ConversationMemory _conversationMemory = ConversationMemory();
  final Map<String, String> _conversationCache = {};

  // --- LLM and API State ---
  final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // IMPORTANT: Replace with your actual key
  final String _model = 'gemini-2.0-flash';
  int _dailyTokenCount = 0;
  final int _maxDailyTokens = 5000000;
  int _lastPromptTokenCount = 0;
  int _lastApiResponseTokenCount = 0;
  static const double _inputCostPer1MTokens_FlashLatest = 0.075;
  static const double _outputCostPer1MTokens_FlashLatest = 0.30;
  final double _inputCostPer1kTokens = _inputCostPer1MTokens_FlashLatest / 1000.0;
  final double _outputCostPer1kTokens = _outputCostPer1MTokens_FlashLatest / 1000.0;
  final double _exchangeRate = 2000.0;
  static const String _fallbackSignal = "[FALLBACK_REQUIRED]";

  // --- Speech and TTS State ---
  late stt.SpeechToText _speechToText;
  late FlutterTts _flutterTts;
  bool _isListening = false;
  bool _isMuted = true;
  List<Map<String, String>> _availableVoices = [];
  Map<String, String>? _selectedVoice;
  bool _voicesLoaded = false;
  String? _outputLanguage = 'English';

  // --- Language to TTS Locale Mapping ---
  final Map<String, String> _languageToLocale = {
    'English': 'en-US',
    'Chichewa': 'ny-MW', // Fallback to English if not available
    'Chitumbuka': 'en-US', // Fallback to English
    'Swahili': 'sw-KE',
    'Shona': 'sn-ZW', // Fallback to English if not available
    'Zulu': 'zu-ZA',
  };

  // --- Table Manifest (FIXED) ---
  final Map<String, String> _tableManifest = {
      'helpdesks': 'Campus help desk locations and services (IT, library, student services, etc.). Good for finding helpdesk contact or location.',
      'accessibility': 'Disability support services and campus accessibility resources.',
      'faq': 'Frequently asked questions about various campus topics. Good for general queries.',
      'links': 'Important website links for departments, applications, and resources.',
      'construction': 'Current/pending campus construction projects with locations and timelines.',
      'printing': 'Printing service locations, costs, and availability.',
      'daycares': 'On-campus childcare facilities and registration information.',
      'sustainability': 'Environmental initiatives and green campus programs.',
      'notices': 'Campus-wide announcements and time-sensitive alerts.',
      'socialmediafeeds': 'Official college social media accounts and links.',
      'admissionsprocess': 'Step-by-step application procedures, admission requirements, and related information.',
      'registrationprocess': 'Course enrollment steps and academic planning.',
      'selection': 'Demographic/academic profiles of admitted students, acceptance rates, and selection criteria.',
      'costsorrates': 'Tuition fees, housing costs, school fees, and other financial rates.',
      'scholarships': 'Information on available grants, awards, scholarships, bursaries, loans, and other financial aid opportunities.',
      'payments': 'Payment methods, portals, and billing information for tuition and fees.',
      'orientations': 'New student orientation programs and schedules.',
      'symposiums': 'Academic conference details and participation info.',
      'graduation': 'Commencement ceremony logistics and graduate data.',
      'people': 'Faculty/staff directories with contact info, roles, and departments. Useful for finding specific individuals.',
      'currentstudents': 'Profiles of enrolled students (majors, housing, etc.).',
      'housing': 'Residence hall details, policies, and living arrangements on campus. Use for queries about dorms or on-campus living.',
      'locallodging': 'Off-campus hotels/B&Bs near the college for visitors.',
      'shopsoreateries': 'On-campus stores, cafes, and dining options. Lists places to eat or shop on campus.',
      'mealplans': 'Dining plan options and associated costs for students.',
      'localareadining': 'Nearby off-campus restaurants and food discounts.',
      'studentdiscounts': 'Local business offers for students.',
      'inventory': 'Campus store products, merchandise, and pricing.',
      'menus': 'Daily dining hall meal offerings and nutritional info.',
      'campusshuttle': 'Transportation routes and schedules for the campus bus/shuttle.',
      'parkingspaces': 'Parking lot locations, permits, and regulations.',
      'localtransport': 'Public transit options and regional travel to/from campus.',
      'schools': 'Academic divisions (e.g., School of Arts), their deans, and departments within them.',
      'departments': 'Academic department info, faculty contacts, and associated school.',
      'centers': 'Research centers, institutes, and special program facilities.',
      'documents': 'Official forms, reports, and policy PDFs for download.',
      'majors': 'Undergraduate degree programs, requirements, and descriptions. Provides a list of all available majors.',
      'minors': 'Minor programs, concentration details, and certification information. Provides a list of all available minors.',
      'funding': 'General research grants and project funding opportunities.',
      'coursecatalog': 'Course descriptions, prerequisites, credits, and class details for all academic offerings.',
      'enrollmentexercise': 'Registration practice simulations.',
      'academicresources': 'Tutoring, libraries, and study support services.',
      'academichonors': "Dean's list, honors programs, and GPA requirements for academic distinction.",
      'academicprizes': 'Student achievement awards and competitions.',
      'academicdress': 'Graduation regalia info and ordering.',
      'entryrequirements': 'Detailed admission criteria, prerequisites, and application guidelines.',
      'gradingscale': 'Letter grade definitions and GPA calculations.',
      'programs': 'Special academic initiatives, student programs, and partnerships.',
      'signatureevents': 'Major annual campus traditions/ceremonies.',
      'traditions': 'Historical campus customs and rituals.',
      'partnershipopportunities': 'Community/corporate collaboration programs.',
      'athletics': 'Sports teams, schedules, and athlete resources.',
      'orgsorclubs': 'Student organizations, club listings, and contact information.',
      'researchgroups': 'Active academic research teams/projects.',
      'committees': 'Campus governance groups and their functions.',
      'news': 'College news articles and press releases.',
      'periodicals': 'Student-run publications and magazines.',
      'radio': 'Campus radio station programming and staff.',
      'television': 'Student-produced TV shows and content.',
      'photos': 'Campus photo archives and event galleries.',
      'videos': 'Official college videos and student projects.',
      'accelerators': 'Entrepreneurship programs and startup support.',
      'makerspaces': 'Creative labs with equipment/tech resources.',
      'startupfunds': 'Funding opportunities for student ventures.',
      'startups': 'Student-run businesses and their profiles.',
      'researchprojects': 'Ongoing faculty/student research studies.',
      'theses': 'Senior capstone projects and research papers.',
      'books': 'Publications by faculty/alumni.',
      'articles': 'Academic papers and journal contributions.',
      'patents': 'Innovations/IP created at the college.',
      'building': 'Campus building info, maps, and facilities directories.',
      'rooms': 'Classroom/lab specifications and reservations.',
      'roomequipment': 'AV/tech gear available in spaces.',
      'roomassignments': 'Student housing placements.',
      'publicart': 'Campus art installations and exhibits.',
      'emergencyequipment': 'Safety devices and their locations.',
      'classschedules': 'Course times, locations, and instructors.',
      'weeklyschedule': 'Recurring events and meetings.',
      'events': 'Campus activities calendar, event details, and RSVP info.',
      'academiccalendar': 'Term dates, holidays, and academic deadlines.',
      'feedback': 'Student surveys and feedback forms.',
      'historicaltimeline': 'Key moments in college history.',
      'rentals': 'Equipment rental or space rental options and policies. Can be ambiguous.',
      'rentalequipmentcalendar': 'Reservation schedule for gear.',
      'jobs': 'Campus employment, career opportunities, and job listings.',
      'services': 'Overview of student support services, including IT, health, counseling, career centers, etc.',
      'atms': 'On-campus cash machine locations.',
      'clinicsorhospitals': 'Health center services and hours.',
      'counselingservices': 'Mental health resources and appointments.',
      'emergencycontacts': 'Critical phone numbers and protocols.',
      'safetyprocedures': 'Emergency response guidelines.',
      'connectivity': 'WiFi, tech resources, and IT support.',
      'giving': 'Donation opportunities and alumni fundraising.',
  };

  @override
  void initState() {
    super.initState();
    _speechToText = stt.SpeechToText();
    _flutterTts = FlutterTts();
    _messageController.addListener(() => setState(() {}));
    WidgetsBinding.instance.addPostFrameCallback((_) { _initializeAgent(); });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    if (_speechToText.isListening) _speechToText.cancel();
    _flutterTts.stop();
    super.dispose();
  }

  Future<void> _initializeAgent() async {
    await _initSpeech();
    await _configureTts();
    await _loadTtsVoices();

    final collegeId = widget.collegeData?['id']?.toString() ?? '';
    if (!AICacheWarmer.isCacheReadyFor(collegeId)) {
        _addInitialGreeting();
    }

    try {
      await AICacheWarmer.prewarmCacheForCollege(widget.collegeData!);
      if (mounted) {
        setState(() {
          _isCacheReady = true;
        });
        _showReadyMessage();
      }
    } catch (e) {
      if (mounted) {
        _showError("Failed to load the knowledge base. Please try again later.");
        setState(() => _isCacheReady = false);
      }
    }
  }

  // --- HELPER METHOD: Get Language Instruction ---
  String _getLanguageInstruction() {
    if (_outputLanguage != null && _outputLanguage != 'English') {
      return "CRITICAL: You MUST respond ONLY in the $_outputLanguage language. Your entire response must be in $_outputLanguage.\n";
    }
    return '';
  }

  Future<void> _sendMessage(String message) async {
    final String userMessageText = message.trim();
    if (userMessageText.isEmpty || _processingStep != ProcessingStep.idle) return;

    setState(() {
      _processingStep = ProcessingStep.processing;
      _messages.add(ChatMessage(text: userMessageText, isUser: true));
    });

    _conversationMemory.updateFromMessage(userMessageText, true);
    _scrollToBottom();
    _messageController.clear();

    try {
      if (_isChitChat(userMessageText)) {
        _addOrUpdateTypingIndicator("Thinking...");
        final response = await _generateChitChatResponse(userMessageText);
        _showFinalResponse(userMessageText, response);
        return;
      }
      
      _addOrUpdateTypingIndicator("Analyzing query...");
      List<String> relevantTables = await _selectRelevantTables(userMessageText);
      
      _addOrUpdateTypingIndicator("Retrieving information...");
      String tier1Context = await _fetchContextForQuery(userMessageText, relevantTables);
      
      _addOrUpdateTypingIndicator("Formulating initial answer...");
      String initialResponse = await _generateInitialResponseAndEvaluate(userMessageText, tier1Context);

      if (initialResponse.contains(_fallbackSignal)) {
        _addOrUpdateTypingIndicator("Searching deeper...");
        String finalContext = _getFullKnowledgeBaseContext(tier1Context);
        
        final finalResponse = await _generateFinalResponse(userMessageText, finalContext);
        _showFinalResponse(userMessageText, finalResponse);
      } else {
        _showFinalResponse(userMessageText, initialResponse);
      }

    } catch (e) {
      print('Error in message processing pipeline: $e');
      _removeTypingIndicator();
      _showError(e.toString().replaceFirst("Exception: ", ""));
      if (mounted) setState(() => _processingStep = ProcessingStep.idle);
    }
  }

  bool _isChitChat(String message) {
    final chitChatPatterns = [
      r'^\s*(hi|hello|hey|heya|yo|greetings)\s*!*$',
      r'^\s*(thanks|thank you|thx|ty)\s*!*$',
      r'^\s*(bye|goodbye|see ya|cya)\s*!*$',
      r'^\s*how are you\??\s*$',
      r'^\s*who are you\??\s*$',
      r'^\s*(ok|okay|cool|nice|awesome|great)\s*!*$',
    ];
    final lowerCaseMessage = message.toLowerCase();
    for (final pattern in chitChatPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(lowerCaseMessage)) {
        return true;
      }
    }
    return false;
  }

  Future<String> _generateChitChatResponse(String message) async {
    final collegeName = widget.collegeData?['fullname'] ?? 'the institution';
    final languageInstruction = _getLanguageInstruction();
    
    final prompt = '''${languageInstruction}You are Vern, a friendly and helpful AI assistant for $collegeName. You are making small talk with a user. Keep your responses brief, warm, and conversational.

User said: "$message"

Your response:''';
    return await _callGeminiApi(prompt, isContextBuilding: true, temperature: 0.7);
  }

  Future<List<String>> _selectRelevantTables(String query) async {
    final manifestFormatted = _tableManifest.entries.map((e) => '- ${e.key}: ${e.value}').join('\n');
    final prompt = '''SYSTEM: You are an AI assistant selecting relevant database tables to answer a user's query.
User Query: "$query"
Available Tables:
$manifestFormatted
---
Instruction: Based on the query, list the short table names (e.g., 'housing', 'costs') most likely to contain the answer. If a table seems relevant, include it. If none seem relevant, respond with 'NONE'. List ONLY comma-separated table names.
model:''';

    final response = await _callGeminiApi(prompt, isContextBuilding: true);
    if (response.toLowerCase() == 'none') return [];
    return response.split(',')
        .map((t) => t.trim().toLowerCase().replaceAll(RegExp(r'[^a-z_]+'), ''))
        .where((t) => t.isNotEmpty).toList();
  }
  
  // Helper function to replace the removed String extension
  String _readableFieldName(String s) {
    if (s.isEmpty) return '';
    return s.split('_').map((word) {
      if (word.isEmpty) return '';
      return word[0].toUpperCase() + word.substring(1);
    }).join(' ');
  }

  Future<String> _fetchContextForQuery(String query, List<String> tables) async {
    final ftsFuture = _performFtsSearch(query);
    final tableFutures = tables.map((table) => _retrieveDataForTable(table)).toList();

    final results = await Future.wait([ftsFuture, ...tableFutures]);

    final ftsResult = results[0] as String;
    final tableResults = results.sublist(1).cast<String>();

    final sb = StringBuffer();
    bool tableDataAdded = false;
    for (int i = 0; i < tables.length; i++) {
      if (tableResults[i].isNotEmpty) {
        final truncatedData = tableResults[i].substring(0, tableResults[i].length.clamp(0, 15000));
        sb.writeln('--- Information from ${_readableFieldName(tables[i])} Table ---\n$truncatedData\n');
        tableDataAdded = true;
      }
    }
    if (!tableDataAdded) {
       sb.writeln('--- Information from Tables ---\nNo relevant structured data found.\n');
    }

    if (ftsResult.isNotEmpty) {
      sb.writeln('--- Information from Document Search ---\n$ftsResult\n');
    }

    return sb.toString();
  }

  String _getFullKnowledgeBaseContext(String tier1Context) {
      final sb = StringBuffer(tier1Context);
      sb.writeln("\n--- Full Knowledge Base (All Documents) ---\n");
      if(AICacheWarmer.sFullKnowledgeBaseCache != null && AICacheWarmer.sFullKnowledgeBaseCache!.isNotEmpty) {
          sb.writeln(AICacheWarmer.sFullKnowledgeBaseCache);
      } else {
          sb.writeln("Full knowledge base is not available.");
      }
      return sb.toString();
  }

  Future<String> _generateInitialResponseAndEvaluate(String query, String contextData) async {
    final history = _buildConversationHistory();
    final languageInstruction = _getLanguageInstruction();
    
    String prompt = '''${languageInstruction}You are an AI assistant with a two-step process. This is Step 1.
Your goal is to answer the user's question using ONLY the provided 'Table' and 'Document Search' context.

RULES:
1. Answer the question as best as you can using ONLY the information below.
2. Format your answer for attractive, easy readability. CRITICAL: For any lists, you MUST use the '•' character as a bullet point. Do NOT use '*' or '-'.
3. **CRITICAL SELF-EVALUATION:** After your answer, you MUST decide if the context was sufficient.
   - If you believe the provided context was insufficient to answer the question *completely* and that a more thorough search might yield a better answer, you MUST append the exact signal '$_fallbackSignal' to the VERY END of your response.
   - If the context is completely empty and you cannot answer at all, your entire response should be ONLY the signal '$_fallbackSignal'.
   - If your answer is complete and confident based on the context, DO NOT add the signal.

--- PROVIDED CONTEXT (LIMITED) ---
${contextData.trim().isEmpty ? "No information found for this query." : contextData}
--- END CONTEXT ---

Recent Chat:
$history

STUDENT'S CURRENT QUESTION: "$query"

Respond as Vern:''';
    
    return await _callGeminiApi(prompt, isContextBuilding: false, temperature: 0.1, maxTokens: 2048);
  }

  // --- UPDATED with the new strict prompt ---
  Future<String> _generateFinalResponse(String query, String contextData) async {
    final collegeName = widget.collegeData?['fullname'] ?? 'the institution';
    final history = _buildConversationHistory();
    final languageInstruction = _getLanguageInstruction();

    String prompt = '''You are Vern, a helpful and knowledgeable AI assistant for $collegeName. Your goal is to provide accurate and complete answers based *only* on the provided context.

**PRIMARY DIRECTIVE: Your entire response MUST be generated exclusively from the information inside the "--- PROVIDED CONTEXT ---" block. Do not use any external knowledge.**

RULES:
$languageInstruction
- **Formatting:** Format your response for attractive, easy readability. Use clear paragraphs for explanations. **ABSOLUTELY CRITICAL: For any lists, you MUST use the '•' character as a bullet point. DO NOT use '*' or '-'.**
- **For list requests (e.g., "list all majors"), you MUST provide the complete list found in the context.** Do not summarize or shorten lists.
- **Handling Missing Information:**
  - **YOU ARE STRICTLY PROHIBITED from suggesting the user call a phone number or visit a website, unless they explicitly ask for a phone number or website.**
  - If the answer is not found in the provided context, your ONLY permitted response is to state, "I currently do not have information on that topic." Do not apologize or add any other text.
- Do not mention the words "context", "documents", or "tables" in your response. Provide the answer as if you knew it.

--- PROVIDED CONTEXT (FULL) ---
${contextData.trim().isEmpty ? "No information found for this query." : contextData}
--- END CONTEXT ---

Recent Chat:
$history

STUDENT'S CURRENT QUESTION: "$query"

Respond as Vern:''';
    
    return await _callGeminiApi(prompt, isContextBuilding: false, temperature: 0.2, maxTokens: 8192);
  }

  Future<String> _retrieveDataForTable(String tableName) async {
    if (AICacheWarmer.sDbCache.containsKey(tableName)) return AICacheWarmer.sDbCache[tableName]!;
    if (_conversationCache.containsKey(tableName)) return _conversationCache[tableName]!;

    final collegeIdentifier = _getCollegeIdentifier();
    final fullTableName = "${collegeIdentifier}_$tableName";

    final data = await AICacheWarmer.fetchSupabaseDataForRow(fullTableName, maxLengthBudget: 20000);
    if (mounted && data.isNotEmpty) _conversationCache[tableName] = data;
    return data;
  }

  Future<String> _performFtsSearch(String query) async {
    if (query.isEmpty) return "";
    final collegeId = widget.collegeData?['id'];
    if (collegeId == null) return "";

    try {
      final response = await Supabase.instance.client.rpc('search_document_chunks', params: {'p_query': query, 'p_college_id': collegeId, 'p_limit': 20});
      if (response == null || (response as List).isEmpty) return "";
      return (response as List).map((doc) => doc['content'] as String).join('\n---\n');
    } catch (e) {
      print("Error during Full-Text Search: $e"); return "";
    }
  }
  
  void _showFinalResponse(String userMessage, String responseText) {
    _removeTypingIndicator();
    final cleanResponseText = responseText.replaceAll(_fallbackSignal, "").trim();

    // Updated and stricter heuristic to detect if the AI is saying it doesn't know.
    final failureKeywords = [
      "i do not have information on that topic", 
      "couldn't find", 
      "don't have that specific information", 
      "not available in my knowledge base", 
      "unable to find"
    ];
    bool isFailureResponse = failureKeywords.any((kw) => cleanResponseText.toLowerCase().contains(kw));

    if (cleanResponseText.isEmpty || isFailureResponse) {
        _handleIrrecoverableFailure(cleanResponseText.isEmpty ? null : cleanResponseText);
        return;
    }

    final aiResponseMessage = ChatMessage(text: cleanResponseText, isUser: false);

    int promptTokens = _calculateApproxTokens(userMessage + _buildConversationHistory());
    int responseTokens = _calculateApproxTokens(cleanResponseText);

    setState(() {
      _messages.add(aiResponseMessage);
      _lastPromptTokenCount = promptTokens;
      _lastApiResponseTokenCount = responseTokens;
      _dailyTokenCount += promptTokens + responseTokens;
      _processingStep = ProcessingStep.idle;
    });
    _scrollToBottom();
    _speakText(cleanResponseText);
  }

  String _getCollegeIdentifier() => widget.collegeData?['tableprefix']?.toString().trim() ?? widget.collegeData!['fullname']!.toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]+'), '').replaceAll(' ', '_');

  Future<void> _initSpeech() async {
    try {
      bool available = await _speechToText.initialize(
        onError: (error) => print('Speech recognition error: ${error.errorMsg}'),
        onStatus: (status) => setState(() => _isListening = _speechToText.isListening),
      );
      if (!available && mounted) _showError("Speech recognition is not available on this device.");
    } catch (e) {
      if (mounted) _showError("Failed to initialize speech recognition.");
    }
  }

  Future<void> _configureTts() async {
    await _flutterTts.awaitSpeakCompletion(true);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setSpeechRate(0.5);
    await _flutterTts.setPitch(1.0);
    
    // Set language based on selected output language
    if (_outputLanguage != null && _languageToLocale.containsKey(_outputLanguage!)) {
      try {
        await _flutterTts.setLanguage(_languageToLocale[_outputLanguage!]!);
      } catch (e) {
        print("Error setting TTS language to ${_languageToLocale[_outputLanguage!]}: $e. Using default.");
        // Fallback to English
        await _flutterTts.setLanguage('en-US');
      }
    }
    
    // Set voice if one is selected
    if (_selectedVoice != null) {
      try { 
        await _flutterTts.setVoice(_selectedVoice!); 
      } catch (e) { 
        print("Error setting TTS voice: $e. Using default."); 
      }
    }
  }

  Future<void> _loadTtsVoices() async {
    if (!mounted) return;
    try {
      var voices = await _flutterTts.getVoices;
      if (voices != null && voices is List) {
        // Get target locale based on selected language
        String targetLocale = _languageToLocale[_outputLanguage ?? 'English'] ?? 'en-US';
        String languageCode = targetLocale.split('-')[0]; // e.g., 'en' from 'en-US'
        
        List<Map<String, String>> filteredVoices = voices
            .map((v) => Map<String, String>.from(v as Map))
            .where((v) {
              String? locale = v['locale'];
              if (locale == null) return false;
              
              // First try exact match, then language code match, then fallback to English
              return locale == targetLocale || 
                     locale.startsWith('$languageCode-') ||
                     locale.startsWith('en-');
            })
            .sortedBy<String>((v) {
              // Prioritize exact matches, then language matches, then English
              String? locale = v['locale'];
              if (locale == targetLocale) return '0${v['name'] ?? ''}';
              if (locale?.startsWith('$languageCode-') == true) return '1${v['name'] ?? ''}';
              return '2${v['name'] ?? ''}';
            })
            .toList();

        setState(() {
          _availableVoices = filteredVoices;
          _voicesLoaded = true;
          
          // Select the best available voice
          _selectedVoice = _availableVoices.firstWhereOrNull((v) => 
            v['locale'] == targetLocale && 
            (v['name']?.toLowerCase().contains('female') ?? false)
          ) ?? _availableVoices.firstWhereOrNull((v) => 
            v['locale'] == targetLocale
          ) ?? _availableVoices.firstWhereOrNull((v) => 
            v['locale']?.startsWith('$languageCode-') == true
          ) ?? _availableVoices.firstOrNull;
        });
        
        await _configureTts();
      }
    } catch (e) { 
      print("Error getting TTS voices: $e"); 
    }
  }

  Future<void> _speakText(String text) async {
    if (_isMuted || text.isEmpty) return;
    await _flutterTts.stop(); 
    await _flutterTts.speak(text);
  }

  void _toggleListening() {
    if (_speechToText.isListening) _speechToText.stop();
    else if (_speechToText.isAvailable) _speechToText.listen(onResult: (result) => _messageController.text = result.recognizedWords);
  }

  Future<String> _callGeminiApi(String promptText, {bool isContextBuilding = false, double temperature = 0.2, int maxTokens = 4096}) async {
    if (!_apiKey.startsWith('AIza')) throw Exception("Invalid API key.");

    final modelToUse = _model.startsWith('models/') ? _model.split('/').last : _model;
    final url = Uri.parse('https://generativelanguage.googleapis.com/v1beta/models/$modelToUse:generateContent?key=$_apiKey');
    
    final body = jsonEncode({
      'contents': [{'role': 'user', 'parts': [{'text': promptText}]}],
      'generationConfig': {
        'temperature': temperature,
        'maxOutputTokens': maxTokens
      },
      'safetySettings': [
        {'category': 'HARM_CATEGORY_HARASSMENT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_HATE_SPEECH', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
      ]
    });

    try {
      final response = await http.post(url, headers: {'Content-Type': 'application/json'}, body: body).timeout(const Duration(seconds: 120));
      final decoded = jsonDecode(response.body);
      if (response.statusCode == 200) {
        if (decoded['candidates'] == null || (decoded['candidates'] as List).isEmpty) {
          final blockReason = decoded['promptFeedback']?['blockReason'];
          if (blockReason != null) {
            throw Exception("Request blocked due to $blockReason. Please rephrase your query.");
          }
          throw Exception("API returned an empty response. The model may be unavailable or the query was filtered.");
        }
        return decoded['candidates']?[0]?['content']?['parts']?[0]?['text']?.trim() ?? '';
      }
      throw Exception("API request failed: ${decoded['error']?['message'] ?? 'Unknown error'}");
    } on TimeoutException { throw Exception("Request to AI service timed out."); }
    on SocketException { throw Exception("Network error: Could not connect to AI service."); }
    catch (e) { throw Exception("An unexpected error occurred: $e"); }   
  }
    
  void _addInitialGreeting() {
    String collegeName = widget.collegeData?['fullname'] ?? 'the institution';
    final greetings = ["Hi! I'm Vern, your AI assistant for $collegeName", "Hello! I'm Vern. I'm preparing my knowledge base to assist you. This will just take a moment."];
    final greeting = greetings[DateTime.now().millisecond % greetings.length];
    if(mounted && _messages.isEmpty) {
      setState(() { 
        _messages.add(ChatMessage(text: greeting, isUser: false)); 
      });
      _speakText(greeting);
    }
  }

  void _showReadyMessage() {
   const readyMessageText = "I have finished preparing and am now ready to help. How can I assist you?";
   final readyMessage = ChatMessage(text: readyMessageText, isUser: false);
   if (mounted) {
     if (_messages.isEmpty || _messages.last.text != readyMessageText) {
       setState(() {
         if (_messages.isNotEmpty && _messages.first.text.contains("preparing my knowledge base")) {
           _messages.clear(); 
           _messages.add(readyMessage);
         } else if (_messages.isEmpty) {
           _messages.add(readyMessage);
         }
       });
     }
     _scrollToBottom();
     _speakText(readyMessageText);
   }
 }

  void _startNewConversation() {
     if(_processingStep != ProcessingStep.idle) return;
     if (mounted) {
       if (_speechToText.isListening) _speechToText.stop();
       _flutterTts.stop();
       setState(() {
           _messages.clear();
           _conversationMemory.clear();
           _processingStep = ProcessingStep.idle;
       });
       _showReadyMessage();
     }
  }

  void _showError(String errorMessageText) {
    String naturalError = "Sorry, I ran into a problem. Could you try asking differently?";
    if (errorMessageText.contains('network') || errorMessageText.contains('timeout')) naturalError = "I'm having trouble connecting. Please check your connection and try again.";
    else if (errorMessageText.contains('API') || errorMessageText.contains('service') || errorMessageText.contains('blocked')) naturalError = "I'm having some technical difficulties. Please try again shortly.";
    _handleIrrecoverableFailure(naturalError);
  }
  
  void _handleIrrecoverableFailure(String? message) {
    if (!mounted) return;
    
    final failureMessage = message ?? "I couldn't find an answer to that. Please try rephrasing or ask me something else.";

    _removeTypingIndicator();
    setState(() {
      _messages.add(ChatMessage(
        text: failureMessage,
        isUser: false,
        isErrorOrFailure: true,
      ));
      _conversationMemory.clear(); 
      if (_processingStep != ProcessingStep.idle) _processingStep = ProcessingStep.idle;
    });

    _scrollToBottom();
    _speakText(failureMessage);
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) _scrollController.animateTo(_scrollController.position.maxScrollExtent, duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
    });
  }

  void _addOrUpdateTypingIndicator(String text) {
    if (!mounted) return;
    _messages.removeWhere((m) => m.isTyping);
    setState(() => _messages.add(ChatMessage(text: text, isUser: false, isTyping: true)));
    _scrollToBottom();
  }

  void _removeTypingIndicator() {
    if (!mounted) return;
    if(_messages.any((m) => m.isTyping)) setState(() => _messages.removeWhere((m) => m.isTyping));
  }

  String _buildConversationHistory() {
    final validMessages = _messages
        .where((m) => !m.isTyping && !m.isErrorOrFailure)
        .toList();

    final recentHistory = validMessages.length <= 4
        ? validMessages
        : validMessages.sublist(validMessages.length - 4);
        
    return recentHistory
        .map((m) => "${m.isUser ? 'User' : 'Vern'}: ${m.text}")
        .join('\n');
  }

  int _calculateApproxTokens(String text) => text.isEmpty ? 0 : (text.trim().length / 4.0).ceil();

  Map<String, double> _calculateThreadMetrics() {
    int inputTokens = _messages.where((m) => m.isUser && !m.isTyping).fold(0, (prev, m) => prev + _calculateApproxTokens(m.text));
    int outputTokens = _messages.where((m) => !m.isUser && !m.isTyping).fold(0, (prev, m) => prev + _calculateApproxTokens(m.text));
    return {
      'totalTokens': (inputTokens + outputTokens).toDouble(),
      'totalCost': (inputTokens / 1000 * _inputCostPer1kTokens) + (outputTokens / 1000 * _outputCostPer1kTokens),
    };
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final iconColor = isDark ? Colors.white70 : Colors.black87;
    String appBarTitle = widget.collegeData?['fullname'] != null ? "${widget.collegeData!['fullname']} AI" : 'College AI Assistant';
    final bool canInteract = _processingStep == ProcessingStep.idle && _isCacheReady;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface, surfaceTintColor: Colors.transparent, elevation: 1.0,
        leading: IconButton(icon: Icon(Icons.arrow_back, color: iconColor), onPressed: () => Navigator.pop(context)),
        title: Text(appBarTitle, style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: theme.colorScheme.onSurface), overflow: TextOverflow.ellipsis),
        actions: [
          IconButton(
            icon: Icon(Icons.edit_outlined, color: canInteract ? iconColor : theme.disabledColor), 
            tooltip: "Start New Conversation", 
            onPressed: canInteract ? _startNewConversation : null
          ),
          const SizedBox(width: 8.0), // Increased spacing
          Padding(
            padding: const EdgeInsets.only(right: 0.0),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _outputLanguage,
                hint: Padding(padding: const EdgeInsets.symmetric(horizontal: 2.0), child: Icon(Icons.g_translate, color: iconColor.withOpacity(0.7), size: 20)),
                icon: Padding(padding: const EdgeInsets.symmetric(horizontal: 2.0), child: Icon(Icons.arrow_drop_down, color: iconColor.withOpacity(0.7), size: 20)),
                selectedItemBuilder: (context) => ['English', 'Chichewa', 'Chitumbuka', 'Swahili', 'Shona', 'Zulu'].map((_) => Center(child: Tooltip(message: "Output Language: $_outputLanguage", child: Icon(Icons.g_translate, color: iconColor.withOpacity(0.7), size: 20)))).toList(),
                items: [
                  DropdownMenuItem(value: 'English', child: Text('English')), 
                  DropdownMenuItem(value: 'Chichewa', child: Text('Chichewa')), 
                  DropdownMenuItem(value: 'Chitumbuka', child: Text('Chitumbuka')),
                  DropdownMenuItem(value: 'Swahili', child: Text('Swahili')), 
                  DropdownMenuItem(value: 'Shona', child: Text('Shona')), 
                  DropdownMenuItem(value: 'Zulu', child: Text('Zulu')),
                ],
                onChanged: canInteract ? (String? newValue) { 
                  if (newValue != null && mounted) {
                    setState(() => _outputLanguage = newValue);
                    // Reload voices and reconfigure TTS when language changes
                    _loadTtsVoices();
                  }
                } : null,
                style: TextStyle(color: theme.colorScheme.onSurface), 
                dropdownColor: theme.colorScheme.surfaceVariant, 
                borderRadius: BorderRadius.circular(8), 
                elevation: 4,
              ),
            ),
          ),
          if (_voicesLoaded && _availableVoices.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(right: 0.0),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<Map<String, String>>(
                  value: _selectedVoice,
                  hint: Padding(padding: const EdgeInsets.symmetric(horizontal: 2.0), child: Icon(Icons.record_voice_over, color: iconColor.withOpacity(0.7), size: 20)),
                  icon: Padding(padding: const EdgeInsets.symmetric(horizontal: 2.0), child: Icon(Icons.arrow_drop_down, color: iconColor.withOpacity(0.7), size: 20)),
                  selectedItemBuilder: (context) => _availableVoices.map((_) => Center(child: Tooltip(message: _selectedVoice != null ? "${_selectedVoice!['name']} (${_selectedVoice!['locale']})" : "Select Voice", child: Icon(Icons.record_voice_over, color: iconColor.withOpacity(0.7), size: 20)))).toList(),
                  items: _availableVoices.map((voice) => DropdownMenuItem<Map<String, String>>(
                    value: voice, 
                    child: Tooltip(
                      message: "${voice['name']} (${voice['locale']})", 
                      child: Text(
                          "${voice['name']} (${voice['locale']})", 
                          style: TextStyle(fontSize: 12, color: theme.colorScheme.onSurfaceVariant), 
                          overflow: TextOverflow.ellipsis
                        )
                    )
                  )).toList(),
                  onChanged: canInteract ? (Map<String, String>? newValue) { 
                    if (newValue != null && mounted) { 
                      setState(() => _selectedVoice = newValue); 
                      _configureTts(); 
                    }
                  } : null,
                  style: TextStyle(color: theme.colorScheme.onSurface), 
                  dropdownColor: theme.colorScheme.surfaceVariant, 
                  borderRadius: BorderRadius.circular(8), 
                  elevation: 4,
                ),
              ),
            ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: ValueListenableBuilder<double>(
                valueListenable: AICacheWarmer.progressNotifier,
                builder: (context, progress, _) {
                  if (!_isCacheReady) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 80,
                            height: 80,
                            child: Stack(
                              fit: StackFit.expand,
                              children: [
                                CircularProgressIndicator(
                                  value: progress > 0 ? progress : null,
                                  strokeWidth: 6,
                                  backgroundColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                                ),
                                if (progress > 0)
                                  Center(
                                    child: Text(
                                      '${(progress * 100).toInt()}%',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                          if (_messages.isNotEmpty && !_isCacheReady)
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 20.0),
                              child: Text(
                                _messages.first.text,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.colorScheme.onSurface.withOpacity(0.7)
                                ),
                              ),
                            )
                          else
                             Text(
                                'Preparing AI Knowledge...',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.colorScheme.onSurface.withOpacity(0.7)
                                ),
                              ),
                        ],
                      ),
                    );
                  }
                  return Column(
                    children: [
                      if (_conversationMemory.getContextSummary().isNotEmpty) _buildContextBar(theme, isDark),
                      Expanded(
                        child: ListView.builder(
                          controller: _scrollController,
                          itemCount: _messages.length,
                          padding: const EdgeInsets.all(8.0),
                          itemBuilder: (context, index) => ChatBubble(message: _messages[index], isDarkMode: isDark, theme: theme)
                        )
                      ),
                    ],
                  );
                },
              ),
            ),
            _buildInputArea(theme, isDark),
          ],
        ),
      ),
    );
  }

  Widget _buildContextBar(ThemeData theme, bool isDark) {
    final contextSummary = _conversationMemory.getContextSummary();
    if (contextSummary.isEmpty) return const SizedBox.shrink();
    return Container(
      width: double.infinity, 
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8), 
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3), 
        borderRadius: BorderRadius.circular(8), 
        border: Border.all(color: (isDark ? Colors.white : Colors.black).withOpacity(0.2))
      ),
      child: Row(
        children: [
          Icon(Icons.psychology_outlined, size: 16, color: isDark ? Colors.white : Colors.black),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              contextSummary, 
              style: TextStyle(
                fontSize: 12, 
                color: (isDark ? Colors.white70 : Colors.black54), 
                fontStyle: FontStyle.italic
              )
            )
          ),
        ],
      ),
    );
  }

  Widget _buildInputArea(ThemeData theme, bool isDark) {
    final bool canInteract = _processingStep == ProcessingStep.idle;
    final bool tokenLimitReached = _dailyTokenCount >= _maxDailyTokens;
    final bool canSendOrListen = canInteract && !tokenLimitReached && _isCacheReady;

    String hintText = 'Ask a question...';
    if (!_isCacheReady) hintText = 'Initializing AI, please wait...';
    else if (_processingStep != ProcessingStep.idle) { 
      final typingMsg = _messages.lastWhereOrNull((m) => m.isTyping); 
      hintText = typingMsg?.text ?? "Processing..."; 
    }
    else if (_isListening) hintText = 'Listening... Speak now';
    else if (tokenLimitReached) hintText = 'Daily token limit reached';

    final threadMetrics = _calculateThreadMetrics();
    double threadCostMkw = threadMetrics['totalCost']! * _exchangeRate;

    return Container(
      padding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 10.0), 
      margin: const EdgeInsets.only(bottom: 4.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface, 
        boxShadow: [BoxShadow(blurRadius: 3, color: Colors.black.withOpacity(0.08), offset: const Offset(0, -1))]
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, 
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_isCacheReady && (_messageController.text.isNotEmpty || (threadMetrics['totalTokens'] ?? 0) > 0))
            Padding(
              padding: const EdgeInsets.only(bottom: 6.0, left: 8.0, right: 8.0),
              child: DefaultTextStyle(
                 style: TextStyle(color: theme.colorScheme.onSurface.withOpacity(0.7), fontSize: 10.5),
                 child: Wrap(
                    spacing: 8.0, runSpacing: 2.0,
                    children: [
                       ValueListenableBuilder<TextEditingValue>(
                          valueListenable: _messageController,
                          builder: (context, value, child) {
                             int approxTokens = _calculateApproxTokens(value.text);
                             if (approxTokens == 0) return const SizedBox.shrink();
                             double cost = (approxTokens / 1000.0) * _inputCostPer1kTokens;
                             return Text("Msg (est): ~${approxTokens}t (\${cost.toStringAsFixed(5)})");
                          },
                       ),
                       if (_lastPromptTokenCount > 0) Text("Last Prompt: ${_lastPromptTokenCount}t"),
                       if (_lastApiResponseTokenCount > 0) Text("Last Resp: ${_lastApiResponseTokenCount}t"),
                       if ((threadMetrics['totalTokens'] ?? 0) > 0) Text("Thread (est): ~${threadMetrics['totalTokens']!.round()}t (\${threadMetrics['totalCost']!.toStringAsFixed(5)}/MKW${threadCostMkw.toStringAsFixed(3)})"),
                       Text( 
                         "Daily Tokens: ${_dailyTokenCount}/${_maxDailyTokens}", 
                         style: TextStyle(
                           color: tokenLimitReached ? Colors.redAccent : theme.colorScheme.onSurface.withOpacity(0.7), 
                           fontWeight: tokenLimitReached ? FontWeight.bold : FontWeight.normal
                         )
                       ),
                    ],
                 ),
              ),
            ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: TextField(
                  controller: _messageController, 
                  enabled: canSendOrListen, 
                  textCapitalization: TextCapitalization.sentences, 
                  maxLines: 1,
                  decoration: InputDecoration(
                    hintText: hintText,
                    hintStyle: TextStyle(color: !canSendOrListen ? theme.disabledColor.withOpacity(0.6) : theme.hintColor.withOpacity(0.6)),
                    filled: true, 
                    fillColor: theme.scaffoldBackgroundColor,
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(24), borderSide: BorderSide.none),
                    enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(24), borderSide: BorderSide.none),
                    focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(24), borderSide: BorderSide.none),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  ),
                  style: TextStyle(color: canSendOrListen ? theme.colorScheme.onSurface : theme.disabledColor),
                  onSubmitted: (value) { if (value.isNotEmpty && canSendOrListen) _sendMessage(value); },
                ),
              ),
              IconButton(
                icon: Icon(_isListening ? Icons.mic : Icons.mic_none, color: canSendOrListen ? (isDark ? Colors.white : Colors.black) : theme.disabledColor), 
                onPressed: canSendOrListen ? _toggleListening : null, 
                tooltip: 'Voice Input', 
                splashRadius: 20
              ),
              IconButton(
                icon: Icon(_isMuted ? Icons.volume_off_outlined : Icons.volume_up_outlined, color: isDark ? Colors.white : Colors.black), 
                onPressed: () => setState(() => _isMuted = !_isMuted), 
                tooltip: _isMuted ? 'Enable Audio' : 'Mute Audio', 
                splashRadius: 20
              ),
              _processingStep == ProcessingStep.processing
                  ? Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0), 
                      child: SizedBox(
                        width: 24, 
                        height: 24, 
                        child: CircularProgressIndicator(strokeWidth: 2.5, color: isDark ? Colors.white : Colors.black)
                      )
                    )
                  : IconButton(
                      icon: Icon(Icons.send, color: canSendOrListen && _messageController.text.isNotEmpty ? (isDark ? Colors.white : Colors.black) : theme.disabledColor), 
                      onPressed: canSendOrListen && _messageController.text.isNotEmpty ? () => _sendMessage(_messageController.text) : null, 
                      tooltip: 'Send Message', 
                      splashRadius: 20
                    ),
            ],
          ),
        ],
      ),
    );
  }
}