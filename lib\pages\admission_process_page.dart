// admission_process_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';
import 'admission_process_detail_page.dart';

class AdmissionProcessPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AdmissionProcessPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  _AdmissionProcessPageState createState() => _AdmissionProcessPageState();
}

class _AdmissionProcessPageState extends State<AdmissionProcessPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('admission_process_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _admissionProcessSteps = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 10;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("AdmissionProcessPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant AdmissionProcessPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("AdmissionProcessPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("AdmissionProcessPage didChangeDependencies called");
  }

  void _loadInitialData() async {
    print("_loadInitialData called");
    await _loadAdmissionProcessFromCache();
    await _loadAdmissionProcessFromSupabase(initialLoad: true);
  }

  void _setupRealtime() {
    final admissionProcessTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_admissionprocess';
    _realtimeChannel = Supabase.instance.client
        .channel('admission_process_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: admissionProcessTableName,
      callback: (payload) async {
        if (!mounted) return;
        print("Realtime update received for admission process: ${payload.eventType}");
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newStepId = payload.newRecord['id'];
          final newStepResponse = await Supabase.instance.client
              .from(admissionProcessTableName)
              .select('*')
              .eq('id', newStepId)
              .single();
          if (mounted) {
            Map<String, dynamic> newStep = Map.from(newStepResponse);
            setState(() {
              _admissionProcessSteps.add(newStep);
              _admissionProcessSteps.sort((a, b) => 
                  (a['stepnumber'] ?? 0).compareTo(b['stepnumber'] ?? 0));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedStepId = payload.newRecord['id'];
          final updatedStepResponse = await Supabase.instance.client
              .from(admissionProcessTableName)
              .select('*')
              .eq('id', updatedStepId)
              .single();
          if (mounted) {
            final updatedStep = Map<String, dynamic>.from(updatedStepResponse);
            setState(() {
              _admissionProcessSteps = _admissionProcessSteps.map((step) {
                return step['id'] == updatedStep['id'] ? updatedStep : step;
              }).toList();
              _admissionProcessSteps.sort((a, b) => 
                  (a['stepnumber'] ?? 0).compareTo(b['stepnumber'] ?? 0));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedStepId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _admissionProcessSteps.removeWhere((step) => step['id'] == deletedStepId);
            });
          }
        }
      },
    ).subscribe();
  }

  Future<void> _loadAdmissionProcessFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'admission_process_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedData = prefs.getString(cacheKey);

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> admissionProcessSteps = decodedData.cast<Map<String, dynamic>>();

        if (mounted && admissionProcessSteps.isNotEmpty) {
          setState(() {
            _admissionProcessSteps = admissionProcessSteps;
            _admissionProcessSteps.sort((a, b) => 
                (a['stepnumber'] ?? 0).compareTo(b['stepnumber'] ?? 0));
          });
          print("Loaded ${admissionProcessSteps.length} admission process steps from cache");
        }
      }
    } catch (e) {
      print("Error loading admission process from cache: $e");
    }
  }

  Future<void> _loadAdmissionProcessFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadAdmissionProcessFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    try {
      final admissionProcessTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_admissionprocess';
      
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _admissionProcessSteps.length;
        endRange = _admissionProcessSteps.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(admissionProcessTableName)
          .select('*')
          .order('stepnumber', ascending: true)
          .range(startRange, endRange);

      if (_isDisposed) return;

      setState(() {
        if (initialLoad) {
          _admissionProcessSteps = List<Map<String, dynamic>>.from(response);
        } else {
          _admissionProcessSteps.addAll(List<Map<String, dynamic>>.from(response));
        }
        _admissionProcessSteps.sort((a, b) => 
            (a['stepnumber'] ?? 0).compareTo(b['stepnumber'] ?? 0));
        _isLoading = false;
        _isLoadingMore = false;
        _hasMore = response.length == _pageSize;
        if (!initialLoad) {
          _page++;
        }
      });

      // Cache the data
      _cacheAdmissionProcess(_admissionProcessSteps);

    } catch (e) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = e.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error loading admission process: $e";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<void> _cacheAdmissionProcess(List<Map<String, dynamic>> steps) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'admission_process_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(steps));
      print("Cached ${steps.length} admission process steps");
    } catch (e) {
      print("Error caching admission process: $e");
    }
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreSteps();
    }
  }

  Future<void> _loadMoreSteps() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more admission process steps...");
      await _loadAdmissionProcessFromSupabase(initialLoad: false);
    }
  }

  void _navigateToDetail(BuildContext context, Map<String, dynamic> step) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AdmissionProcessDetailPage(
            step: step,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("AdmissionProcessPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    print("AdmissionProcessPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Admission Process',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadAdmissionProcessFromSupabase(initialLoad: true);
              },
              child: _admissionProcessSteps.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No admission process steps found'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _admissionProcessSteps.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _admissionProcessSteps.length) {
                          final step = _admissionProcessSteps[index];
                          return VisibilityDetector(
                            key: Key('admission_step_${step['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              // No special image loading needed for admission process steps
                            },
                            child: _buildStepCard(step, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStepCard(
    Map<String, dynamic> step,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final int stepNumber = step['stepnumber'] ?? 0;
    final String stepName = step['stepname'] ?? 'Unknown Step';
    final String description = step['description'] ?? '';
    final String department = step['department'] ?? '';
    final String targetAudience = step['targetaudience'] ?? '';
    final String estimatedTime = step['estimatedtime'] ?? '';
    final String requiredMaterials = step['requiredmaterials'] ?? '';

    // Build additional info text
    List<String> additionalInfo = [];
    if (department.isNotEmpty) additionalInfo.add('Dept: $department');
    if (targetAudience.isNotEmpty) additionalInfo.add('For: $targetAudience');
    if (estimatedTime.isNotEmpty) additionalInfo.add('Time: $estimatedTime');
    if (requiredMaterials.isNotEmpty) additionalInfo.add('Materials: $requiredMaterials');

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, step),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Step number circle
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Text(
                  stepNumber.toString(),
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      stepName,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (description.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          description,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (additionalInfo.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          additionalInfo.join(' • '),
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}