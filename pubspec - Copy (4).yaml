name: OSchoolHarmony
description: A new Flutter project with an Instagram-like interface.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ">=2.17.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  video_player: '>=2.9.2 <3.0.0' # Or the latest version
  path_provider: '>=2.1.5 <3.0.0' # Or the latest version
  audio_video_progress_bar: ^2.0.1 # For audio player progress bar
  path: '>=1.9.0 <2.0.0' # Or the latest version
  sqflite: '>=2.4.1 <3.0.0' # Or the latest version
  sqflite_common_ffi: '>=2.3.4 <3.0.0' # Or the latest version
  sqlite3: '>=2.7.2 <3.0.0'
  shared_preferences: '>=2.3.4 <3.0.0' # Or the latest version
  math_expressions: '>=2.6.0 <3.0.0'  # You can check pub.dev for the latest version
  image_picker: '>=1.1.2 <2.0.0'  # Use the latest version available on pub.dev
  intl: '>=0.17.0 <1.0.0' # Or the latest version
  crypto: '>=3.0.6 <4.0.0'
  photo_view: '>=0.15.0 <1.0.0'
  flutter_tts: '>=3.8.5 <4.0.0' # Or the latest version
  #qr_code_scanner: ^1.0.1   # Or the latest version
  fl_chart: '>=0.66.2 <0.67.0' # Use the latest version
  pdf: ^3.11.3 # Add this line
  pdfx: '>=2.8.0 <3.0.0'
  file_picker: '>=6.2.1 <7.0.0'
  cached_network_image: '>=3.4.1 <4.0.0' # Use the latest version
  csv: '>=5.1.1 <6.0.0'
  flutter_pdfview: '>=1.4.0 <2.0.0'
  external_path: '>=2.0.1 <3.0.0'
  geolocator: '>=9.0.2 <10.0.0' # Use the latest version
  #ar_flutter_plugin: ^0.7.0 # Use the latest version
  permission_handler: '>=11.3.1 <12.0.0' # For permissions
  cupertino_icons: ^1.0.2
  font_awesome_flutter: 10.7.0
  flutter_colorpicker: '>=1.1.0 <2.0.0'
  url_launcher: '>=6.3.1 <7.0.0'
  # Firebase
  firebase_core:  '>=3.10.1 <4.0.0'
  firebase_core_web: '>=2.19.0 <3.0.0'
  firebase_messaging: '>=15.2.1 <16.0.0' # Add the Firebase Messaging dependency
  flutter_local_notifications: '>=18.0.1 <19.0.0'
  google_mobile_ads: '>=4.0.0 <5.0.0'  # Use the latest version
  timezone: ^0.9.0
  #webview_flutter: ^4.5.0
  #webview_flutter_platform_interface: ^2.10.0
  
  panorama: ^0.4.1 # Or the latest version
  motion_sensors: ^0.1.0
  #webview_flutter_web: ^0.2.3+4
  http: ^1.2.0
  html: ^0.15.4
  #webview_flutter_android: ^4.3.1
  flutter_downloader: ^1.11.5 # Updated version
  #printing: ^5.14.2
  #uuid: ^4.5.1

  supabase_flutter: '>=2.8.3 <3.0.0' # Use the latest version
  google_generative_ai: ^0.4.6
  syncfusion_flutter_pdf: ^28.2.6
  flip_card: ^0.7.0
  flutter_markdown: ^0.7.6+2
  universal_html: ^2.2.4
  speech_to_text: ^7.0.0
  app_links: ^6.3.3 # Or the latest version
  app_links_platform_interface: ^2.0.2
  app_links_web: ^1.0.4
  #google_sign_in: '>=6.2.2 <7.0.0'    # Make sure to use the latest version
  googleapis: ^13.2.0 # Or the latest version for Google APIs
  calendar_view: ^1.4.0 # Or the latest version for calendar_view
  table_calendar: ^3.0.9 # For calendar views in tertiary_calendar_page
  realtime_client: '>=2.4.1 <3.0.0'
  connectivity_plus: '>=6.1.2 <7.0.0' # For checking internet connectivity
  visibility_detector: '>=0.4.0 <1.0.0' # Add this line
  flutter_dotenv: '>=5.2.1 <6.0.0'
  provider: '>=6.1.2 <7.0.0'
  #ffmpeg_kit_flutter_full_gpl: ^6.0.3 # Or another variant of ffmpeg_kit_flutter based
  google_fonts: '>=6.2.1 <7.0.0' # Use the latest version
  flutter_map: '>=7.0.2 <8.0.0'
  latlong2: '>=0.9.1 <1.0.0'
  flutter_map_cancellable_tile_provider: '>=3.0.2 <4.0.0'
  js: '>=0.6.7 <1.0.0'
  flutter_svg: ^2.0.17 # ✅ Added flutter_svg with a recent version
  share_plus: ^7.2.1 # Added for sharing functionality

  flutter_math_fork: 0.7.3
  excel: ^4.0.6
  #google_mlkit_document_scanner: ^0.3.0
  google_mlkit_text_recognition: ^0.14.0
  collection: ^1.18.0
  youtube_explode_dart: ^2.3.9


  pdf_render: ^1.4.12
  archive: ^3.3.6
  xml: ^6.5.0
  image: ^4.3.0

  markdown_widget: ^2.3.2+6
  flutter_tex: ^4.0.9
  #markdown: ^7.3.0
  just_audio: ^0.9.36
  video_compress: ^3.1.4
  #flutter_windowmanager: ^0.2.0
  secure_application: 4.1.0
  encrypt: ^5.0.3
  flutter_windowmanager_plus: ^1.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  flutter_launcher_icons: "^0.13.1" # Add the latest version from pub.dev
  flutter_native_splash: "^2.4.3"  # Add the latest version from pub.dev

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/icon.png" # Replace with your icon path
  # min_sdk_android: 21 # Optional, for adaptive icons

flutter_native_splash:

  color: "#080808" # Customize your splash screen background color was #ffffff
  image: assets/splash/splash.png # Replace with your splash image path
  # android_12_background_color: "#ffffff" # Optional for Android 12+
  # android_12_splash_image: assets/splash/splash-android12.png # Optional for Android 12+

flutter:
  uses-material-design: true
  assets:
    - assets/ads/ # Add this line to include the entire ads directory
    - assets/library/
    - assets/placeholder_image.png
    - assets/google_logo.png
    - assets/fonts/Roboto-Regular.ttf
    - assets/fonts/Roboto-Bold.ttf

    - assets/word_template.docx

  fonts:
    - family: Bravura
      fonts:
        - asset: assets/fonts/Bravura.ttf
    - family: JetBrainsMono
      fonts:
        - asset: assets/fonts/JetBrainsMono-Regular.ttf
    - family: ISOCP
      fonts:
        - asset: assets/fonts/ISOCP-Regular.ttf  # Make sure filename matches exactly
    - family: Symbola
      fonts:
        - asset: assets/fonts/Symbola.ttf
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/NotoSansSymbols-Regular.ttf
          weight: 400
        - asset: assets/fonts/NotoSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/NotoSans-Italic.ttf
          style: italic
        - asset: assets/fonts/NotoSans-BoldItalic.ttf
          weight: 700
          style: italic
    - family: STIXTwoMath
      fonts:
        - asset: assets/fonts/STIXTwoMath-Regular.ttf
android: # Correct placement - top level
  useAndroidX: true
  minSdkVersion: 21
  targetSdkVersion: 33
  