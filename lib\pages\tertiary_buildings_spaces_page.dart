import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'buildings_page.dart';
import 'rooms_page.dart';
import 'room_equipment_page.dart';
import 'public_art_page.dart';

class TertiaryBuildingsSpacesPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryBuildingsSpacesPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryBuildingsSpacesPage> createState() => _TertiaryBuildingsSpacesPageState();
}

class _TertiaryBuildingsSpacesPageState extends State<TertiaryBuildingsSpacesPage> {
  // Buildings data
  List<Map<String, dynamic>> _buildings = [];
  bool _isLoadingBuildings = false;

  // Rooms data
  List<Map<String, dynamic>> _rooms = [];
  bool _isLoadingRooms = false;

  // Room equipment data
  List<Map<String, dynamic>> _roomEquipment = [];
  bool _isLoadingRoomEquipment = false;

  // Public art data
  List<Map<String, dynamic>> _publicArt = [];
  bool _isLoadingPublicArt = false;

  // Realtime channels
  late RealtimeChannel _buildingsChannel;
  late RealtimeChannel _roomsChannel;
  late RealtimeChannel _roomEquipmentChannel;
  late RealtimeChannel _publicArtChannel;

  @override
  void initState() {
    super.initState();
    _loadBuildingsFromCache();
    _loadRoomsFromCache();
    _loadRoomEquipmentFromCache();
    _loadPublicArtFromCache();

    _fetchBuildingsFromSupabase();
    _fetchRoomsFromSupabase();
    _fetchRoomEquipmentFromSupabase();
    _fetchPublicArtFromSupabase();

    _setupRealtimeListeners();
  }

  @override
  void dispose() {
    _buildingsChannel.unsubscribe();
    _roomsChannel.unsubscribe();
    _roomEquipmentChannel.unsubscribe();
    _publicArtChannel.unsubscribe();
    super.dispose();
  }

  // Cache methods
  Future<void> _loadBuildingsFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'building_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        setState(() {
          _buildings = decoded.cast<Map<String, dynamic>>();
          print('Loaded ${_buildings.length} buildings from cache');
        });
      }
    } catch (e) {
      print('Error loading buildings from cache: $e');
    }
  }

  Future<void> _loadRoomsFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'rooms_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        setState(() {
          _rooms = decoded.cast<Map<String, dynamic>>();
          print('Loaded ${_rooms.length} rooms from cache');
        });
      }
    } catch (e) {
      print('Error loading rooms from cache: $e');
    }
  }

  Future<void> _loadRoomEquipmentFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'roomequipment_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        setState(() {
          _roomEquipment = decoded.cast<Map<String, dynamic>>();
          print('Loaded ${_roomEquipment.length} room equipment items from cache');
        });
      }
    } catch (e) {
      print('Error loading room equipment from cache: $e');
    }
  }

  Future<void> _loadPublicArtFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'publicart_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        setState(() {
          _publicArt = decoded.cast<Map<String, dynamic>>();
          print('Loaded ${_publicArt.length} public art items from cache');
        });
      }
    } catch (e) {
      print('Error loading public art from cache: $e');
    }
  }

  // Save to cache methods
  Future<void> _saveBuildingsToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'building_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
      print('Saved ${data.length} buildings to cache');
    } catch (e) {
      print('Error saving buildings to cache: $e');
    }
  }

  Future<void> _saveRoomsToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'rooms_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
      print('Saved ${data.length} rooms to cache');
    } catch (e) {
      print('Error saving rooms to cache: $e');
    }
  }

  Future<void> _saveRoomEquipmentToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'roomequipment_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
      print('Saved ${data.length} room equipment items to cache');
    } catch (e) {
      print('Error saving room equipment to cache: $e');
    }
  }

  Future<void> _savePublicArtToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'publicart_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
      print('Saved ${data.length} public art items to cache');
    } catch (e) {
      print('Error saving public art to cache: $e');
    }
  }

  // Fetch from Supabase methods
  Future<void> _fetchBuildingsFromSupabase() async {
    if (_isLoadingBuildings) return;

    setState(() {
      _isLoadingBuildings = true;
    });

    try {
      final buildingsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_building';
      print('Fetching buildings from table: $buildingsTableName');

      final response = await Supabase.instance.client
          .from(buildingsTableName)
          .select('*')
          .order('fullname', ascending: true);

      final buildings = List<Map<String, dynamic>>.from(response);
      print('Fetched ${buildings.length} buildings from Supabase');

      // Save to cache
      await _saveBuildingsToCache(buildings);

      setState(() {
        _buildings = buildings;
        _isLoadingBuildings = false;
      });
    } catch (e) {
      print('Error fetching buildings from Supabase: $e');
      setState(() {
        _isLoadingBuildings = false;
      });
    }
  }

  Future<void> _fetchRoomsFromSupabase() async {
    if (_isLoadingRooms) return;

    setState(() {
      _isLoadingRooms = true;
    });

    try {
      final roomsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_rooms';
      print('Fetching rooms from table: $roomsTableName');

      final response = await Supabase.instance.client
          .from(roomsTableName)
          .select('*')
          .order('fullname', ascending: true);

      final rooms = List<Map<String, dynamic>>.from(response);
      print('Fetched ${rooms.length} rooms from Supabase');

      // Save to cache
      await _saveRoomsToCache(rooms);

      setState(() {
        _rooms = rooms;
        _isLoadingRooms = false;
      });
    } catch (e) {
      print('Error fetching rooms from Supabase: $e');
      setState(() {
        _isLoadingRooms = false;
      });
    }
  }

  Future<void> _fetchRoomEquipmentFromSupabase() async {
    if (_isLoadingRoomEquipment) return;

    setState(() {
      _isLoadingRoomEquipment = true;
    });

    try {
      final roomEquipmentTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_roomequipment';
      print('Fetching room equipment from table: $roomEquipmentTableName');

      final response = await Supabase.instance.client
          .from(roomEquipmentTableName)
          .select('*')
          .order('fullname', ascending: true);

      final roomEquipment = List<Map<String, dynamic>>.from(response);
      print('Fetched ${roomEquipment.length} room equipment items from Supabase');

      // Save to cache
      await _saveRoomEquipmentToCache(roomEquipment);

      setState(() {
        _roomEquipment = roomEquipment;
        _isLoadingRoomEquipment = false;
      });
    } catch (e) {
      print('Error fetching room equipment from Supabase: $e');
      setState(() {
        _isLoadingRoomEquipment = false;
      });
    }
  }

  Future<void> _fetchPublicArtFromSupabase() async {
    if (_isLoadingPublicArt) return;

    setState(() {
      _isLoadingPublicArt = true;
    });

    try {
      final publicArtTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_publicart';
      print('Fetching public art from table: $publicArtTableName');

      final response = await Supabase.instance.client
          .from(publicArtTableName)
          .select('*')
          .order('fullname', ascending: true);

      final publicArt = List<Map<String, dynamic>>.from(response);
      print('Fetched ${publicArt.length} public art items from Supabase');

      // Save to cache
      await _savePublicArtToCache(publicArt);

      setState(() {
        _publicArt = publicArt;
        _isLoadingPublicArt = false;
      });
    } catch (e) {
      print('Error fetching public art from Supabase: $e');
      setState(() {
        _isLoadingPublicArt = false;
      });
    }
  }

  // Setup realtime listeners
  void _setupRealtimeListeners() {
    _setupBuildingsRealtimeListener();
    _setupRoomsRealtimeListener();
    _setupRoomEquipmentRealtimeListener();
    _setupPublicArtRealtimeListener();
  }

  void _setupBuildingsRealtimeListener() {
    final buildingsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_building';
    _buildingsChannel = Supabase.instance.client
        .channel('buildings_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: buildingsTableName,
          callback: (payload) {
            print('Realtime update received for buildings');
            _fetchBuildingsFromSupabase();
          },
        )
        .subscribe();
  }

  void _setupRoomsRealtimeListener() {
    final roomsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_rooms';
    _roomsChannel = Supabase.instance.client
        .channel('rooms_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: roomsTableName,
          callback: (payload) {
            print('Realtime update received for rooms');
            _fetchRoomsFromSupabase();
          },
        )
        .subscribe();
  }

  void _setupRoomEquipmentRealtimeListener() {
    final roomEquipmentTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_roomequipment';
    _roomEquipmentChannel = Supabase.instance.client
        .channel('roomequipment_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: roomEquipmentTableName,
          callback: (payload) {
            print('Realtime update received for room equipment');
            _fetchRoomEquipmentFromSupabase();
          },
        )
        .subscribe();
  }

  void _setupPublicArtRealtimeListener() {
    final publicArtTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_publicart';
    _publicArtChannel = Supabase.instance.client
        .channel('publicart_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: publicArtTableName,
          callback: (payload) {
            print('Realtime update received for public art');
            _fetchPublicArtFromSupabase();
          },
        )
        .subscribe();
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDark = theme.brightness == Brightness.dark;
    return Card(
      key: Key('buildings_spaces_grid_item_$title'),
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          _navigateToPage(context, title);
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDark ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToPage(BuildContext context, String title) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    switch (title) {
      case 'Buildings':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BuildingsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: currentIsDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedBuildings: _buildings,
              isFromDetailPage: widget.isFromDetailPage,
            ),
          ),
        );
        break;
      case 'Rooms':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => RoomsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: currentIsDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedRooms: _rooms,
              isFromDetailPage: widget.isFromDetailPage,
            ),
          ),
        );
        break;
      case 'Room Equipment':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => RoomEquipmentPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: currentIsDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedRoomEquipment: _roomEquipment,
              isFromDetailPage: widget.isFromDetailPage,
            ),
          ),
        );
        break;
      case 'Public Art':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PublicArtPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: currentIsDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedPublicArt: _publicArt,
              isFromDetailPage: widget.isFromDetailPage,
            ),
          ),
        );
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final bool isFromDetailPage = widget.isFromDetailPage;

    // Define grid items with their title and icon (removed Room Assignments and Emergency Equipment)
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'Buildings', 'icon': Icons.apartment},
      {'title': 'Rooms', 'icon': Icons.meeting_room},
      {'title': 'Room Equipment', 'icon': Icons.devices_other},
      {'title': 'Public Art', 'icon': Icons.palette},
    ];

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Buildings & Spaces',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 4;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 2;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: gridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                        isFromDetailPage,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}