// connectivity_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'login_page.dart';

class Connectivity {
  final int id;
  final String fullname;
  final String about;

  Connectivity({
    required this.id,
    required this.fullname,
    required this.about,
  });

  factory Connectivity.fromJson(Map<String, dynamic> json) {
    return Connectivity(
      id: json['id'] ?? 0,
      fullname: json['fullname'] ?? 'Unnamed Connectivity',
      about: json['about'] ?? 'No description available',
    );
  }

  // Helper method to get wifi icon for all connectivity items
  IconData getIcon() {
    return Icons.wifi;
  }
}

class ConnectivityDetailPage extends StatefulWidget {
  final Map<String, dynamic> connectivity;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String institutionName;

  const ConnectivityDetailPage({
    Key? key,
    required this.connectivity,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.institutionName,
  }) : super(key: key);

  @override
  State<ConnectivityDetailPage> createState() => _ConnectivityDetailPageState();
}

class _ConnectivityDetailPageState extends State<ConnectivityDetailPage> {
  late Connectivity connectivityObj;

  @override
  void initState() {
    super.initState();
    connectivityObj = Connectivity.fromJson(widget.connectivity);
  }

  void _copyToClipboard(String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$label copied to clipboard'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bool currentIsDarkMode = widget.isDarkMode;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          connectivityObj.fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header image section (placeholder for consistency)
            Container(
              height: 200,
              color: theme.colorScheme.surfaceVariant,
              child: Center(
                child: Icon(
                  connectivityObj.getIcon(),
                  size: 80,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header section with avatar and title
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CircleAvatar(
                                radius: 30,
                                backgroundColor: currentIsDarkMode
                                    ? Colors.white.withOpacity(0.1)
                                    : Colors.black.withOpacity(0.1),
                                child: Icon(
                                  connectivityObj.getIcon(),
                                  size: 30,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      connectivityObj.fullname,
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 4),
                                      child: Text(
                                        _getServiceCategory(connectivityObj.fullname),
                                        style: TextStyle(
                                          color: theme.colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          
                          // Service details
                          _buildDetailRow(
                            theme, 
                            Icons.business_outlined, 
                            'Institution', 
                            widget.institutionName, 
                            canCopy: true
                          ),
                          
                          // About section
                          if (connectivityObj.about.isNotEmpty && 
                              connectivityObj.about != 'No description available') ...[
                            const SizedBox(height: 16),
                            _buildSectionTitle(theme, Icons.info_outline, 'About'),
                            const SizedBox(height: 8),
                            Text(
                              connectivityObj.about,
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                          
                          // Service type section
                          const SizedBox(height: 24),
                          _buildSectionTitle(theme, Icons.wifi, 'Service Type'),
                          const SizedBox(height: 8),
                          _buildDetailRow(
                            theme,
                            connectivityObj.getIcon(),
                            'Category',
                            _getServiceCategory(connectivityObj.fullname),
                            canCopy: false,
                          ),
                          
                          // Additional service info
                          const SizedBox(height: 16),
                          _buildDetailRow(
                            theme,
                            Icons.info_outline,
                            'Service Name',
                            connectivityObj.fullname,
                            canCopy: true,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: Icon(
                  Icons.home_outlined,
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: () {
                  Navigator.of(context).popUntil((route) => route.isFirst);
                },
                tooltip: 'Home',
              ),
              IconButton(
                icon: Icon(
                  currentIsDarkMode
                      ? Icons.light_mode_outlined
                      : Icons.dark_mode_outlined,
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: widget.toggleTheme,
                tooltip: 'Toggle theme',
              ),
              IconButton(
                icon: Icon(
                  Icons.person_outline,
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LoginPage(
                        isDarkMode: currentIsDarkMode,
                        toggleTheme: widget.toggleTheme,
                      ),
                    ),
                  );
                },
                tooltip: 'Login',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    ThemeData theme,
    IconData icon,
    String title,
    dynamic value, {
    bool canCopy = false,
    VoidCallback? onTap,
  }) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    final bool isClickable = onTap != null;

    Widget content = Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: theme.colorScheme.onSurface,
          size: 20,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      value.toString(),
                      style: TextStyle(
                        color: isClickable 
                            ? theme.colorScheme.primary 
                            : theme.colorScheme.onSurfaceVariant,
                        decoration: isClickable 
                            ? TextDecoration.underline 
                            : TextDecoration.none,
                      ),
                    ),
                  ),
                  if (canCopy)
                    SizedBox(
                      width: 30,
                      height: 30,
                      child: IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _copyToClipboard(value.toString(), title),
                        tooltip: 'Copy $title',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ],
    );

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: isClickable
          ? InkWell(
              onTap: onTap,
              child: content,
            )
          : content,
    );
  }

  Widget _buildSectionTitle(ThemeData theme, IconData icon, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, color: theme.colorScheme.onSurface, size: 20),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  String _getServiceCategory(String fullname) {
    final String name = fullname.toLowerCase();
    if (name.contains('wi-fi') || name.contains('wifi') || name.contains('wireless')) {
      return 'Wireless Network';
    } else if (name.contains('mobile') || name.contains('cellular') || name.contains('network')) {
      return 'Mobile/Cellular Network';
    } else if (name.contains('computer') || name.contains('lab')) {
      return 'Computer Services';
    } else if (name.contains('print') || name.contains('printing')) {
      return 'Printing Services';
    } else if (name.contains('support') || name.contains('help') || name.contains('it')) {
      return 'IT Support';
    } else if (name.contains('ethernet') || name.contains('lan')) {
      return 'Wired Network';
    } else if (name.contains('vpn')) {
      return 'VPN Services';
    } else if (name.contains('email') || name.contains('mail')) {
      return 'Email Services';
    } else {
      return 'Network Services';
    }
  }
}