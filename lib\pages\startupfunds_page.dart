// startupfunds_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'startupfund_detail_page.dart';

class StartupFundsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedStartupFunds;
  final bool isFromDetailPage;

  const StartupFundsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedStartupFunds,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<StartupFundsPage> createState() => _StartupFundsPageState();
}

class _StartupFundsPageState extends State<StartupFundsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('startupfunds_list');
  List<Map<String, dynamic>> _startupFunds = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("StartupFundsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant StartupFundsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("StartupFundsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("StartupFundsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedStartupFunds != null && widget.preloadedStartupFunds!.isNotEmpty) {
      print("Preloaded startup funds found, using them.");
      setState(() {
        _startupFunds = List<Map<String, dynamic>>.from(widget.preloadedStartupFunds!);
        _startupFunds.forEach((fund) {
          fund['_isImageLoading'] = false;
        });
        _startupFunds.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded startup funds or empty list, loading from database.");
      await _loadStartupFundsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final startupFundsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_startupfunds';
    
    try {
      final response = await Supabase.instance.client
          .from(startupFundsTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_startupFunds.length, _startupFunds.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadStartupFundsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadStartupFundsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final startupFundsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_startupfunds';

    try {
      int startRange = initialLoad ? 0 : _startupFunds.length;
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(startupFundsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedStartupFunds =
          await _updateStartupFundImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _startupFunds = updatedStartupFunds;
          } else {
            _startupFunds.addAll(updatedStartupFunds);
          }
          _startupFunds.forEach((fund) {
            fund['_isImageLoading'] = false;
          });
          _startupFunds.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
      _cacheStartupFunds(_startupFunds);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching startup funds: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateStartupFundImageUrls(
      List<Map<String, dynamic>> startupFunds) async {
    List<Future<void>> futures = [];
    for (final fund in startupFunds) {
      if (fund['image_url'] == null ||
          fund['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(fund));
      }
    }
    await Future.wait(futures);
    return startupFunds;
  }
  
  void _setupRealtime() {
    final startupFundsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_startupfunds';
    _realtimeChannel = Supabase.instance.client
        .channel('startupfunds')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: startupFundsTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newFundId = payload.newRecord['id'];
          final newFundResponse = await Supabase.instance.client
              .from(startupFundsTableName)
              .select('*')
              .eq('id', newFundId)
              .single();
          if (mounted) {
            Map<String, dynamic> newFund = Map.from(newFundResponse);
            final updatedFund = await _updateStartupFundImageUrls([newFund]);
            setState(() {
              _startupFunds.add(updatedFund.first);
              updatedFund.first['_isImageLoading'] = false;
              _startupFunds.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedFundId = payload.newRecord['id'];
          final updatedFundResponse = await Supabase.instance.client
              .from(startupFundsTableName)
              .select('*')
              .eq('id', updatedFundId)
              .single();
          if (mounted) {
            final updatedFund = Map<String, dynamic>.from(updatedFundResponse);
            setState(() {
              _startupFunds = _startupFunds.map((fund) {
                return fund['id'] == updatedFund['id'] ? updatedFund : fund;
              }).toList();
              _startupFunds.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedFundId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _startupFunds.removeWhere((fund) => fund['id'] == deletedFundId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("StartupFundsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreStartupFunds();
    }
  }

  Future<void> _loadMoreStartupFunds() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more startup funds...");
      await _loadStartupFundsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _cacheStartupFunds(List<Map<String, dynamic>> startupFunds) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String startupFundsJson = jsonEncode(startupFunds);
      await prefs.setString(
          'startupfunds_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          startupFundsJson);
    } catch (e) {
      print('Error caching startup funds: $e');
    }
  }
  
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> fund) async {
    if (fund['_isImageLoading'] == true) return;
    if (fund['image_url'] != null && fund['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => fund['_isImageLoading'] = true);

    final fullname = fund['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeStartupFundBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/startupfunds';

    try {
      final file = await Supabase.instance.client.storage.from(collegeStartupFundBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeStartupFundBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        fund['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        fund['_isImageLoading'] = false;
      });
    } else {
      fund['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> fund) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StartupFundDetailPage(
            startupFund: fund,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("StartupFundsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Startup Funds',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadStartupFundsFromSupabase(initialLoad: true);
              },
              child: _startupFunds.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No startup funds available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _startupFunds.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _startupFunds.length) {
                          final fund = _startupFunds[index];
                          return VisibilityDetector(
                            key: Key('fund_${fund['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (fund['image_url'] == null ||
                                      fund['image_url'] == 'assets/placeholder_image.png') &&
                                  !fund['_isImageLoading']) {
                                _fetchImageUrl(fund);
                              }
                            },
                            child: _buildStartupFundCard(fund, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStartupFundCard(
    Map<String, dynamic> fund,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = fund['fullname'] ?? 'Unknown';
    final String about = fund['about'] ?? '';
    final String imageUrl = fund['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, fund),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.monetization_on,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.monetization_on,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.monetization_on,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}