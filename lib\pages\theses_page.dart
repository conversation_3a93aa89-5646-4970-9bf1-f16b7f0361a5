// theses_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'thesis_detail_page.dart';

class ThesesPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedTheses;
  final bool isFromDetailPage;

  const ThesesPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedTheses,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<ThesesPage> createState() => _ThesesPageState();
}

class _ThesesPageState extends State<ThesesPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('theses_list');
  List<Map<String, dynamic>> _theses = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  String? _selectedYear;
  String? _selectedMajor;
  List<String> _availableYears = [];
  List<String> _availableMajors = [];

  @override
  void initState() {
    super.initState();
    print("ThesesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant ThesesPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.collegeNameForTable != widget.collegeNameForTable) {
      _loadInitialData();
    }
    print("ThesesPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("ThesesPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    await _fetchFilterOptions();
    if (widget.preloadedTheses != null && widget.preloadedTheses!.isNotEmpty) {
      print("Preloaded theses found, using them.");
      setState(() {
        _theses = List<Map<String, dynamic>>.from(widget.preloadedTheses!);
        _sortTheses();
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded theses or empty list, loading from database.");
      await _loadThesesFromSupabase(initialLoad: true);
    }
  }

  Future<void> _fetchFilterOptions() async {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_theses';
    try {
      final yearsResponse = await Supabase.instance.client.from(tableName).select('year');
      final majorsResponse = await Supabase.instance.client.from(tableName).select('career');

      if (mounted) {
        setState(() {
          _availableYears = (yearsResponse as List)
              .map((e) => e['year'].toString())
              .where((y) => y != 'null' && y.isNotEmpty)
              .toSet()
              .toList()..sort((a,b) => b.compareTo(a));

          _availableMajors = (majorsResponse as List)
              .map((e) => e['career'].toString())
              .where((d) => d != 'null' && d.isNotEmpty)
              .toSet()
              .toList()..sort();
        });
      }
    } catch (e) {
      print('Error fetching filter options: $e');
    }
  }

  Future<void> _checkForMoreData() async {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_theses';
    
    try {
      var query = Supabase.instance.client.from(tableName).select('id');
      if (_selectedYear != null) query = query.eq('year', int.parse(_selectedYear!));
      if (_selectedMajor != null) query = query.eq('career', _selectedMajor!);
      
      final response = await query
          .order('year', ascending: false)
          .range(_theses.length, _theses.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadThesesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadThesesFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_theses';

    try {
      int startRange = initialLoad ? 0 : _theses.length;
      int endRange = startRange + _pageSize - 1;

      var query = Supabase.instance.client.from(tableName).select('*');
      if (_selectedYear != null) query = query.eq('year', int.parse(_selectedYear!));
      if (_selectedMajor != null) query = query.eq('career', _selectedMajor!);

      final response = await query
          .order('year', ascending: false)
          .range(startRange, endRange);

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _theses = List<Map<String, dynamic>>.from(response);
          } else {
            _theses.addAll(List<Map<String, dynamic>>.from(response));
          }
          _sortTheses();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching theses: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_theses';
    _realtimeChannel = Supabase.instance.client
        .channel('theses_realtime')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: tableName,
      callback: (payload) async {
        if (!mounted) return;
        await _loadThesesFromSupabase(initialLoad: true);
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("ThesesPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreTheses();
    }
  }

  Future<void> _loadMoreTheses() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more theses...");
      await _loadThesesFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _sortTheses() {
    _theses.sort((a, b) {
      return (b['year'] ?? 0).compareTo(a['year'] ?? 0);
    });
  }

  void _showOfflineSnackbar() {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _navigateToDetail(BuildContext context, Map<String, dynamic> thesis) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ThesisDetailPage(
            thesis: thesis,
            collegeNameForTable: widget.collegeNameForTable,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  void _showFilterDialog() {
    String? tempYear = _selectedYear;
    String? tempMajor = _selectedMajor;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Filter Theses'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DropdownButtonFormField<String>(
                      value: tempYear,
                      hint: const Text('Select Year'),
                      isExpanded: true,
                      onChanged: (value) => setDialogState(() => tempYear = value),
                      items: _availableYears.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(value: value, child: Text(value));
                      }).toList(),
                      decoration: const InputDecoration(labelText: 'Year'),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: tempMajor,
                      hint: const Text('Select Major'),
                      isExpanded: true,
                      onChanged: (value) => setDialogState(() => tempMajor = value),
                      items: _availableMajors.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(value: value, child: Text(value));
                      }).toList(),
                      decoration: const InputDecoration(labelText: 'Major'),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  child: const Text('Clear'),
                  onPressed: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedYear = null;
                      _selectedMajor = null;
                      _page = 0;
                      _hasMore = true;
                    });
                    _loadThesesFromSupabase(initialLoad: true);
                  },
                ),
                TextButton(
                  child: const Text('Apply'),
                  onPressed: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedYear = tempYear;
                      _selectedMajor = tempMajor;
                      _page = 0;
                      _hasMore = true;
                    });
                    _loadThesesFromSupabase(initialLoad: true);
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    print("ThesesPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Theses & Dissertations',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.filter_list, color: theme.colorScheme.onSurface),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadThesesFromSupabase(initialLoad: true);
              },
              child: _theses.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No theses available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _theses.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _theses.length) {
                          final thesis = _theses[index];
                          return _buildThesisCard(thesis, theme, currentIsDarkMode);
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildThesisCard(
    Map<String, dynamic> thesis,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = thesis['fullname'] ?? 'Unknown';
    final String author = thesis['author'] ?? 'N/A';
    final String major = thesis['career'] ?? 'N/A';
    final String year = thesis['year']?.toString() ?? 'N/A';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, thesis),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.school,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (author != 'N/A')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          author,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (major != 'N/A' || year != 'N/A')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          '$major • $year',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}