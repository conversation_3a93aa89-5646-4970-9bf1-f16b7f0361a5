import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'login_page.dart';
import 'package:visibility_detector/visibility_detector.dart';

class TimelineEvent {
  final int id;
  final String fullname;
  final int year;
  final String about;
  final IconData icon;

  TimelineEvent({
    required this.id,
    required this.fullname,
    required this.year,
    required this.about,
    required this.icon,
  });

  factory TimelineEvent.fromJson(Map<String, dynamic> json) {
    return TimelineEvent(
      id: json['id'] ?? 0,
      fullname: json['fullname'] ?? 'Unknown Event',
      year: json['year'] ?? 0,
      about: json['about'] ?? 'No description available',
      icon: Icons.timeline, // Single timeline icon for all events
    );
  }
}

class TertiaryTimelinePage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryTimelinePage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryTimelinePage> createState() => _TertiaryTimelinePageState();
}

class _TertiaryTimelinePageState extends State<TertiaryTimelinePage> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<TimelineEvent> _events = [];
  List<TimelineEvent> _filteredEvents = [];
  TextEditingController _searchController = TextEditingController();
  RangeValues _yearRange = const RangeValues(1900, 2030);
  int _minYear = 1900;
  int _maxYear = 2030;
  bool _isAscending = true;

  @override
  void initState() {
    super.initState();
    _fetchTimelineEvents();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchTimelineEvents() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_historicaltimeline';

      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('year', ascending: true);

      final List<TimelineEvent> events = List<Map<String, dynamic>>.from(response)
          .map((json) => TimelineEvent.fromJson(json))
          .toList();

      // Find min and max years for the range slider
      if (events.isNotEmpty) {
        _minYear = events.map((e) => e.year).reduce((a, b) => a < b ? a : b);
        _maxYear = events.map((e) => e.year).reduce((a, b) => a > b ? a : b);
        _yearRange = RangeValues(_minYear.toDouble(), _maxYear.toDouble());
      }

      setState(() {
        _events = events;
        _filteredEvents = List.from(events);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading timeline events: $e';
      });
      print('Error fetching timeline events: $e');
    }
  }

  void _filterEvents() {
    final int minYear = _yearRange.start.round();
    final int maxYear = _yearRange.end.round();

    setState(() {
      _filteredEvents = _events.where((event) {
        // Filter by year range
        if (event.year < minYear || event.year > maxYear) {
          return false;
        }

        return true;
      }).toList();

      // Sort by year
      _filteredEvents.sort((a, b) => _isAscending
          ? a.year.compareTo(b.year)
          : b.year.compareTo(a.year));
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Historical Timeline',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _isAscending ? Icons.arrow_upward : Icons.arrow_downward,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              setState(() {
                _isAscending = !_isAscending;
                _filterEvents();
              });
            },
            tooltip: _isAscending ? 'Oldest to Newest' : 'Newest to Oldest',
          ),
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _fetchTimelineEvents,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Year range slider
                if (_events.isNotEmpty) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Year Range:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        '${_yearRange.start.round()} - ${_yearRange.end.round()}',
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: currentIsDarkMode ? Colors.white : Colors.black,
                      inactiveTrackColor: currentIsDarkMode ? Colors.white.withOpacity(0.3) : Colors.black.withOpacity(0.3),
                      thumbColor: currentIsDarkMode ? Colors.white : Colors.black,
                      overlayColor: (currentIsDarkMode ? Colors.white : Colors.black).withOpacity(0.1),
                      valueIndicatorColor: currentIsDarkMode ? Colors.white : Colors.black,
                      valueIndicatorTextStyle: TextStyle(
                        color: currentIsDarkMode ? Colors.black : Colors.white,
                      ),
                    ),
                    child: RangeSlider(
                      values: _yearRange,
                      min: _minYear.toDouble(),
                      max: _maxYear.toDouble(),
                      divisions: (_maxYear - _minYear) > 100 ? 100 : (_maxYear - _minYear),
                      labels: RangeLabels(
                        _yearRange.start.round().toString(),
                        _yearRange.end.round().toString(),
                      ),
                      onChanged: (RangeValues values) {
                        setState(() {
                          _yearRange = values;
                          _filterEvents();
                        });
                      },
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Timeline events list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error_outline, size: 48, color: Colors.red),
                            const SizedBox(height: 16),
                            Text(
                              _errorMessage,
                              style: TextStyle(color: theme.colorScheme.error),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _fetchTimelineEvents,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _filteredEvents.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.timeline,
                                  size: 64,
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _events.isEmpty
                                      ? 'No timeline events available'
                                      : 'No events match your filters',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _filteredEvents.length,
                            padding: const EdgeInsets.all(16),
                            itemBuilder: (context, index) {
                              final event = _filteredEvents[index];
                              final isFirst = index == 0;
                              final isLast = index == _filteredEvents.length - 1;

                              return IntrinsicHeight(
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 80,
                                      child: Text(
                                        event.year.toString(),
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: theme.colorScheme.secondary,
                                        ),
                                      ),
                                    ),
                                    Column(
                                      children: [
                                        SizedBox(
                                          height: 12,
                                          child: Center(
                                            child: Container(
                                              width: 2,
                                              color: isFirst ? Colors.transparent : theme.colorScheme.secondary,
                                            ),
                                          ),
                                        ),
                                        Container(
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: theme.colorScheme.secondary.withOpacity(1.0),
                                          ),
                                          padding: const EdgeInsets.all(8),
                                          child: Icon(
                                            event.icon,
                                            color: currentIsDarkMode ? Colors.black : Colors.white,
                                            size: 24,
                                          ),
                                        ),
                                        SizedBox(
                                          height: 12,
                                          child: Center(
                                            child: Container(
                                              width: 2,
                                              color: isLast ? Colors.transparent : theme.colorScheme.secondary,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Card(
                                        color: theme.colorScheme.surface,
                                        surfaceTintColor: Colors.transparent,
                                        child: Padding(
                                          padding: const EdgeInsets.all(16),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                event.fullname,
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                  color: theme.colorScheme.onSurface,
                                                ),
                                              ),
                                              const SizedBox(height: 8),
                                              Text(
                                                event.about,
                                                style: TextStyle(
                                                  color: theme.colorScheme.onSurface.withOpacity(0.8),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}