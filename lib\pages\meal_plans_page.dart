import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'meal_plan_detail_page.dart';
import 'login_page.dart';

class MealPlansPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedMealPlans;
  final bool isFromDetailPage;

  const MealPlansPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedMealPlans,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _MealPlansPageState createState() => _MealPlansPageState();
}

class _MealPlansPageState extends State<MealPlansPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('meal_plans_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _mealPlans = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 15;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("MealPlansPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedMealPlans != null &&
        widget.preloadedMealPlans!.isNotEmpty) {
      print("Preloaded meal plans found, using them.");
      setState(() {
        _mealPlans = List<Map<String, dynamic>>.from(widget.preloadedMealPlans!);
        _sortMealPlans();
        _hasMore = widget.preloadedMealPlans!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      await _checkForMoreData();
    } else {
      print("No preloaded meal plans or empty list, loading from database.");
      await _loadMealPlansFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final mealPlansTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_mealplans';
    try {
      final response = await Supabase.instance.client
          .from(mealPlansTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_mealPlans.length, _mealPlans.length + _pageSize - 1);

      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadMealPlansFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadMealPlansFromSupabase called - initialLoad: $initialLoad, current page: $_page");

    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final mealPlansTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_mealplans';

    try {
      int startRange;
      if (initialLoad) {
        startRange = 0;
        _page = 0;
      } else {
        startRange = _mealPlans.length;
      }
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(mealPlansTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final newMealPlans = List<Map<String, dynamic>>.from(response);

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _mealPlans = newMealPlans;
          } else {
            _mealPlans.addAll(newMealPlans);
          }
          _sortMealPlans();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = newMealPlans.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching meal plans: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final mealPlansTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_mealplans';
    _realtimeChannel =
        Supabase.instance.client.channel('meal_plans').onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: mealPlansTableName,
      callback: (payload) async {
        if (!mounted) return;

        if (payload.eventType == PostgresChangeEvent.insert) {
          final newId = payload.newRecord['id'];
          final newResponse = await Supabase.instance.client
              .from(mealPlansTableName)
              .select('*')
              .eq('id', newId)
              .single();
          if (mounted) {
            setState(() {
              _mealPlans.add(Map.from(newResponse));
              _sortMealPlans();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedId = payload.newRecord['id'];
          final updatedResponse = await Supabase.instance.client
              .from(mealPlansTableName)
              .select('*')
              .eq('id', updatedId)
              .single();
          if (mounted) {
            final updatedMealPlan = Map<String, dynamic>.from(updatedResponse);
            setState(() {
              _mealPlans = _mealPlans.map((mp) {
                return mp['id'] == updatedMealPlan['id'] ? updatedMealPlan : mp;
              }).toList();
              _sortMealPlans();
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _mealPlans.removeWhere((mp) => mp['id'] == deletedId);
            });
          }
        }
      },
    ).subscribe();
  }

  void _sortMealPlans() {
    _mealPlans.sort((a, b) =>
        (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("MealPlansPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreMealPlans();
    }
  }

  Future<void> _loadMoreMealPlans() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more meal plans...");
      await _loadMealPlansFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> mealPlan) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MealPlanDetailPage(
            mealPlan: mealPlan,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Meal Plans',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadMealPlansFromSupabase(initialLoad: true);
              },
              child: _mealPlans.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No meal plans available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _mealPlans.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _mealPlans.length) {
                          final mealPlan = _mealPlans[index];
                          return VisibilityDetector(
                            key: Key('meal_plan_${mealPlan['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {},
                            child: _buildMealPlanCard(
                                mealPlan, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(Icons.home_outlined,
                      color: theme.colorScheme.onSurface),
                  onPressed: () =>
                      Navigator.of(context).popUntil((route) => route.isFirst),
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(Icons.person_outline,
                      color: theme.colorScheme.onSurface),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMealPlanCard(
    Map<String, dynamic> mealPlan,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = mealPlan['fullname'] ?? 'Unnamed Meal Plan';
    final String about = mealPlan['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, mealPlan),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.restaurant_menu_outlined,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}