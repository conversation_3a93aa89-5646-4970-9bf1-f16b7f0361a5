import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io';

import 'login_page.dart';
import 'shuttle_stops_detail_page.dart';

class ShuttleStopsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedShuttleStops;
  final bool isFromDetailPage;

  const ShuttleStopsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedShuttleStops,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _ShuttleStopsPageState createState() => _ShuttleStopsPageState();
}

class _ShuttleStopsPageState extends State<ShuttleStopsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('shuttle_stops_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _shuttleStops = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 10;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("ShuttleStopsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant ShuttleStopsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("ShuttleStopsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("ShuttleStopsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedShuttleStops != null && widget.preloadedShuttleStops!.isNotEmpty) {
      print("Preloaded shuttle stops found, using them.");
      setState(() {
        _shuttleStops = List<Map<String, dynamic>>.from(widget.preloadedShuttleStops!);
        _shuttleStops.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedShuttleStops!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      // Check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded shuttle stops or empty list, loading from database.");
      await _loadShuttleStopsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final shuttleStopsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_shuttlestops';
    
    try {
      final response = await Supabase.instance.client
          .from(shuttleStopsTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_shuttleStops.length, _shuttleStops.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
      // If the dedicated table doesn't exist, try campus shuttle table
      await _checkForMoreDataFromCampusShuttle();
    }
  }

  Future<void> _checkForMoreDataFromCampusShuttle() async {
    final campusShuttleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_campusshuttle';
    
    try {
      final response = await Supabase.instance.client
          .from(campusShuttleTableName)
          .select('id')
          .not('latitude', 'is', null)
          .not('longitude', 'is', null)
          .order('fullname', ascending: true)
          .range(_shuttleStops.length, _shuttleStops.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data from campus shuttle: $error");
      setState(() {
        _hasMore = false;
      });
    }
  }

  Future<void> _loadShuttleStopsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadShuttleStopsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _shuttleStops.length;
        endRange = _shuttleStops.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      List<Map<String, dynamic>> response = [];
      
      // Try dedicated shuttle stops table first
      final shuttleStopsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_shuttlestops';
      final tableExists = await _checkIfTableExists(shuttleStopsTableName);
      
      if (tableExists) {
        final data = await Supabase.instance.client
            .from(shuttleStopsTableName)
            .select('*')
            .order('fullname', ascending: true)
            .range(startRange, endRange);
        response = List<Map<String, dynamic>>.from(data);
      } else {
        // Fall back to campus shuttle table
        final campusShuttleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_campusshuttle';
        final data = await Supabase.instance.client
            .from(campusShuttleTableName)
            .select('*')
            .not('latitude', 'is', null)
            .not('longitude', 'is', null)
            .order('fullname', ascending: true)
            .range(startRange, endRange);
        response = List<Map<String, dynamic>>.from(data);
      }

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _shuttleStops = response;
          } else {
            _shuttleStops.addAll(response);
          }
          _shuttleStops.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching shuttle stops: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<bool> _checkIfTableExists(String tableName) async {
    try {
      await Supabase.instance.client
          .from(tableName)
          .select('id')
          .limit(1);
      return true;
    } catch (e) {
      print("Table $tableName might not exist: $e");
      return false;
    }
  }

  void _setupRealtime() {
    final shuttleStopsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_shuttlestops';
    _realtimeChannel = Supabase.instance.client
        .channel('shuttle_stops_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: shuttleStopsTableName,
      callback: (payload) async {
        if (!mounted) return;
        print("Realtime update received for shuttle stops: ${payload.eventType}");
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newStopId = payload.newRecord['id'];
          final newStopResponse = await Supabase.instance.client
              .from(shuttleStopsTableName)
              .select('*')
              .eq('id', newStopId)
              .single();
          if (mounted) {
            final newStop = Map<String, dynamic>.from(newStopResponse);
            setState(() {
              _shuttleStops.add(newStop);
              _shuttleStops.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedStopId = payload.newRecord['id'];
          final updatedStopResponse = await Supabase.instance.client
              .from(shuttleStopsTableName)
              .select('*')
              .eq('id', updatedStopId)
              .single();
          if (mounted) {
            final updatedStop = Map<String, dynamic>.from(updatedStopResponse);
            setState(() {
              _shuttleStops = _shuttleStops.map((stop) {
                return stop['id'] == updatedStop['id'] ? updatedStop : stop;
              }).toList();
              _shuttleStops.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedStopId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _shuttleStops.removeWhere((stop) => stop['id'] == deletedStopId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("ShuttleStopsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreShuttleStops();
    }
  }

  Future<void> _loadMoreShuttleStops() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more shuttle stops...");
      await _loadShuttleStopsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> shuttleStop) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ShuttleStopsDetailPage(
            shuttleStop: shuttleStop,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForTable: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("ShuttleStopsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Bus/Shuttle Stops',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadShuttleStopsFromSupabase(initialLoad: true);
              },
              child: _shuttleStops.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No shuttle stops found'),
                            ),
                          ),
                        );
                      },
                    )
                  : _buildListView(theme, currentIsDarkMode),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildListView(ThemeData theme, bool isDarkMode) {
    return ListView.builder(
      key: _listKey,
      controller: _scrollController,
      shrinkWrap: true,
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.all(16),
      itemCount: _shuttleStops.length + (_hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < _shuttleStops.length) {
          final stop = _shuttleStops[index];
          return VisibilityDetector(
            key: Key('shuttle_stop_${stop['id']}'),
            onVisibilityChanged: (VisibilityInfo info) {
              // Visibility detection for future enhancements
            },
            child: _buildShuttleStopCard(stop, theme, isDarkMode),
          );
        } else if (_hasMore) {
          return const Center(
              child: Padding(
                  padding: EdgeInsets.all(16),
                  child: CircularProgressIndicator()));
        } else {
          return Container();
        }
      },
    );
  }

  Widget _buildShuttleStopCard(
    Map<String, dynamic> stop,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = stop['fullname'] ?? 'Unknown';
    final String about = stop['about'] ?? '';
    final String location = stop['location'] ?? '';
    final String building = stop['building'] ?? '';
    final String room = stop['room'] ?? '';

    String locationText = '';
    if (location.isNotEmpty) {
      locationText = location;
    } else if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, stop),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.directions_bus,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}