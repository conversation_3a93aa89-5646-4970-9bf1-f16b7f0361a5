import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'login_page.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'clinics_hospitals_page.dart';
import 'emergency_equipment_page.dart';

class ClinicOrHospital {
  final int id;
  final String fullname;
  final String about;
  final String phone;
  final String email;
  final String fax;
  final String whatsapp;
  final double? latitude;
  final double? longitude;
  final String daysnhours;
  final String building;
  final String room;
  final String address;
  final String postaladdress;
  final String payment;
  final String mission;
  final String vision;
  final String corevalues;
  final String acceptableclients;
  final String acceptablehealthinsurance;
  final String admissions;
  final String visitors;
  final String visitinghours;
  final String discharge;
  final String parking;

  ClinicOrHospital({
    required this.id,
    required this.fullname,
    required this.about,
    required this.phone,
    required this.email,
    required this.fax,
    required this.whatsapp,
    this.latitude,
    this.longitude,
    required this.daysnhours,
    required this.building,
    required this.room,
    required this.address,
    required this.postaladdress,
    required this.payment,
    required this.mission,
    required this.vision,
    required this.corevalues,
    required this.acceptableclients,
    required this.acceptablehealthinsurance,
    required this.admissions,
    required this.visitors,
    required this.visitinghours,
    required this.discharge,
    required this.parking,
  });

  factory ClinicOrHospital.fromJson(Map<String, dynamic> json) {
    return ClinicOrHospital(
      id: json['id'] ?? 0,
      fullname: json['fullname'] ?? 'Unnamed Clinic/Hospital',
      about: json['about'] ?? 'No description available',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      fax: json['fax'] ?? '',
      whatsapp: json['whatsapp'] ?? '',
      latitude: json['latitude'],
      longitude: json['longitude'],
      daysnhours: json['daysnhours'] ?? '',
      building: json['building'] ?? '',
      room: json['room'] ?? '',
      address: json['address'] ?? '',
      postaladdress: json['postaladdress'] ?? '',
      payment: json['payment'] ?? '',
      mission: json['mission'] ?? '',
      vision: json['vision'] ?? '',
      corevalues: json['corevalues'] ?? '',
      acceptableclients: json['acceptableclients'] ?? '',
      acceptablehealthinsurance: json['acceptablehealthinsurance'] ?? '',
      admissions: json['admissions'] ?? '',
      visitors: json['visitors'] ?? '',
      visitinghours: json['visitinghours'] ?? '',
      discharge: json['discharge'] ?? '',
      parking: json['parking'] ?? '',
    );
  }

  bool hasLocation() {
    return latitude != null && longitude != null && latitude != 0.0 && longitude != 0.0;
  }
}

class TertiaryHealthPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isFromDetailPage;

  const TertiaryHealthPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeData,
    required this.institutionName,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryHealthPage> createState() => _TertiaryHealthPageState();
}

class _TertiaryHealthPageState extends State<TertiaryHealthPage> {

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDark = theme.brightness == Brightness.dark;
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Card(
      key: Key('health_grid_item_$title'),
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          switch (title) {
            case 'Clinics & Hospitals':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ClinicsHospitalsPage(
                    institutionName: widget.institutionName,
                    collegeData: widget.collegeData,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: widget.toggleTheme,
                  ),
                ),
              );
              break;
            case 'Emergency Equipment':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EmergencyEquipmentPage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: widget.toggleTheme,
                    preloadedEmergencyEquipment: [],
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
              break;
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDark ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final bool isFromDetailPage = widget.isFromDetailPage;

    // Define all grid items with their title and icon.
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'Clinics & Hospitals', 'icon': Icons.local_hospital},
      {'title': 'Emergency Equipment', 'icon': Icons.emergency},
    ];

    // No need to filter items as both are always relevant
    final filteredGridItems = gridItems;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Health',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: filteredGridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                        isFromDetailPage,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}