import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'login_page.dart';
import 'event_detail_page.dart';

class EventsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const EventsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<EventsPage> createState() => _EventsPageState();
}

class _EventsPageState extends State<EventsPage> {
  late CalendarFormat _calendarFormat;
  late DateTime _focusedDay;
  late DateTime _selectedDay;
  late Map<DateTime, List<dynamic>> _events;
  late List<dynamic> _selectedEvents;
  bool _isLoading = true;
  String _errorMessage = '';
  List<Map<String, dynamic>> _allEvents = [];

  @override
  void initState() {
    super.initState();
    _calendarFormat = CalendarFormat.week; // Changed from twoWeeks to week
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
    _events = {};
    _selectedEvents = [];
    _fetchEvents();
  }

  Future<void> _fetchEvents() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_events';
      
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('startday', ascending: true);

      final events = List<Map<String, dynamic>>.from(response);
      
      // Convert events to calendar events
      final Map<DateTime, List<dynamic>> calendarEvents = {};
      
      for (var event in events) {
        // Check if the event has start date
        if (event['startday'] != null && 
            event['startmonth'] != null && 
            event['startyear'] != null) {
          
          final startDate = DateTime(
            event['startyear'],
            event['startmonth'],
            event['startday'],
          );
          
          // For multi-day events, add to each day
          if (event['endday'] != null && 
              event['endmonth'] != null && 
              event['endyear'] != null) {
            
            final endDate = DateTime(
              event['endyear'],
              event['endmonth'],
              event['endday'],
            );
            
            // Add event to each day between start and end
            DateTime currentDate = startDate;
            while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
              final key = DateTime(currentDate.year, currentDate.month, currentDate.day);
              
              // Check if this is a recurring event that only happens on specific days
              bool shouldAdd = true;
              if (event['_mon'] != null || event['_tue'] != null || 
                  event['_wed'] != null || event['_thur'] != null || 
                  event['_fri'] != null || event['_sat'] != null || 
                  event['_sun'] != null) {
                
                shouldAdd = false;
                switch (currentDate.weekday) {
                  case DateTime.monday:
                    shouldAdd = event['_mon'] == true;
                    break;
                  case DateTime.tuesday:
                    shouldAdd = event['_tue'] == true;
                    break;
                  case DateTime.wednesday:
                    shouldAdd = event['_wed'] == true;
                    break;
                  case DateTime.thursday:
                    shouldAdd = event['_thur'] == true;
                    break;
                  case DateTime.friday:
                    shouldAdd = event['_fri'] == true;
                    break;
                  case DateTime.saturday:
                    shouldAdd = event['_sat'] == true;
                    break;
                  case DateTime.sunday:
                    shouldAdd = event['_sun'] == true;
                    break;
                }
              }
              
              if (shouldAdd) {
                if (calendarEvents[key] == null) {
                  calendarEvents[key] = [];
                }
                calendarEvents[key]!.add(event);
              }
              
              // Move to next day
              currentDate = currentDate.add(const Duration(days: 1));
            }
          } else {
            // Single day event
            final key = DateTime(startDate.year, startDate.month, startDate.day);
            if (calendarEvents[key] == null) {
              calendarEvents[key] = [];
            }
            calendarEvents[key]!.add(event);
          }
        }
      }

      setState(() {
        _allEvents = events;
        _events = calendarEvents;
        _selectedEvents = _getEventsForDay(_selectedDay);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading events: $e';
      });
      print('Error fetching events: $e');
    }
  }

  List<dynamic> _getEventsForDay(DateTime day) {
    final normalizedDay = DateTime(day.year, day.month, day.day);
    return _events[normalizedDay] ?? [];
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
      _selectedEvents = _getEventsForDay(selectedDay);
    });
  }

  Widget _buildEventCard(
    Map<String, dynamic> event,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String eventName = event['fullname'] ?? 'Unknown Event'; // Changed from 'eventname' to 'fullname'
    final String description = event['description'] ?? '';
    final String location = event['location'] ?? '';
    final String startTime = event['starttime'] ?? '';
    final String endTime = event['endtime'] ?? '';
    
    // Format time display
    String timeText = '';
    if (startTime.isNotEmpty && endTime.isNotEmpty) {
      timeText = '$startTime - $endTime';
    } else if (startTime.isNotEmpty) {
      timeText = 'Starts at $startTime';
    } else if (endTime.isNotEmpty) {
      timeText = 'Ends at $endTime';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => EventDetailPage(
                eventData: event,
                institutionName: widget.institutionName,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.event,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      eventName,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (description.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          description,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (location.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          location,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (timeText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          timeText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Events',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage,
                        style: TextStyle(color: theme.colorScheme.error),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _fetchEvents,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    Container(
                      margin: const EdgeInsets.all(16.0),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surface, // Changed from Colors.white to theme.colorScheme.surface
                        borderRadius: BorderRadius.circular(12.0),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TableCalendar(
                        firstDay: DateTime.utc(2020, 1, 1),
                        lastDay: DateTime.utc(2030, 12, 31),
                        focusedDay: _focusedDay,
                        calendarFormat: _calendarFormat,
                        eventLoader: _getEventsForDay,
                        selectedDayPredicate: (day) {
                          return isSameDay(_selectedDay, day);
                        },
                        onDaySelected: _onDaySelected,
                        onFormatChanged: (format) {
                          setState(() {
                            _calendarFormat = format;
                          });
                        },
                        onPageChanged: (focusedDay) {
                          _focusedDay = focusedDay;
                        },
                        calendarStyle: CalendarStyle(
                          // Updated calendar styling for theme support
                          outsideDaysVisible: false,
                          markersMaxCount: 3,
                          markerDecoration: BoxDecoration(
                            color: currentIsDarkMode ? Colors.white : Colors.black,
                            shape: BoxShape.circle,
                          ),
                          todayDecoration: BoxDecoration(
                            color: (currentIsDarkMode ? Colors.white : Colors.black).withOpacity(0.3),
                            shape: BoxShape.circle,
                          ),
                          selectedDecoration: BoxDecoration(
                            color: currentIsDarkMode ? Colors.white : Colors.black, // Black in light mode, white in dark mode
                            shape: BoxShape.circle,
                          ),
                          defaultTextStyle: TextStyle(
                            color: theme.colorScheme.onSurface, // Theme-aware text color
                          ),
                          weekendTextStyle: TextStyle(
                            color: theme.colorScheme.onSurface, // Theme-aware text color
                          ),
                          outsideTextStyle: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                          ),
                          selectedTextStyle: TextStyle(
                            color: currentIsDarkMode ? Colors.black : Colors.white, // Inverted text color for selected date
                            fontWeight: FontWeight.bold,
                          ),
                          todayTextStyle: TextStyle(
                            color: theme.colorScheme.onSurface,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        headerStyle: HeaderStyle(
                          formatButtonVisible: true,
                          titleCentered: true,
                          formatButtonShowsNext: false,
                          formatButtonDecoration: BoxDecoration(
                            color: theme.colorScheme.surface,
                            borderRadius: BorderRadius.circular(16.0),
                          ),
                          formatButtonTextStyle: TextStyle(
                            color: theme.colorScheme.onSurface,
                          ),
                          titleTextStyle: TextStyle(
                            color: theme.colorScheme.onSurface, // Theme-aware header text
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          leftChevronIcon: Icon(
                            Icons.chevron_left,
                            color: theme.colorScheme.onSurface,
                          ),
                          rightChevronIcon: Icon(
                            Icons.chevron_right,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        daysOfWeekStyle: DaysOfWeekStyle(
                          weekdayStyle: TextStyle(
                            color: theme.colorScheme.onSurface, // Theme-aware days of week text
                          ),
                          weekendStyle: TextStyle(
                            color: theme.colorScheme.onSurface, // Theme-aware days of week text
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: _selectedEvents.isEmpty
                          ? Center(
                              child: Text(
                                'No events scheduled for this day',
                                style: TextStyle(color: theme.colorScheme.onSurface),
                              ),
                            )
                          : ListView.builder(
                              padding: const EdgeInsets.all(16),
                              itemCount: _selectedEvents.length,
                              itemBuilder: (context, index) {
                                final event = _selectedEvents[index];
                                return _buildEventCard(event, theme, currentIsDarkMode);
                              },
                            ),
                    ),
                  ],
                ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}