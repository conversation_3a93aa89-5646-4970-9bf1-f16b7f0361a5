import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class DaycareDetailPage extends StatefulWidget {
  final Map<String, dynamic> daycare;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DaycareDetailPage({
    Key? key,
    required this.daycare,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DaycareDetailPage> createState() => _DaycareDetailPageState();
}

class _DaycareDetailPageState extends State<DaycareDetailPage> {
  late RealtimeChannel _daycareRealtimeChannel;
  String? _imageUrl;
  bool _isLoadingImage = false;

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _daycareRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.daycare['image_url'];
      _isLoadingImage = false;
    });
  }

  void _setupRealtimeListener() {
    _daycareRealtimeChannel = Supabase.instance.client
        .channel('daycare_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'daycares',
      callback: (payload) async {
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.daycare['id']) {
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshDaycare();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshDaycare() async {
    try {
      final response = await Supabase.instance.client
          .from('daycares')
          .select('*')
          .eq('id', widget.daycare['id'])
          .single();

      if (mounted) {
        setState(() {
          widget.daycare.clear();
          widget.daycare.addAll(response);
          _loadImageFromPreloadedData();
          _updateDaycareCache();
        });
      }
    } catch (e) {
      print("Error refreshing daycare: $e");
    }
  }

  Future<void> _updateDaycareCache() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final cacheKey = 'daycares';
    String? cachedDaycaresJson = prefs.getString(cacheKey);

    if (cachedDaycaresJson != null) {
      List<Map<String, dynamic>> cachedDaycares = 
          (jsonDecode(cachedDaycaresJson) as List<dynamic>).cast<Map<String, dynamic>>();

      for (int i = 0; i < cachedDaycares.length; i++) {
        if (cachedDaycares[i]['id'] == widget.daycare['id']) {
          cachedDaycares[i] = widget.daycare;
          break;
        }
      }

      await prefs.setString(cacheKey, jsonEncode(cachedDaycares));
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    if (phoneNumber.isEmpty) return;
    final Uri telUri = Uri(scheme: 'tel', path: phoneNumber);
    await _launchUrlHelper(telUri, 'place a call');
  }

  Future<void> _launchEmail(String email) async {
    if (email.isEmpty) return;
    final Uri emailUri = Uri(scheme: 'mailto', path: email);
    await _launchUrlHelper(emailUri, 'send email');
  }

  Future<void> _launchNavigation(dynamic latitude, dynamic longitude) async {
    double? lat = double.tryParse(latitude?.toString() ?? '');
    double? lng = double.tryParse(longitude?.toString() ?? '');

    if (lat == null || lng == null) {
      _showErrorSnackbar('Location coordinates are invalid.');
      return;
    }
    final Uri mapUri = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$lat,$lng');
    await _launchUrlHelper(mapUri, 'open navigation');
  }

  Future<void> _launchWhatsapp(String phoneNumber) async {
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[+\s()-]'), '');
    final Uri whatsappUri = Uri.parse('https://wa.me/$cleanNumber');
    await _launchUrlHelper(whatsappUri, 'open WhatsApp');
  }

  Future<void> _launchUrlHelper(Uri url, String actionDescription) async {
    try {
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        throw Exception('Could not launch $url');
      }
    } catch (e) {
      _showErrorSnackbar('Could not $actionDescription.');
    }
  }

  void _showErrorSnackbar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.redAccent,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    // Extract daycare information
    final String fullname = widget.daycare['fullname'] ?? 'Unknown';
    final String building = widget.daycare['building'] ?? '';
    final String room = widget.daycare['room'] ?? '';
    final String hours = widget.daycare['hours'] ?? '';
    final String boxNumber = widget.daycare['boxnumber'] ?? '';
    final String about = widget.daycare['about'] ?? '';
    final String phone = widget.daycare['phone']?.toString() ?? '';
    final String email = widget.daycare['email']?.toString() ?? '';
    final String fax = widget.daycare['fax']?.toString() ?? '';
    final String whatsappNumber = widget.daycare['whatsapp']?.toString() ?? '';
    final dynamic latitude = widget.daycare['latitude'];
    final dynamic longitude = widget.daycare['longitude'];

    // Format location text
    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    // Check if contact methods are available
    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null &&
        double.tryParse(latitude.toString()) != null &&
        double.tryParse(longitude.toString()) != null;
    final bool isWhatsappAvailable = whatsappNumber.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top image section
            _isLoadingImage
                ? Container(
                    height: 200,
                    color: theme.colorScheme.surfaceVariant,
                    child: const Center(child: CircularProgressIndicator()),
                  )
                : (_imageUrl != null && _imageUrl!.isNotEmpty)
                    ? CachedNetworkImage(
                        imageUrl: _imageUrl!,
                        height: 200,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          height: 200,
                          color: theme.colorScheme.surfaceVariant,
                          child: const Center(child: CircularProgressIndicator()),
                        ),
                        errorWidget: (context, url, error) => _buildPlaceholderImage(theme),
                      )
                    : _buildPlaceholderImage(theme),
            
            // Main content card
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with name and location
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode
                                ? Colors.white.withOpacity(0.1)
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.child_care,
                              size: 30,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  fullname,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                if (locationText.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      locationText,
                                      style: TextStyle(
                                        color: theme.colorScheme.onSurfaceVariant,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Daycare details
                      if (hours.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.access_time, 
                          'Hours', 
                          hours
                        ),
                      
                      if (boxNumber.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.inbox, 
                          'Box Number', 
                          boxNumber
                        ),
                      
                      // Contact information
                      if (phone.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.phone, 
                          'Phone', 
                          phone, 
                          onTap: () => _launchDialer(phone),
                          canCopy: true,
                        ),
                      
                      if (email.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.email, 
                          'Email', 
                          email, 
                          onTap: () => _launchEmail(email),
                          canCopy: true,
                        ),
                      
                      if (fax.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          Icons.fax, 
                          'Fax', 
                          fax, 
                          canCopy: true,
                        ),
                      
                      if (whatsappNumber.isNotEmpty)
                        _buildDetailRow(
                          theme, 
                          FontAwesomeIcons.whatsapp, 
                          'WhatsApp', 
                          whatsappNumber, 
                          onTap: () => _launchWhatsapp(whatsappNumber),
                          canCopy: true,
                        ),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 24),
                        _buildSectionTitle(theme, Icons.info_outline, 'About this Daycare'),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 14,
                            height: 1.5,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      // Bottom action buttons (EMAIL REMOVED FROM FOOTER)
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              if (isPhoneAvailable)
                IconButton(
                  icon: Icon(
                    Icons.call,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () => _launchDialer(phone),
                  tooltip: 'Call $phone',
                ),
              if (isNavigationAvailable)
                IconButton(
                  icon: Icon(
                    Icons.navigation,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () => _launchNavigation(latitude, longitude),
                  tooltip: 'Navigate',
                ),
              if (isWhatsappAvailable)
                IconButton(
                  icon: FaIcon(
                    FontAwesomeIcons.whatsapp,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () => _launchWhatsapp(whatsappNumber),
                  tooltip: 'WhatsApp $whatsappNumber',
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage(ThemeData theme) {
    return Container(
      height: 200,
      color: theme.colorScheme.surfaceVariant,
      child: Center(
        child: Icon(
          Icons.child_care,
          size: 50,
          color: theme.colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    ThemeData theme, 
    IconData icon, 
    String title, 
    dynamic value, {
    bool canCopy = false, 
    VoidCallback? onTap,
  }) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    final bool isClickable = onTap != null;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurface,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                    fontSize: 13,
                  ),
                ),
                const SizedBox(height: 4),
                InkWell(
                  onTap: onTap,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          value.toString(),
                          style: TextStyle(
                            color: theme.colorScheme.onSurface,
                            decoration: isClickable 
                                ? TextDecoration.underline 
                                : TextDecoration.none,
                            fontSize: 15,
                          ),
                        ),
                      ),
                      if (canCopy)
                        SizedBox(
                          width: 30,
                          height: 30,
                          child: IconButton(
                            icon: Icon(
                              Icons.content_copy,
                              size: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                            onPressed: () {
                              Clipboard.setData(ClipboardData(text: value.toString()));
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('$title copied to clipboard'),
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            },
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(ThemeData theme, IconData icon, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, top: 8.0),
      child: Row(
        children: [
          Icon(
            icon, 
            color: theme.colorScheme.onSurface, 
            size: 20
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}