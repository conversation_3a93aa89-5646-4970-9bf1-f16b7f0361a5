import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import 'login_page.dart';

class DocumentsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedDocuments;
  final bool isFromDetailPage;

  const DocumentsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedDocuments,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _DocumentsPageState createState() => _DocumentsPageState();
}

class _DocumentsPageState extends State<DocumentsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('documents_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _documents = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int? _selectedYear;
  List<int> _yearOptions = [];

  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("DocumentsPage initState called");
    _initializePage();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _initializePage() async {
    await _fetchFilterYears();
    _loadInitialData();
  }

  void _loadInitialData() {
    if (widget.preloadedDocuments != null &&
        widget.preloadedDocuments!.isNotEmpty) {
      print("Preloaded documents found, using them.");
      setState(() {
        _documents = List<Map<String, dynamic>>.from(widget.preloadedDocuments!);
        _documents.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedDocuments!.length >= _pageSize;
      });
    } else {
      print("No preloaded documents, loading from database.");
      _loadDocumentsFromSupabase(initialLoad: true);
    }
  }

  void _setupRealtime() {
    final documentsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_documents';
    _realtimeChannel = Supabase.instance.client
        .channel('documents_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: documentsTableName,
      callback: (payload) async {
        if (!mounted) return;
        print("Realtime update received for documents: ${payload.eventType}");
        // Re-fetch years in case a new one was added/removed
        await _fetchFilterYears();
        // Reload the document list to reflect changes
        await _loadDocumentsFromSupabase(initialLoad: true);
      },
    ).subscribe();
  }

  Future<void> _fetchFilterYears() async {
    if (_isDisposed) return;
    try {
      final documentsTableName =
          '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_documents';
      final response = await Supabase.instance.client
          .from(documentsTableName)
          .select('year');

      if (_isDisposed) return;

      final years = Set<int>.from(response
          .map((item) => item['year'] as int?)
          .where((year) => year != null));

      setState(() {
        _yearOptions = years.toList()..sort((a, b) => b.compareTo(a)); // Sort descending
      });
    } catch (e) {
      print("Error fetching filter years: $e");
      // Don't show a snackbar for this, as it's a non-critical background task
    }
  }

  Future<void> _loadDocumentsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    try {
      final documentsTableName =
          '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_documents';
      
      int startRange;
      if (initialLoad) {
        startRange = 0;
      } else {
        startRange = _documents.length;
      }
      int endRange = startRange + _pageSize - 1;

      var query = Supabase.instance.client
          .from(documentsTableName)
          .select('*');

      // Apply year filter
      if (_selectedYear != null) {
        query = query.eq('year', _selectedYear!);
      }

      final response = await query
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      if (_isDisposed) return;

      setState(() {
        final newItems = List<Map<String, dynamic>>.from(response);
        if (initialLoad) {
          _documents = newItems;
        } else {
          _documents.addAll(newItems);
        }
        _isLoading = false;
        _isLoadingMore = false;
        _hasMore = newItems.length == _pageSize;
      });
    } catch (e) {
      if (_isDisposed) return;
      
      String errorMsg;
      final errorStr = e.toString().toLowerCase();
      if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
        _showOfflineSnackbar();
      } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
        errorMsg = "Almost all data for this institution hasn't been added yet.";
        _showErrorSnackbar(errorMsg);
      } else {
        errorMsg = "Error fetching documents: $e";
        _showErrorSnackbar(errorMsg);
      }

      setState(() {
        _isLoading = false;
        _isLoadingMore = false;
        _hasMore = false;
      });
    }
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadDocumentsFromSupabase(initialLoad: false);
    }
  }

  Future<void> _openDocument(String link) async {
    if (link.isEmpty) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Document link is not available')),
      );
      return;
    }

    final Uri uri = Uri.parse(link);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not open document: $link')),
      );
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  void _showOfflineSnackbar() {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Documents',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              children: [
                Text(
                  'Filter by Year: ',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<int?>(
                        value: _selectedYear,
                        isExpanded: true,
                        hint: const Text('All Years'),
                        icon: Icon(Icons.arrow_drop_down, color: theme.colorScheme.onSurface),
                        style: TextStyle(color: theme.colorScheme.onSurface, fontSize: 16),
                        dropdownColor: theme.colorScheme.surface,
                        items: [
                          const DropdownMenuItem<int?>(
                            value: null,
                            child: Text('All Years'),
                          ),
                          ..._yearOptions.map((int year) {
                            return DropdownMenuItem<int?>(
                              value: year,
                              child: Text(year.toString()),
                            );
                          }).toList(),
                        ],
                        onChanged: (int? newValue) {
                          if (newValue != _selectedYear) {
                            setState(() {
                              _selectedYear = newValue;
                            });
                            _loadDocumentsFromSupabase(initialLoad: true);
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                    color: theme.colorScheme.onSurface,
                    onRefresh: () async {
                       await _loadDocumentsFromSupabase(initialLoad: true);
                    },
                    child: _documents.isEmpty
                        ? LayoutBuilder(
                            builder: (BuildContext context, BoxConstraints constraints) {
                              return SingleChildScrollView(
                                physics: const AlwaysScrollableScrollPhysics(),
                                child: SizedBox(
                                  height: constraints.maxHeight,
                                  child: const Center(
                                    child: Text('No documents found.'),
                                  ),
                                ),
                              );
                            },
                          )
                        : ListView.builder(
                            key: _listKey,
                            controller: _scrollController,
                            physics: const AlwaysScrollableScrollPhysics(),
                            padding: const EdgeInsets.all(16),
                            itemCount: _documents.length + (_hasMore ? 1 : 0),
                            itemBuilder: (context, index) {
                              if (index < _documents.length) {
                                return _buildDocumentCard(
                                  _documents[index],
                                  theme,
                                );
                              } else if (_hasMore) {
                                return const Center(
                                    child: Padding(
                                        padding: EdgeInsets.all(16),
                                        child: CircularProgressIndicator()));
                              } else {
                                return Container();
                              }
                            },
                          ),
                  ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(Icons.home_outlined, color: theme.colorScheme.onSurface),
                  onPressed: () => Navigator.of(context).popUntil((route) => route.isFirst),
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(Icons.person_outline, color: theme.colorScheme.onSurface),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentCard(Map<String, dynamic> document, ThemeData theme) {
    final String fullname = document['fullname'] ?? 'Unknown';
    final String link = document['link'] ?? '';
    final int? year = document['year'];
    final bool isDarkMode = theme.brightness == Brightness.dark;

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _openDocument(link),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.description_outlined,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (year != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Year: $year',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.open_in_new,
                size: 20,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}