import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'accessibility_detail_page.dart';
import 'login_page.dart';

class AccessibilityPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedAccessibilityFeatures;
  final bool isFromDetailPage;

  const AccessibilityPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedAccessibilityFeatures,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _AccessibilityPageState createState() => _AccessibilityPageState();
}

class _AccessibilityPageState extends State<AccessibilityPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('accessibility_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _accessibilityFeatures = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("AccessibilityPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant AccessibilityPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("AccessibilityPage didUpdateWidget called");
    if (oldWidget.collegeNameForTable != widget.collegeNameForTable) {
      _resetAndLoad();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("AccessibilityPage didChangeDependencies called");
  }

  void _resetAndLoad() {
    _page = 0;
    _hasMore = true;
    _accessibilityFeatures.clear();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedAccessibilityFeatures != null && 
        widget.preloadedAccessibilityFeatures!.isNotEmpty) {
      print("Preloaded accessibility features found, using them.");
      setState(() {
        _accessibilityFeatures = List<Map<String, dynamic>>.from(widget.preloadedAccessibilityFeatures!);
        _accessibilityFeatures.forEach((feature) {
          feature['_isImageLoading'] = false;
        });
        _accessibilityFeatures.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedAccessibilityFeatures!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      // Check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded accessibility features or empty list, loading from database.");
      await _loadAccessibilityFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final accessibilityTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_accessibility';
    
    try {
      final response = await Supabase.instance.client
          .from(accessibilityTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_accessibilityFeatures.length, _accessibilityFeatures.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadAccessibilityFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadAccessibilityFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final accessibilityTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_accessibility';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _accessibilityFeatures.length;
        endRange = _accessibilityFeatures.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(accessibilityTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedFeatures =
          await _updateAccessibilityImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _accessibilityFeatures = updatedFeatures;
          } else {
            _accessibilityFeatures.addAll(updatedFeatures);
          }
          _accessibilityFeatures.forEach((feature) {
            feature['_isImageLoading'] = false;
          });
          _accessibilityFeatures.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching accessibility features: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateAccessibilityImageUrls(
      List<Map<String, dynamic>> features) async {
    List<Future<void>> futures = [];
    for (final feature in features) {
      if (feature['image_url'] == null ||
          feature['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(feature));
      }
    }
    await Future.wait(futures);
    return features;
  }

  void _setupRealtime() {
    final accessibilityTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_accessibility';
    _realtimeChannel = Supabase.instance.client
        .channel('accessibility')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: accessibilityTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newFeatureId = payload.newRecord['id'];
          final newFeatureResponse = await Supabase.instance.client
              .from(accessibilityTableName)
              .select('*')
              .eq('id', newFeatureId)
              .single();
          if (mounted) {
            Map<String, dynamic> newFeature = Map.from(newFeatureResponse);
            final updatedFeature = await _updateAccessibilityImageUrls([newFeature]);
            setState(() {
              _accessibilityFeatures.add(updatedFeature.first);
              updatedFeature.first['_isImageLoading'] = false;
              _accessibilityFeatures.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedFeatureId = payload.newRecord['id'];
          final updatedFeatureResponse = await Supabase.instance.client
              .from(accessibilityTableName)
              .select('*')
              .eq('id', updatedFeatureId)
              .single();
          if (mounted) {
            final updatedFeature = Map<String, dynamic>.from(updatedFeatureResponse);
            setState(() {
              _accessibilityFeatures = _accessibilityFeatures.map((feature) {
                return feature['id'] == updatedFeature['id'] ? updatedFeature : feature;
              }).toList();
              _accessibilityFeatures.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedFeatureId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _accessibilityFeatures.removeWhere((feature) => feature['id'] == deletedFeatureId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("AccessibilityPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreFeatures();
    }
  }

  Future<void> _loadMoreFeatures() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more accessibility features...");
      await _loadAccessibilityFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> feature) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AccessibilityDetailPage(
            accessibilityFeature: feature,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("AccessibilityPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Accessibility Features',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadAccessibilityFromSupabase(initialLoad: true);
              },
              child: _accessibilityFeatures.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No accessibility features available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _accessibilityFeatures.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _accessibilityFeatures.length) {
                          final feature = _accessibilityFeatures[index];
                          return VisibilityDetector(
                            key: Key('accessibility_${feature['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (feature['image_url'] == null ||
                                      feature['image_url'] == 'assets/placeholder_image.png') &&
                                  !feature['_isImageLoading']) {
                                _fetchImageUrl(feature);
                              }
                            },
                            child: _buildAccessibilityCard(feature, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> feature) async {
    if (feature['_isImageLoading'] == true) {
      print('Image loading already in progress for ${feature['fullname']}, skipping.');
      return;
    }
    if (feature['image_url'] != null &&
        feature['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${feature['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      feature['_isImageLoading'] = true;
    });

    final fullname = feature['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeAccessibilityBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/accessibility';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeAccessibilityBucket');
    print('Image URL before fetch: ${feature['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeAccessibilityBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeAccessibilityBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        feature['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        feature['_isImageLoading'] = false;
        print('Setting image_url for ${feature['fullname']} to: ${feature['image_url']}');
      });
    } else {
      feature['_isImageLoading'] = false;
    }
  }

  Widget _buildAccessibilityCard(
    Map<String, dynamic> feature,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = feature['fullname'] ?? 'Unknown';
    final String building = feature['building'] ?? '';
    final String room = feature['room'] ?? '';
    final String hours = feature['hours'] ?? '';
    final String about = feature['about'] ?? '';
    final String imageUrl = feature['image_url'] ?? '';

    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, feature),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.accessible,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.accessible,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.accessible,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          hours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}