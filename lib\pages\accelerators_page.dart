// accelerators_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'accelerator_detail_page.dart';

class AcceleratorsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedAccelerators;
  final bool isFromDetailPage;

  const AcceleratorsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedAccelerators,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<AcceleratorsPage> createState() => _AcceleratorsPageState();
}

class _AcceleratorsPageState extends State<AcceleratorsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('accelerators_list');
  List<Map<String, dynamic>> _accelerators = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("AcceleratorsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant AcceleratorsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("AcceleratorsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("AcceleratorsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedAccelerators != null && widget.preloadedAccelerators!.isNotEmpty) {
      print("Preloaded accelerators found, using them.");
      setState(() {
        _accelerators = List<Map<String, dynamic>>.from(widget.preloadedAccelerators!);
        _accelerators.forEach((accelerator) {
          accelerator['_isImageLoading'] = false;
        });
        _accelerators.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded accelerators or empty list, loading from database.");
      await _loadAcceleratorsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final acceleratorsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_accelerators';
    
    try {
      final response = await Supabase.instance.client
          .from(acceleratorsTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_accelerators.length, _accelerators.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadAcceleratorsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadAcceleratorsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final acceleratorsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_accelerators';

    try {
      int startRange = initialLoad ? 0 : _accelerators.length;
      int endRange = startRange + _pageSize - 1;

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(acceleratorsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedAccelerators =
          await _updateAcceleratorImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _accelerators = updatedAccelerators;
          } else {
            _accelerators.addAll(updatedAccelerators);
          }
          _accelerators.forEach((accelerator) {
            accelerator['_isImageLoading'] = false;
          });
          _accelerators.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
      _cacheAccelerators(_accelerators);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching accelerators: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateAcceleratorImageUrls(
      List<Map<String, dynamic>> accelerators) async {
    List<Future<void>> futures = [];
    for (final accelerator in accelerators) {
      if (accelerator['image_url'] == null ||
          accelerator['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(accelerator));
      }
    }
    await Future.wait(futures);
    return accelerators;
  }
  
  void _setupRealtime() {
    final acceleratorsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_accelerators';
    _realtimeChannel = Supabase.instance.client
        .channel('accelerators')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: acceleratorsTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newAcceleratorId = payload.newRecord['id'];
          final newAcceleratorResponse = await Supabase.instance.client
              .from(acceleratorsTableName)
              .select('*')
              .eq('id', newAcceleratorId)
              .single();
          if (mounted) {
            Map<String, dynamic> newAccelerator = Map.from(newAcceleratorResponse);
            final updatedAccelerator = await _updateAcceleratorImageUrls([newAccelerator]);
            setState(() {
              _accelerators.add(updatedAccelerator.first);
              updatedAccelerator.first['_isImageLoading'] = false;
              _accelerators.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedAcceleratorId = payload.newRecord['id'];
          final updatedAcceleratorResponse = await Supabase.instance.client
              .from(acceleratorsTableName)
              .select('*')
              .eq('id', updatedAcceleratorId)
              .single();
          if (mounted) {
            final updatedAccelerator = Map<String, dynamic>.from(updatedAcceleratorResponse);
            setState(() {
              _accelerators = _accelerators.map((accelerator) {
                return accelerator['id'] == updatedAccelerator['id'] ? updatedAccelerator : accelerator;
              }).toList();
              _accelerators.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedAcceleratorId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _accelerators.removeWhere((accelerator) => accelerator['id'] == deletedAcceleratorId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("AcceleratorsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreAccelerators();
    }
  }

  Future<void> _loadMoreAccelerators() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more accelerators...");
      await _loadAcceleratorsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _cacheAccelerators(List<Map<String, dynamic>> accelerators) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String acceleratorsJson = jsonEncode(accelerators);
      await prefs.setString(
          'accelerators_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          acceleratorsJson);
    } catch (e) {
      print('Error caching accelerators: $e');
    }
  }
  
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> accelerator) async {
    if (accelerator['_isImageLoading'] == true) return;
    if (accelerator['image_url'] != null && accelerator['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => accelerator['_isImageLoading'] = true);

    final fullname = accelerator['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeAcceleratorBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/accelerators';

    try {
      final file = await Supabase.instance.client.storage.from(collegeAcceleratorBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeAcceleratorBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        accelerator['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        accelerator['_isImageLoading'] = false;
      });
    } else {
      accelerator['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> accelerator) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AcceleratorDetailPage(
            accelerator: accelerator,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("AcceleratorsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Accelerators',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadAcceleratorsFromSupabase(initialLoad: true);
              },
              child: _accelerators.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No accelerators available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _accelerators.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _accelerators.length) {
                          final accelerator = _accelerators[index];
                          return VisibilityDetector(
                            key: Key('accelerator_${accelerator['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (accelerator['image_url'] == null ||
                                      accelerator['image_url'] == 'assets/placeholder_image.png') &&
                                  !accelerator['_isImageLoading']) {
                                _fetchImageUrl(accelerator);
                              }
                            },
                            child: _buildAcceleratorCard(accelerator, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAcceleratorCard(
    Map<String, dynamic> accelerator,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = accelerator['fullname'] ?? 'Unknown';
    final String building = accelerator['building'] ?? '';
    final String room = accelerator['room'] ?? '';
    final String about = accelerator['about'] ?? '';
    final String imageUrl = accelerator['image_url'] ?? '';

    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, accelerator),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.rocket_launch,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.rocket_launch,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.rocket_launch,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}