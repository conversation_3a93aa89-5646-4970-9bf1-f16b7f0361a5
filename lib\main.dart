import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb, defaultTargetPlatform, TargetPlatform, WidgetState;
import 'package:flutter/services.dart';
import 'dart:async';
import 'package:video_player/video_player.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/firebase_messaging_service.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'pages/tertiary_page.dart';
import 'pages/secondary_page.dart';
import 'pages/primary_page.dart';
import 'pages/pre_primary_page.dart';
import 'pages/login_page.dart';
import 'pages/dashboard_page.dart';
import 'pages/downloader.dart';
import 'widgets/latex_image_renderer.dart';
import 'pages/ai_credits_wallet_page.dart';

// Custom scroll behavior that always shows scrollbar on desktop/web
class CustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    if (kIsWeb || defaultTargetPlatform == TargetPlatform.linux ||
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.windows) {
      return RawScrollbar(
        controller: details.controller,
        thumbVisibility: true,
        thickness: 12.0,
        radius: const Radius.circular(6),
        thumbColor: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFF8c8c8c)
            : const Color.fromRGBO(158, 158, 158, .6),
        trackColor: Theme.of(context).brightness == Brightness.light
            ? Colors.white
            : null,
        child: child,
      );
    }
    return super.buildScrollbar(context, child, details);
  }
}


// Add these global variables at the top of your main file
late final pw.Font roboto;
late final pw.Font robotoBold;


void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize fonts for PDF generation
  final robotoData = await rootBundle.load('assets/fonts/Roboto-Regular.ttf');
  final robotoBoldData = await rootBundle.load('assets/fonts/Roboto-Bold.ttf');
  roboto = pw.Font.ttf(robotoData);
  robotoBold = pw.Font.ttf(robotoBoldData);

    if (!kIsWeb) {
    // Initialize the downloader only on mobile/desktop.
    // import 'package:flutter_downloader/flutter_downloader.dart'; is conditional in downloader.dart
    // await FlutterDownloader.initialize(); // moved to downloader.dart
  }

  // Initialize Firebase FIRST (before Supabase)
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await FirebaseMessagingService().initialize();

  if (!kIsWeb) {
    await MobileAds.instance.initialize(); // Initialize AdMob only if not web
  }

  // Initialize Supabase AFTER Firebase
  await Supabase.initialize(
    url: 'https://qyhhhvqmbahyknltcbqw.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF5aGhodnFtYmFoeWtubHRjYnF3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYxNDkzMzQsImV4cCI6MjA1MTcyNTMzNH0.gEAdk-Efly7wec5AoQrErtL2kHLuEjzMVmCBCKrse7g',
  );

  final prefs = await SharedPreferences.getInstance();
  final isDarkMode = prefs.getBool('darkMode') ?? false;
  runApp(MyApp(initialIsDarkMode: isDarkMode));
}

class MyApp extends StatefulWidget {
  final bool initialIsDarkMode;
  // Static variable to store the preloaded colleges
  static List<Map<String, dynamic>>? preloadedColleges;
  static List<Map<String, dynamic>>? preloadedSecondarySchools;
  static List<Map<String, dynamic>>? preloadedPrimarySchools;
  static List<Map<String, dynamic>>? preloadedPreSchools;

  const MyApp({Key? key, required this.initialIsDarkMode}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  late bool isDarkMode;
  StreamSubscription<List<Map<String, dynamic>>>? _collegesSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _secondarySchoolsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _primarySchoolsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _preSchoolsSubscription;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    isDarkMode = widget.initialIsDarkMode;
    _preloadAllDataAndListen();
   }

  // Function to preload all data and listen for changes
  void _preloadAllDataAndListen() async {
     await _preloadColleges();
      await _preloadSecondarySchools();
      await _preloadPrimarySchools();
      await _preloadPreSchools();
      _listenToCollegeChanges();
      _listenToSecondarySchoolChanges();
      _listenToPrimarySchoolChanges();
      _listenToPreSchoolChanges();
   }


  // Function to preload colleges from Supabase
  Future<void> _preloadColleges() async {
    try {
      final response = await Supabase.instance.client
          .from('colleges')
          .select('*'); // Select all fields

      if (response is List) {
        MyApp.preloadedColleges = List<Map<String, dynamic>>.from(response);
        print('Colleges preloaded successfully: ${MyApp.preloadedColleges?.length} colleges with all fields.');
      } else {
        print('Error preloading colleges: Response is not a List');
      }
    } catch (e) {
      print('Error preloading colleges: $e');
    }
  }

  void _listenToCollegeChanges() {
    _collegesSubscription = Supabase.instance.client
        .from('colleges')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
          if (snapshot.isNotEmpty) {
            print('Realtime update received for colleges: ${snapshot.length} changes.');
            _updatePreloadedColleges(snapshot);
          }
        }, onError: (err) {
          print('Realtime colleges stream error: $err');
        });
  }

  // --- START OF FIX: Corrected Real-time Update Logic ---
  // The old logic was incorrectly parsing the stream payload. This new logic correctly
  // handles the `List<Map<String, dynamic>>` of updated records from the stream.

  void _updatePreloadedColleges(List<Map<String, dynamic>> updatedRecords) {
    if (!mounted) return;

    if (MyApp.preloadedColleges == null) {
      // Fallback: Initialize with the first stream data if preloading hasn't finished.
      setState(() => MyApp.preloadedColleges = List.from(updatedRecords));
      print('Preloaded colleges initialized from stream with ${updatedRecords.length} records.');
      return;
    }

    // Use a Map for efficient updating/adding of records by their ID.
    Map<dynamic, Map<String, dynamic>> collegesMap = {
      for (var college in MyApp.preloadedColleges!) _parseIdToInt(college['id'], 'existing college'): college
    };
    collegesMap.remove(null); // Clean up any entries that had invalid IDs.

    // The 'updatedRecords' is a list of the full, new records.
    // Simply iterate and update the map.
    for (var record in updatedRecords) {
      final recordId = _parseIdToInt(record['id'], 'new college record');
      if (recordId != null) {
        collegesMap[recordId] = record; // This adds new records or updates existing ones.
      }
    }
    
    // Convert back to a list, sort, and update the state.
    setState(() {
      MyApp.preloadedColleges = collegesMap.values.toList()
        ..sort((a, b) => (a['fullname'] ?? '').compareTo(b['fullname'] ?? ''));
    });
    
    print('Preloaded colleges updated via stream. Total colleges: ${MyApp.preloadedColleges?.length}');
  }

  // Preload Secondary Schools
  Future<void> _preloadSecondarySchools() async {
    try {
      final response = await Supabase.instance.client
          .from('secondaryschools')
          .select('*');

      if (response is List) {
        MyApp.preloadedSecondarySchools = List<Map<String, dynamic>>.from(response);
        print('Secondary Schools preloaded successfully: ${MyApp.preloadedSecondarySchools?.length}');
      } else {
        print('Error preloading Secondary Schools: Response is not a List');
      }
    } catch (e) {
      print('Error preloading Secondary Schools: $e');
    }
  }

  void _listenToSecondarySchoolChanges() {
    _secondarySchoolsSubscription = Supabase.instance.client
        .from('secondaryschools')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
          if (snapshot.isNotEmpty) {
            print('Realtime update received for secondary schools: ${snapshot.length} changes.');
            _updatePreloadedSecondarySchools(snapshot);
          }
        }, onError: (err) {
          print('Realtime secondary schools stream error: $err');
        });
  }

  void _updatePreloadedSecondarySchools(List<Map<String, dynamic>> updatedRecords) {
    if (!mounted) return;

    if (MyApp.preloadedSecondarySchools == null) {
      setState(() => MyApp.preloadedSecondarySchools = List.from(updatedRecords));
      print('Preloaded secondary schools initialized from stream.');
      return;
    }

    Map<dynamic, Map<String, dynamic>> schoolsMap = {
      for (var school in MyApp.preloadedSecondarySchools!) _parseIdToInt(school['id'], 'existing secondary school'): school
    };
    schoolsMap.remove(null);

    for (var record in updatedRecords) {
      final recordId = _parseIdToInt(record['id'], 'new secondary school record');
      if (recordId != null) {
        schoolsMap[recordId] = record;
      }
    }
    
    setState(() {
      MyApp.preloadedSecondarySchools = schoolsMap.values.toList()
        ..sort((a, b) => (a['fullname'] ?? '').compareTo(b['fullname'] ?? ''));
    });
    
    print('Preloaded secondary schools updated. Total schools: ${MyApp.preloadedSecondarySchools?.length}');
  }


  // Preload Primary Schools
  Future<void> _preloadPrimarySchools() async {
    try {
      final response = await Supabase.instance.client
          .from('primaryschools')
          .select('*');

      if (response is List) {
        MyApp.preloadedPrimarySchools = List<Map<String, dynamic>>.from(response);
        print('Primary Schools preloaded successfully: ${MyApp.preloadedPrimarySchools?.length}');
      } else {
        print('Error preloading Primary Schools: Response is not a List');
      }
    } catch (e) {
      print('Error preloading Primary Schools: $e');
    }
  }

  void _listenToPrimarySchoolChanges() {
    _primarySchoolsSubscription = Supabase.instance.client
        .from('primaryschools')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
          if (snapshot.isNotEmpty) {
            print('Realtime update received for primary schools: ${snapshot.length} changes.');
            _updatePreloadedPrimarySchools(snapshot);
          }
        }, onError: (err) {
          print('Realtime primary schools stream error: $err');
        });
  }

  void _updatePreloadedPrimarySchools(List<Map<String, dynamic>> updatedRecords) {
    if (!mounted) return;

    if (MyApp.preloadedPrimarySchools == null) {
       setState(() => MyApp.preloadedPrimarySchools = List.from(updatedRecords));
       print('Preloaded primary schools initialized from stream.');
       return;
    }

    Map<dynamic, Map<String, dynamic>> schoolsMap = {
      for (var school in MyApp.preloadedPrimarySchools!) _parseIdToInt(school['id'], 'existing primary school'): school
    };
    schoolsMap.remove(null);

    for (var record in updatedRecords) {
      final recordId = _parseIdToInt(record['id'], 'new primary school record');
      if (recordId != null) {
        schoolsMap[recordId] = record;
      }
    }
    
    setState(() {
      MyApp.preloadedPrimarySchools = schoolsMap.values.toList()
        ..sort((a, b) => (a['fullname'] ?? '').compareTo(b['fullname'] ?? ''));
    });
    
    print('Preloaded primary schools updated. Total schools: ${MyApp.preloadedPrimarySchools?.length}');
  }

  // Preload Pre-schools
  Future<void> _preloadPreSchools() async {
    try {
      final response = await Supabase.instance.client
          .from('preschools')
          .select('*');

      if (response is List) {
        MyApp.preloadedPreSchools = List<Map<String, dynamic>>.from(response);
        print('Pre-schools preloaded successfully: ${MyApp.preloadedPreSchools?.length}');
      } else {
        print('Error preloading Pre-schools: Response is not a List');
      }
    } catch (e) {
      print('Error preloading Pre-schools: $e');
    }
  }

  void _listenToPreSchoolChanges() {
    _preSchoolsSubscription = Supabase.instance.client
        .from('preschools')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
          if (snapshot.isNotEmpty) {
            print('Realtime update received for pre-schools: ${snapshot.length} changes.');
            _updatePreloadedPreSchools(snapshot);
          }
        }, onError: (err) {
          print('Realtime pre-schools stream error: $err');
        });
  }

  void _updatePreloadedPreSchools(List<Map<String, dynamic>> updatedRecords) {
     if (!mounted) return;

    if (MyApp.preloadedPreSchools == null) {
       setState(() => MyApp.preloadedPreSchools = List.from(updatedRecords));
       print('Preloaded pre-schools initialized from stream.');
       return;
    }

    Map<dynamic, Map<String, dynamic>> schoolsMap = {
      for (var school in MyApp.preloadedPreSchools!) _parseIdToInt(school['id'], 'existing pre-school'): school
    };
    schoolsMap.remove(null);

    for (var record in updatedRecords) {
      final recordId = _parseIdToInt(record['id'], 'new pre-school record');
      if (recordId != null) {
        schoolsMap[recordId] = record;
      }
    }
    
    setState(() {
      MyApp.preloadedPreSchools = schoolsMap.values.toList()
        ..sort((a, b) => (a['fullname'] ?? '').compareTo(b['fullname'] ?? ''));
    });
    
    print('Preloaded pre-schools updated. Total schools: ${MyApp.preloadedPreSchools?.length}');
  }

  // Helper function to parse ID to int with error handling
  int? _parseIdToInt(dynamic id, String context) {
    if (id == null) {
      print('Warning: Change data missing ID for $context.');
      return null;
    }
    if (id is int) {
      return id;
    } else if (id is String) {
      try {
        return int.parse(id);
      } catch (e) {
        print('Error parsing ID to int for $context: $id, error: $e');
        return null;
      }
    } else {
      print('Unexpected type for ID in $context: ${id.runtimeType}');
      return null;
    }
  }
  
  // --- END OF FIX ---


  void _saveThemePreference(bool isDark) async {
    final prefs = await SharedPreferences.getInstance();
     await prefs.setBool('darkMode', isDark);
   }

  void toggleTheme() {
    setState(() {
      isDarkMode = !isDarkMode;
      _saveThemePreference(isDarkMode);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _collegesSubscription?.cancel();
    _secondarySchoolsSubscription?.cancel();
    _primarySchoolsSubscription?.cancel();
    _preSchoolsSubscription?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: navigatorKey, // Add navigatorKey for LaTeX image rendering
      scrollBehavior: CustomScrollBehavior(),
      title: 'apptelligent',
      themeMode: isDarkMode ? ThemeMode.dark : ThemeMode.light,
      theme: ThemeData(
        scaffoldBackgroundColor: const Color(0xFFEEEEEE),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.dark,
        ),
        navigationBarTheme: NavigationBarThemeData(
          backgroundColor: Colors.white,
          indicatorColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          height: 60,
        ),
        colorScheme: ColorScheme.light(
          surface: Colors.white,
          onSurface: Colors.black,
          secondary: Colors.black.withOpacity(0.6),
        ),
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            }
            return isDarkMode ? Colors.white : Colors.black;
          }),
          trackColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey.shade300;
            }
            return isDarkMode ? Colors.white.withOpacity(0.3) : Colors.black.withOpacity(0.3);
          }),
        ),
        progressIndicatorTheme: const ProgressIndicatorThemeData(
          color: Colors.grey,
        ),
        textSelectionTheme: const TextSelectionThemeData( // Cursor and selection color
          cursorColor: Colors.black,
          selectionHandleColor: Colors.black,
        ),
        inputDecorationTheme: const InputDecorationTheme( // TextFormField border
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.black),
          ),
        ),
        radioTheme: RadioThemeData( // Radio button theming
          fillColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
            if (states.contains(WidgetState.selected)) {
              return Colors.black; // Selected radio button color - Light mode: Black
            }
            return null; // Use default color for other states
          }),
          overlayColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
            if (states.contains(WidgetState.focused)) {
              return Colors.black.withOpacity(0.12); // Optional: focus overlay
            }
            return null;
          }),
        ),
      ),
      darkTheme: ThemeData(
        scaffoldBackgroundColor: const Color(0xFF090909),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF202020),
          foregroundColor: Colors.white,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
        navigationBarTheme: NavigationBarThemeData(
          backgroundColor: const Color(0xFF202020),
          indicatorColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          height: 60,
        ),
        colorScheme: const ColorScheme.dark(
          surface: Color(0xFF202020),
          onSurface: Colors.white,
          secondary: Colors.white70,
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            }
            return isDarkMode ? Colors.white : Colors.black;
          }),
          trackColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey.shade300;
            }
            return isDarkMode ? Colors.white.withOpacity(0.3) : Colors.black.withOpacity(0.3);
          }),
        ),
        progressIndicatorTheme: const ProgressIndicatorThemeData(
          color: Colors.grey,
        ),
        textSelectionTheme: const TextSelectionThemeData( // Cursor and selection color
          cursorColor: Colors.white,
          selectionHandleColor: Colors.white,
        ),
        inputDecorationTheme: const InputDecorationTheme( // TextFormField border
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.white),
          ),
        ),
        radioTheme: RadioThemeData( // Radio button theming
          fillColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
            if (states.contains(WidgetState.selected)) {
              return Colors.white; // Selected radio button color - Dark mode: White
            }
            return null; // Use default color for other states
          }),
          overlayColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
            if (states.contains(WidgetState.focused)) {
              return Colors.white.withOpacity(0.12); // Optional: focus overlay
            }
            return null;
          }),
        ),
      ),
      home: HomeScreen(
        isDarkMode: isDarkMode,
        toggleTheme: toggleTheme,
      ),
      // Add routes here to define navigation paths
      routes: {
        '/dashboard': (context) => DashboardPage(isDarkMode: isDarkMode, toggleTheme: toggleTheme),
        '/login': (context) => LoginPage(isDarkMode: isDarkMode, toggleTheme: toggleTheme), // If you want to explicitly define login route as well
      },
    );
  }
}

// A list of countries for the selector
const List<String> _countries = [
  "Malawi", "South Africa","USA"
];

class HomeScreen extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const HomeScreen({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late Timer _adTimer;
  List<String> _adUrls = []; // Store URLs from Supabase
  int _currentAdIndex = 0;
  late bool _isDarkMode;
  VoidCallback? _toggleTheme;
  VideoPlayerController? _adVideoController;
  bool _isVideoInitialized = false;

  BannerAd? _bannerAd; // Will be null on web
  bool _isBannerAdReady = false; // Will be false on web

  bool _isWebPlatform = kIsWeb; // Track if it's web platform

  final GlobalKey _appBarKey = GlobalKey(); // Global key for AppBar
  String? _selectedCountry; // To store the selected country

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDarkMode;
    _toggleTheme = widget.toggleTheme;

    // Check for country setting on startup.
    _checkAndPromptForCountry();

    // Request Calendar Permissions here at app start:
    _requestCalendarPermissions();
    _requestCameraPermission(); // Request camera permission for document scanner

    _loadAdUrlsFromSupabase(); // Load ads from Supabase
    _adTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_adUrls.isNotEmpty) {
        setState(() {
          _currentAdIndex = (_currentAdIndex + 1) % _adUrls.length;
          _initializeVideoPlayer();
        });
      }
    });
    if (!_isWebPlatform) { // Load ads only if not web
      _loadBannerAd();
    }
  }

  /// Checks if a country is saved in SharedPreferences.
  /// If not, it presents a non-dismissible modal to force selection.
  Future<void> _checkAndPromptForCountry() async {
    final prefs = await SharedPreferences.getInstance();
    final String? savedCountry = prefs.getString('selectedCountry');

    if (savedCountry == null || savedCountry.isEmpty) {
      // Use addPostFrameCallback to ensure the widget is built before showing a dialog
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        _showCountrySelector(context, isInitialSetup: true);
      });
    } else {
      setState(() {
        _selectedCountry = savedCountry;
      });
    }
  }

  Future<void> _requestCameraPermission() async {
    PermissionStatus cameraStatus = await Permission.camera.status;
    if (!cameraStatus.isGranted) {
      PermissionStatus status = await Permission.camera.request();
      if (status == PermissionStatus.granted) {
        print("Camera permissions granted!");
      } else {
        print("Camera permissions denied.");
        if (status == PermissionStatus.permanentlyDenied) {
          print("Camera permissions permanently denied.");
          openAppSettings();
        }
      }
    } else {
      print("Camera permissions already granted.");
    }
  }


  Future<void> _requestCalendarPermissions() async {
    PermissionStatus calendarStatus = await Permission.calendar.status;

    if (!calendarStatus.isGranted) {
      PermissionStatus status = await Permission.calendar.request();

      if (status == PermissionStatus.granted) {
        // Permissions granted, you can now access the calendar
        print("Calendar permissions granted!");
      } else {
        // Permissions denied
        print("Calendar permissions denied.");
        if (status == PermissionStatus.permanentlyDenied) {
          // The user has permanently denied permissions, you might want to open app settings
          print("Calendar permissions permanently denied.");
          openAppSettings();
        }
      }
    } else {
      // Permissions already granted
      print("Calendar permissions already granted.");
    }
  }


  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isWebPlatform) { // Load banner ad only if not web
      _loadBannerAd();
    }
  }


  void _loadAdUrlsFromSupabase() async {
    try {
      final List<FileObject> response = await Supabase.instance.client
          .storage
          .from('ads') // Ensure 'ads' is your bucket name
          .list();

      if (response.isNotEmpty) {
        final urls = response.map((file) {
          return Supabase.instance.client.storage
              .from('ads')
              .getPublicUrl(file.name);
        }).toList();

        setState(() {
          _adUrls = urls;
          _initializeVideoPlayer(); // Initialize video player after loading ads
        });
        print('Successfully loaded ad URLs from Supabase: $urls'); // Add this log
      } else {
        print('No files found in the Supabase ads bucket.'); // Add this log
      }
    } catch (e) {
      print('Error loading ads from Supabase: $e');
     }
   }


  void _initializeVideoPlayer() {
    if (_adUrls.isNotEmpty && _adUrls[_currentAdIndex].endsWith('.mp4')) {
      if (_adVideoController != null) {
        if (_adVideoController!.dataSource != _adUrls[_currentAdIndex]) {
          _adVideoController!.dispose().then((_) {
            _adVideoController = VideoPlayerController.network(_adUrls[_currentAdIndex]);
            _adVideoController!.initialize().then((_) {
              _adVideoController!.setVolume(0.0); // Mute the video in the grid
              _adVideoController!.play();
              _adVideoController!.setLooping(true);
              if (mounted) {
                setState(() {
                  _isVideoInitialized = true;
                });
              }
            }).catchError((error) {
              print("Error initializing video player: $error");
            });
          });
        } else if (!_isVideoInitialized) {
          _adVideoController!.initialize().then((_) {
            _adVideoController!.setVolume(0.0); // Mute the video in the grid
            _adVideoController!.play();
            _adVideoController!.setLooping(true);
            if (mounted) {
              setState(() {
                _isVideoInitialized = true;
              });
            }
          }).catchError((error) {
            print("Error initializing video player: $error");
          });
        }
      } else {
        _adVideoController = VideoPlayerController.network(_adUrls[_currentAdIndex]);
        _adVideoController!.initialize().then((_) {
          _adVideoController!.setVolume(0.0); // Mute the video in the grid
          _adVideoController!.play();
          _adVideoController!.setLooping(true);
          if (mounted) {
            setState(() {
              _isVideoInitialized = true;
            });
          }
        }).catchError((error) {
          print("Error initializing video player: $error");
        });
      }
     } else {
       _disposeVideoPlayer();
      }
    }

    void _disposeVideoPlayer() {
      if (_adVideoController != null) {
        _adVideoController!.pause();
        _adVideoController!.dispose();
        _adVideoController = null;
        _isVideoInitialized = false;
      }
    }

    Future<void> _loadBannerAd() async {
      if (_isWebPlatform) return; // Don't load on web

      String adUnitId;
      AdSize adSize = AdSize.fluid; // Adaptive for mobile
      adUnitId = 'ca-app-pub-3940256099942544/9214589741'; // Android test banner ad ID


      _bannerAd = BannerAd(
        adUnitId: adUnitId,
        request: const AdRequest(),
        size: adSize,
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            setState(() {
              _isBannerAdReady = true;
            });
          },
          onAdFailedToLoad: (ad, error) {
            print('Banner Ad failed to load: $error');
            _isBannerAdReady = false;
            ad.dispose();
          },
          onAdOpened: (Ad ad) => print('Banner Ad opened.'),
          onAdClosed: (Ad ad) => print('Banner Ad closed.'),
        ),
      );
      _bannerAd!.load();
    }

    @override
    void dispose() {
      _adTimer.cancel();
      _disposeVideoPlayer();
      if (!_isWebPlatform) {
        _bannerAd?.dispose();
      }
      super.dispose();
    }

    void _navigateToPage(BuildContext context, String title) {
      final Map<String, Widget Function(BuildContext)> pages = {
        'Tertiary': (context) => TertiaryPage(
              isDarkMode: _isDarkMode,
              toggleTheme: _toggleTheme!,
              selectedCountry: _selectedCountry ?? 'USA', // Pass country with a default value
            ),
        'Secondary': (context) => SecondaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
        'Primary': (context) => PrimaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
        'Pre-Primary': (context) => PrePrimaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
        'Legwork Automator': (context) {
          final supabaseUser = Supabase.instance.client.auth.currentUser;
          return supabaseUser != null
              ? DashboardPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!)
              : LoginPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!);
        },
      };

      if (pages.containsKey(title)) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: pages[title]!,
          ),
        );
      }
    }

    void _showFullPageAd(BuildContext context, String adUrl) {
      print('_showFullPageAd called with URL: $adUrl');
      Navigator.of(context).push(
        PageRouteBuilder(
          opaque: false,
          pageBuilder: (BuildContext context, _, __) {
            return FullScreenAdModal(
              adUrl: adUrl,
            );
          },
        ),
      );
    }

   _launchURL(Uri url) async {
     if (await canLaunchUrl(url)) {
       await launchUrl(url);
     } else {
       print('Could not launch $url');
     }
   }

   void _showCalendarMenu(BuildContext context) async {
     final theme = Theme.of(context);
     final RenderBox appBarBox = _appBarKey.currentContext!.findRenderObject() as RenderBox;
     final Offset appBarPosition = appBarBox.localToGlobal(Offset.zero);
     final Size appBarSize = appBarBox.size;

     double menuWidth = 240.0; // Adjust width for icons and text
     double appBarCenter = appBarPosition.dx + appBarSize.width / 2;
     double menuLeft = appBarCenter - menuWidth / 2;
     double menuTop = appBarPosition.dy + appBarSize.height;

     await showMenu(
       context: context,
       position: RelativeRect.fromLTRB(
         menuLeft, // left
         menuTop,  // top
         menuLeft + menuWidth, // right (using left + width for explicit width)
         menuTop + 300, // bottom (adjust as needed, large enough to cover menu)
       ),
       color: theme.colorScheme.surface,
       surfaceTintColor: Colors.transparent,
       items: <PopupMenuEntry<String>>[
         PopupMenuItem<String>(
           value: 'header_my_time',
           enabled: false,
           child: Text('My Time', style: TextStyle(fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface)),
         ),
         PopupMenuItem<String>(
           value: 'google_calendar',
           child: Row(
             children: <Widget>[
               Icon(Icons.today, color: theme.colorScheme.onSurface),
               const SizedBox(width: 12),
               Text('Google Calendar', style: TextStyle(color: theme.colorScheme.onSurface)),
             ],
           ),
           onTap: () {
             WidgetsBinding.instance.addPostFrameCallback((_) {
               final Uri googleCalendarUri = Uri.parse('https://calendar.google.com/calendar/r');
               _launchURL(googleCalendarUri);
             });
           },
         ),
         const PopupMenuDivider(),
         PopupMenuItem<String>(
           value: 'header_my_classes',
           enabled: false,
           child: Text('My Classes', style: TextStyle(fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface)),
         ),
         PopupMenuItem<String>(
           value: 'google_classroom',
           child: Row(
             children: <Widget>[
               Icon(Icons.airplay, color: theme.colorScheme.onSurface),
               const SizedBox(width: 12),
               Text('Google Classroom', style: TextStyle(color: theme.colorScheme.onSurface)),
             ],
           ),
           onTap: () {
             WidgetsBinding.instance.addPostFrameCallback((_) {
               final Uri googleClassroomUri = Uri.parse('https://classroom.google.com');
               _launchURL(googleClassroomUri);
             });
           },
         ),
         PopupMenuItem<String>(
           value: 'canvas',
           child: Row(
             children: <Widget>[
               Icon(Icons.layers_outlined, color: theme.colorScheme.onSurface),
               const SizedBox(width: 12),
               Text('Canvas', style: TextStyle(color: theme.colorScheme.onSurface)),
             ],
           ),
           onTap: () {
             WidgetsBinding.instance.addPostFrameCallback((_) {
               final Uri canvasUri = Uri.parse('https://canvas.instructure.com/');
               _launchURL(canvasUri);
             });
           },
         ),
         PopupMenuItem<String>(
           value: 'blackboard',
           child: Row(
             children: <Widget>[
               Icon(Icons.check_box_outline_blank, color: theme.colorScheme.onSurface),
               const SizedBox(width: 12),
               Text('Blackboard', style: TextStyle(color: theme.colorScheme.onSurface)),
             ],
           ),
           onTap: () {
             WidgetsBinding.instance.addPostFrameCallback((_) {
               final Uri blackboardUri = Uri.parse('https://www.blackboard.com/');
               _launchURL(blackboardUri);
             });
           },
         ),
       ],
     );
   }

  void _showCountrySelector(BuildContext context, {bool isInitialSetup = false}) async {
    final theme = Theme.of(context);
    String? tempSelectedCountry = _selectedCountry;

    // FIX: Validate the saved country. If it's not in our list, nullify it to prevent an assertion error.
    if (tempSelectedCountry != null && !_countries.contains(tempSelectedCountry)) {
      tempSelectedCountry = null;
    }

    final result = await showDialog<String>(
      context: context,
      barrierDismissible: !isInitialSetup,
      builder: (dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.0)), // <-- This line sets the border radius
			
              title: Text(isInitialSetup ? 'Welcome! Select a Country' : 'Change Country'),
              backgroundColor: theme.colorScheme.surface,
              surfaceTintColor: Colors.transparent,
              content: DropdownButtonFormField<String>(
                value: tempSelectedCountry,
                hint: const Text('Please select a country'),
                isExpanded: true,
                items: _countries.map((String country) {
                  return DropdownMenuItem<String>(
                    value: country,
                    child: Text(country, overflow: TextOverflow.ellipsis),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    tempSelectedCountry = newValue;
                  });
                },
                decoration: InputDecoration(
                  border: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                  filled: true,
                  fillColor: theme.scaffoldBackgroundColor,
                ),
              ),
              actions: <Widget>[
                if (!isInitialSetup)
                  TextButton(
                    style: TextButton.styleFrom(foregroundColor: theme.colorScheme.onSurface),
                    child: const Text('Cancel'),
                    onPressed: () {
                      Navigator.of(dialogContext).pop();
                    },
                  ),
                TextButton(
                  style: TextButton.styleFrom(foregroundColor: theme.colorScheme.onSurface),
                  onPressed: tempSelectedCountry == null ? null : () {
                    Navigator.of(dialogContext).pop(tempSelectedCountry);
                  },
                  child: Text(isInitialSetup ? 'Confirm' : 'Set'),
                ),
              ],
            );
          },
        );
      },
    );
    
    if (result != null && result.isNotEmpty) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selectedCountry', result);
      setState(() {
        _selectedCountry = result;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Country set to $_selectedCountry'))
        );
      }
    }
  }


    // ==========================================================
    // THIS IS THE MODIFIED AD BANNER METHOD
    // ==========================================================
    Widget _buildAdBanner(BuildContext context, ThemeData theme, {required double height}) {
      if (_adUrls.isEmpty) {
        return const SizedBox(); // Or a placeholder
      }

      final currentAdUrl = _adUrls[_currentAdIndex];
      return Card(
        color: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        clipBehavior: Clip.antiAlias, // Ensures the content respects the card's rounded corners
        child: InkWell(
          onTap: () {
            print('Tapped on ad URL: $currentAdUrl');
            _showFullPageAd(context, currentAdUrl);
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  'Sponsored',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                // Replaced AspectRatio with SizedBox to enforce a specific height
                SizedBox(
                  height: height,
                  child: currentAdUrl.endsWith('.mp4')
                      ? (_isVideoInitialized && _adVideoController != null
                          ? VideoPlayer(_adVideoController!)
                          : Theme(
                              data: theme.copyWith(progressIndicatorTheme: const ProgressIndicatorThemeData(color: Colors.grey)),
                              child: const Center(child: CircularProgressIndicator())
                            ))
                      : Image.network(
                          currentAdUrl,
                          fit: BoxFit.cover,
                          loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Theme(
                              data: theme.copyWith(progressIndicatorTheme: const ProgressIndicatorThemeData(color: Colors.grey)),
                              child: Center(
                                child: CircularProgressIndicator(
                                  value: loadingProgress.expectedTotalBytes != null
                                      ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                      : null,
                                ),
                              ),
                            );
                          },
                          errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
                            print('Error loading image: $exception');
                            return const Icon(Icons.error_outline);
                          },
                        ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // ==========================================================
    // THIS IS THE CORRECTED AND RESTRUCTURED BUILD METHOD
    // ==========================================================
    @override
    Widget build(BuildContext context) {
      final theme = Theme.of(context);
      const double wideLayoutBreakpoint = 720;

      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          key: _appBarKey,
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          scrolledUnderElevation: 0,
          title: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Refactr',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              ShaderMask(
                shaderCallback: (bounds) => const LinearGradient(
                  colors: [Color(0xFF3D9BFF), Color(0xFF5C8DB9), Color(0xFF265B93)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ).createShader(Rect.fromLTWH(0, 0, bounds.width, bounds.height)),
                child: const Text(
                  'X',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          actions: [
            IconButton(
              icon: Icon(Icons.account_balance_wallet_outlined, color: theme.colorScheme.onSurface),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AiCreditsWalletPage(
                      isDarkMode: _isDarkMode,
                      toggleTheme: _toggleTheme!,
                    ),
                  ),
                );
              },
              tooltip: 'Wallet',
            ),
            IconButton(
              icon: Icon(Icons.watch_outlined, color: theme.colorScheme.onSurface),
              onPressed: () => _showCalendarMenu(context),
              tooltip: 'Calendar',
            ),
            IconButton(
              icon: Icon(Icons.more_vert, color: theme.colorScheme.onSurface),
              onPressed: () => _showCountrySelector(context),
              tooltip: _selectedCountry ?? 'Select Country',
            ),
          ],
        ),
        body: SafeArea(
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 900),
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final double totalWidth = constraints.maxWidth;
                      final double halfItemWidth = (totalWidth - 16) / 2;
                      final double automatorHeight = halfItemWidth / 1.1; 
                      final double fullWidthAspectRatio = totalWidth / automatorHeight;
                      
                      final bool isWide = constraints.maxWidth >= wideLayoutBreakpoint;

                      // WIDE LAYOUT (Tablet/Desktop)
                      if (isWide) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            _buildGridItem(
                              context, 
                              'Legwork Automator', 
                              FontAwesomeIcons.robot,
                              theme, 
                              tagline: 'AI Co-Pilot for study and career.',
                              isFullWidth: true,
                              fullWidthAspectRatio: fullWidthAspectRatio,
                              isWide: isWide,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(child: _buildGridItem(context, 'Tertiary', FontAwesomeIcons.graduationCap, theme, tagline: 'Institutions', isWide: isWide)),
                                const SizedBox(width: 16),
                                Expanded(child: _buildGridItem(context, 'Secondary', FontAwesomeIcons.buildingColumns, theme, tagline: 'Schools', isWide: isWide)),
                                const SizedBox(width: 16),
                                Expanded(child: _buildGridItem(context, 'Primary', FontAwesomeIcons.pencilRuler, theme, tagline: 'Schools', isWide: isWide)),
                                const SizedBox(width: 16),
                                Expanded(child: _buildGridItem(context, 'Pre-Primary', Icons.child_care, theme, tagline: 'Schools', isWide: isWide)),
                              ],
                            ),
                            const SizedBox(height: 16),
                            _buildAdBanner(context, theme, height: automatorHeight),
                          ],
                        );
                      } 
                      // NARROW LAYOUT (Mobile)
                      else {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            _buildGridItem(
                              context, 
                              'Legwork Automator', 
                              FontAwesomeIcons.robot, 
                              theme, 
                              tagline: 'AI Co-Pilot for study and career.',
                              isFullWidth: true,
                              fullWidthAspectRatio: fullWidthAspectRatio,
                              isWide: isWide,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(child: _buildGridItem(context, 'Tertiary', FontAwesomeIcons.graduationCap, theme, tagline: 'Institutions', isWide: isWide)),
                                const SizedBox(width: 16),
                                Expanded(child: _buildGridItem(context, 'Secondary', FontAwesomeIcons.buildingColumns, theme, tagline: 'Schools', isWide: isWide)),
                              ],
                            ),
                            const SizedBox(height: 16),
                            _buildAdBanner(context, theme, height: automatorHeight),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(child: _buildGridItem(context, 'Primary', FontAwesomeIcons.pencilRuler, theme, tagline: 'Schools', isWide: isWide)),
                                const SizedBox(width: 16),
                                Expanded(child: _buildGridItem(context, 'Pre-Primary', Icons.child_care, theme, tagline: 'Schools', isWide: isWide)),
                              ],
                            ),
                          ],
                        );
                      }
                    },
                  ),
                ),
              ),
            ),
          ),
        ),
        bottomNavigationBar: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: double.infinity,
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      IconButton(
                        icon: Icon(Icons.home, color: theme.colorScheme.onSurface),
                        onPressed: () {},
                      ),
                      const SizedBox(width: 24),
                      IconButton(
                        icon: Icon(
                          Theme.of(context).brightness == Brightness.dark ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: _toggleTheme,
                      ),
                      const SizedBox(width: 24),
                      IconButton(
                        icon: Icon(Icons.person_outline, color: theme.colorScheme.onSurface),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => LoginPage(
                                isDarkMode: _isDarkMode,
                                toggleTheme: _toggleTheme!,
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
            if (!_isWebPlatform && _isBannerAdReady && _bannerAd != null)
              SizedBox(
                  width: MediaQuery.of(context).size.width,
                  height: _bannerAd?.size.height.toDouble() ?? AdSize.banner.height.toDouble(),
                  child: AdWidget(ad: _bannerAd!),
                ),
          ],
        ),
      );
    }


    Widget _buildGridItem(BuildContext context, String title, dynamic icon, ThemeData theme, {bool isFullWidth = false, double? fullWidthAspectRatio, String? tagline, required bool isWide}) {
      final bool isDarkMode = theme.brightness == Brightness.dark;
      
      final double robotIconSize = isWide ? 56.0 : 23.0;
      final double otherIconSize = isWide ? 30.0 : 23.0;
      final double iconSize = (icon == FontAwesomeIcons.robot) ? robotIconSize : otherIconSize;

      Widget content = Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon is IconData
            ? Icon(
                icon,
                size: iconSize,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              )
            : FaIcon(
                icon as IconData?,
                size: iconSize,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          if (tagline != null)
            Padding(
              padding: const EdgeInsets.only(top: 4.0, left: 8.0, right: 8.0),
              child: Text(
                tagline,
                style: TextStyle(
                  fontSize: 12,
                  color: theme.colorScheme.secondary,
                  height: 1.2,
                ),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      );

      return Card(
        color: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToPage(context, title),
          child: AspectRatio(
            aspectRatio: isFullWidth ? fullWidthAspectRatio! : 1.1,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              child: content,
            ),
          ),
        ),
      );
    }
  }
  
  class FullScreenAdModal extends StatefulWidget {
     final String adUrl;

     const FullScreenAdModal({Key? key, required this.adUrl}) : super(key: key);

     @override
     State<FullScreenAdModal> createState() => _FullScreenAdModalState();
  }

  class _FullScreenAdModalState extends State<FullScreenAdModal> {
     VideoPlayerController? _localVideoController;

     @override
     void initState() {
       super.initState();
       print('FullScreenAdModal initState with URL: ${widget.adUrl}');
       if (widget.adUrl.endsWith('.mp4')) {
         _localVideoController = VideoPlayerController.network(widget.adUrl);
         // Initialize the video player and start playing
         _localVideoController!.initialize().then((_) {
           _localVideoController!.setVolume(1.0);
           _localVideoController!.play();
           _localVideoController!.setLooping(true);
           if (mounted) {
             setState(() {});
           }
         }).catchError((error) {
           print("Error initializing video player: $error");
         });
       }
     }

     @override
     void dispose() {
       print('FullScreenAdModal dispose');
       if (_localVideoController != null) {
         _localVideoController!.dispose();
       }
       super.dispose();
     }

     @override
     Widget build(BuildContext context) {
       final theme = Theme.of(context);
       return Scaffold(
         backgroundColor: Colors.black.withOpacity(0.9),
         body: Center(
           child: SingleChildScrollView(
             child: Stack(
               alignment: Alignment.center,
               children: <Widget>[
                 if (widget.adUrl.endsWith('.mp4'))
                   _localVideoController != null && _localVideoController!.value.isInitialized
                       ? GestureDetector(
                           onTap: () {
                             setState(() {
                               if (_localVideoController!.value.isPlaying) {
                                 _localVideoController!.pause();
                               } else {
                                 _localVideoController!.play();
                               }
                             });
                           },
                           child: AspectRatio(
                             aspectRatio: _localVideoController!.value.aspectRatio,
                             child: VideoPlayer(_localVideoController!),
                           ),
                         )
                       : Theme(
                           data: theme.copyWith(progressIndicatorTheme: const ProgressIndicatorThemeData(color: Colors.grey)),
                           child: const Center(child: CircularProgressIndicator())
                         )
                 else
                   Image.network(
                     widget.adUrl,
                     fit: BoxFit.contain,
                     loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
                       if (loadingProgress == null) return child;
                       return Theme(
                         data: theme.copyWith(progressIndicatorTheme: const ProgressIndicatorThemeData(color: Colors.grey)),
                         child: Center(
                           child: CircularProgressIndicator(
                             value: loadingProgress.expectedTotalBytes != null
                                 ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                 : null,
                           ),
                         ),
                       );
                     },
                     errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
                       print('Error loading full screen image: $exception');
                       return const Icon(Icons.error_outline);
                     },
                   ),
                 Positioned(
                   top: 20,
                   right: 20,
                   child: SafeArea(
                     child: IconButton(
                       icon: const Icon(Icons.close, color: Colors.white),
                       onPressed: () {
                         Navigator.pop(context);
                       },
                     ),
                   ),
                 ),
               ],
             ),
           ),
         ),
       );
     }
  }

  // Example Chart Painter (Replace with your actual chart library widget)
  class LineChartPainter extends CustomPainter {
    final Color lineColor;
    LineChartPainter({required this.lineColor});

    @override
    void paint(Canvas canvas, Size size) {
      final paint = Paint()
        ..color = lineColor
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke;

      final path = Path();
      path.moveTo(0, size.height / 2);
      path.lineTo(size.width, size.height / 2);

      canvas.drawPath(path, paint);
    }

    @override
    bool shouldRepaint(covariant CustomPainter oldDelegate) {
      return false;
    }
  }