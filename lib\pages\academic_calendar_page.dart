import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:intl/intl.dart';
import 'login_page.dart';

class AcademicCalendarPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AcademicCalendarPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<AcademicCalendarPage> createState() => _AcademicCalendarPageState();
}

class _AcademicCalendarPageState extends State<AcademicCalendarPage> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<Map<String, dynamic>> _academicCalendarEvents = [];

  @override
  void initState() {
    super.initState();
    _fetchAcademicCalendar();
  }

  Future<void> _fetchAcademicCalendar() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_academiccalendar';
      
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('startday', ascending: true);

      final academicEvents = List<Map<String, dynamic>>.from(response);
      
      // Sort events chronologically
      academicEvents.sort((a, b) {
        final aYear = a['startyear'] ?? 0;
        final aMonth = a['startmonth'] ?? 1;
        final aDay = a['startday'] ?? 1;
        
        final bYear = b['startyear'] ?? 0;
        final bMonth = b['startmonth'] ?? 1;
        final bDay = b['startday'] ?? 1;
        
        final dateA = DateTime(aYear, aMonth, aDay);
        final dateB = DateTime(bYear, bMonth, bDay);
        
        return dateA.compareTo(dateB);
      });

      setState(() {
        _academicCalendarEvents = academicEvents;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading academic calendar: $e';
      });
      print('Error fetching academic calendar: $e');
    }
  }

  Widget _buildEventCard(
    Map<String, dynamic> event,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String eventName = event['fullname'] ?? 'Unknown Event';
    
    // Format date range
    String dateRange = '';
    if (event['startday'] != null && 
        event['startmonth'] != null && 
        event['startyear'] != null) {
      
      final startDate = DateTime(
        event['startyear'],
        event['startmonth'],
        event['startday'],
      );
      
      if (event['endday'] != null && 
          event['endmonth'] != null && 
          event['endyear'] != null) {
        
        final endDate = DateTime(
          event['endyear'],
          event['endmonth'],
          event['endday'],
        );
        
        if (startDate == endDate) {
          dateRange = DateFormat('MMM d, yyyy').format(startDate);
        } else {
          dateRange = '${DateFormat('MMM d').format(startDate)} - ${DateFormat('MMM d, yyyy').format(endDate)}';
        }
      } else {
        dateRange = DateFormat('MMM d, yyyy').format(startDate);
      }
    }
    
    // Format time display
    String timeText = '';
    final String startTime = event['starttime'] ?? '';
    final String endTime = event['endtime'] ?? '';
    if (startTime.isNotEmpty && endTime.isNotEmpty) {
      timeText = '$startTime - $endTime';
    } else if (startTime.isNotEmpty) {
      timeText = 'Starts at $startTime';
    } else if (endTime.isNotEmpty) {
      timeText = 'Ends at $endTime';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CircleAvatar(
              radius: 24,
              backgroundColor: isDarkMode
                  ? Colors.white.withOpacity(0.1)
                  : Colors.black.withOpacity(0.1),
              child: Icon(
                Icons.event_note,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    eventName,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  if (dateRange.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        dateRange,
                        style: TextStyle(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  if (timeText.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        timeText,
                        style: TextStyle(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Academic Calendar',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage,
                        style: TextStyle(color: theme.colorScheme.error),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _fetchAcademicCalendar,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _academicCalendarEvents.isEmpty
                  ? Center(
                      child: Text(
                        'No academic calendar events available',
                        style: TextStyle(color: theme.colorScheme.onSurface),
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _academicCalendarEvents.length,
                      itemBuilder: (context, index) {
                        final event = _academicCalendarEvents[index];
                        return _buildEventCard(event, theme, currentIsDarkMode);
                      },
                    ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}