import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'login_page.dart';
import 'clinic_hospital_detail_page.dart';
import 'tertiary_health_page.dart';

class ClinicsHospitalsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<ClinicOrHospital>? preloadedClinicsHospitals;
  final bool isFromDetailPage;

  const ClinicsHospitalsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedClinicsHospitals,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<ClinicsHospitalsPage> createState() => _ClinicsHospitalsPageState();
}

class _ClinicsHospitalsPageState extends State<ClinicsHospitalsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('clinics_hospitals_list');
  bool _isDisposed = false;
  List<ClinicOrHospital> _clinicsHospitals = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;
  bool _showMap = false;
  LatLng? _mapCenter;

  @override
  void initState() {
    super.initState();
    print("ClinicsHospitalsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant ClinicsHospitalsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("ClinicsHospitalsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("ClinicsHospitalsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedClinicsHospitals != null && widget.preloadedClinicsHospitals!.isNotEmpty) {
      print("Preloaded clinics/hospitals found, using them.");
      setState(() {
        _clinicsHospitals = List<ClinicOrHospital>.from(widget.preloadedClinicsHospitals!);
        _clinicsHospitals.sort((a, b) =>
            a.fullname.toLowerCase().compareTo(b.fullname.toLowerCase()));
        _hasMore = widget.preloadedClinicsHospitals!.length == _pageSize;
        _page = 0; // Reset page counter
        _mapCenter = _findMapCenter();
      });
      // Don't load more from database if we have preloaded data
      // But check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded clinics/hospitals or empty list, loading from database.");
      await _loadClinicsHospitalsFromSupabase(initialLoad: true);
    }
  }

  LatLng? _findMapCenter() {
    for (var clinic in _clinicsHospitals) {
      if (clinic.hasLocation()) {
        return LatLng(clinic.latitude!, clinic.longitude!);
      }
    }
    return null;
  }

  Future<void> _checkForMoreData() async {
    final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_clinicsorhospitals';
    
    try {
      final response = await Supabase.instance.client
          .from(tableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_clinicsHospitals.length, _clinicsHospitals.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadClinicsHospitalsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadClinicsHospitalsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_clinicsorhospitals';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _clinicsHospitals.length;
        endRange = _clinicsHospitals.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final List<ClinicOrHospital> fetchedClinicsHospitals = List<Map<String, dynamic>>.from(response)
          .map((json) => ClinicOrHospital.fromJson(json))
          .toList();

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _clinicsHospitals = fetchedClinicsHospitals;
            _mapCenter = _findMapCenter();
          } else {
            _clinicsHospitals.addAll(fetchedClinicsHospitals);
          }
          _clinicsHospitals.sort((a, b) =>
              a.fullname.toLowerCase().compareTo(b.fullname.toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching clinics and hospitals: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_clinicsorhospitals';
    _realtimeChannel = Supabase.instance.client
        .channel('clinics_hospitals')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: tableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newClinicId = payload.newRecord['id'];
          final newClinicResponse = await Supabase.instance.client
              .from(tableName)
              .select('*')
              .eq('id', newClinicId)
              .single();
          if (mounted) {
            ClinicOrHospital newClinic = ClinicOrHospital.fromJson(newClinicResponse);
            setState(() {
              _clinicsHospitals.add(newClinic);
              _clinicsHospitals.sort((a, b) =>
                  a.fullname.toLowerCase().compareTo(b.fullname.toLowerCase()));
              if (_mapCenter == null) {
                _mapCenter = _findMapCenter();
              }
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedClinicId = payload.newRecord['id'];
          final updatedClinicResponse = await Supabase.instance.client
              .from(tableName)
              .select('*')
              .eq('id', updatedClinicId)
              .single();
          if (mounted) {
            final updatedClinic = ClinicOrHospital.fromJson(updatedClinicResponse);
            setState(() {
              _clinicsHospitals = _clinicsHospitals.map((clinic) {
                return clinic.id == updatedClinic.id ? updatedClinic : clinic;
              }).toList();
              _clinicsHospitals.sort((a, b) =>
                  a.fullname.toLowerCase().compareTo(b.fullname.toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedClinicId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _clinicsHospitals.removeWhere((clinic) => clinic.id == deletedClinicId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("ClinicsHospitalsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreClinicsHospitals();
    }
  }

  Future<void> _loadMoreClinicsHospitals() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more clinics and hospitals...");
      await _loadClinicsHospitalsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, ClinicOrHospital clinic) {
    if (!_isDisposed) {
      final theme = Theme.of(context);
      final currentIsDarkMode = theme.brightness == Brightness.dark;
      
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ClinicHospitalDetailPage(
            clinic: clinic,
            institutionName: widget.institutionName,
            isDarkMode: currentIsDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("ClinicsHospitalsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Clinics & Hospitals',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadClinicsHospitalsFromSupabase(initialLoad: true);
              },
              child: _clinicsHospitals.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No clinics or hospitals available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                          key: _listKey,
                          controller: _scrollController,
                          shrinkWrap: true,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(16),
                          itemCount: _clinicsHospitals.length + (_hasMore ? 1 : 0),
                          itemBuilder: (context, index) {
                            if (index < _clinicsHospitals.length) {
                              final clinic = _clinicsHospitals[index];
                              return VisibilityDetector(
                                key: Key('clinic_${clinic.id}'),
                                onVisibilityChanged: (VisibilityInfo info) {
                                  // Add any visibility-based logic here if needed
                                },
                                child: _buildClinicCard(clinic, theme, currentIsDarkMode),
                              );
                            } else if (_hasMore) {
                              return const Center(
                                  child: Padding(
                                      padding: EdgeInsets.all(16),
                                      child: CircularProgressIndicator()));
                            } else {
                              return Container();
                            }
                          },
                        ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClinicCard(
    ClinicOrHospital clinic,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = clinic.fullname;
    final String address = clinic.address;
    final String phone = clinic.phone;
    final String daysnhours = clinic.daysnhours;
    final String about = clinic.about;

    String locationText = '';
    if (clinic.building.isNotEmpty && clinic.room.isNotEmpty) {
      locationText = '${clinic.building}, Room ${clinic.room}';
    } else if (clinic.building.isNotEmpty) {
      locationText = clinic.building;
    } else if (clinic.room.isNotEmpty) {
      locationText = 'Room ${clinic.room}';
    } else if (address.isNotEmpty) {
      locationText = address;
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, clinic),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.local_hospital,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (phone.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          phone,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (daysnhours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          daysnhours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMap() {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    // Filter clinics with valid locations
    final clinicsWithLocation = _clinicsHospitals.where((clinic) => clinic.hasLocation()).toList();
    
    if (clinicsWithLocation.isEmpty) {
      return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          return SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: SizedBox(
              height: constraints.maxHeight,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.location_off,
                      size: 64,
                      color: theme.colorScheme.onSurface.withOpacity(0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No clinics or hospitals with location data available',
                      style: TextStyle(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    }
    
    return FlutterMap(
      options: MapOptions(
        initialCenter: _mapCenter!,
        initialZoom: 16.0,
      ),
      children: [
        TileLayer(
          urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
          userAgentPackageName: 'com.harmonizr.app',
        ),
        MarkerLayer(
          markers: clinicsWithLocation.map((clinic) {
            return Marker(
              point: LatLng(clinic.latitude!, clinic.longitude!),
              width: 40,
              height: 40,
              child: GestureDetector(
                onTap: () => _navigateToDetail(context, clinic),
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.local_hospital,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      child: Text(
                        clinic.fullname,
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}