import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'campus_shuttle_detail_page.dart';
import 'login_page.dart';

class CampusShuttlePage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedCampusShuttles;
  final bool isFromDetailPage;

  const CampusShuttlePage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedCampusShuttles,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _CampusShuttlePageState createState() => _CampusShuttlePageState();
}

class _CampusShuttlePageState extends State<CampusShuttlePage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('campus_shuttle_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _campusShuttles = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("CampusShuttlePage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant CampusShuttlePage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("CampusShuttlePage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("CampusShuttlePage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedCampusShuttles != null && widget.preloadedCampusShuttles!.isNotEmpty) {
      print("Preloaded campus shuttles found, using them.");
      setState(() {
        _campusShuttles = List<Map<String, dynamic>>.from(widget.preloadedCampusShuttles!);
        _campusShuttles.forEach((shuttle) {
          shuttle['_isImageLoading'] = false;
        });
        _campusShuttles.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedCampusShuttles!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      // Don't load more from database if we have preloaded data
      // But check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded campus shuttles or empty list, loading from database.");
      await _loadCampusShuttlesFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final campusShuttleTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_campusshuttle';
    
    try {
      final response = await Supabase.instance.client
          .from(campusShuttleTableName)
          .select('id')
          .order('fullname', ascending: true)
          .range(_campusShuttles.length, _campusShuttles.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadCampusShuttlesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadCampusShuttlesFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final campusShuttleTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_campusshuttle';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _campusShuttles.length;
        endRange = _campusShuttles.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(campusShuttleTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final updatedShuttles =
          await _updateCampusShuttleImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _campusShuttles = updatedShuttles;
          } else {
            _campusShuttles.addAll(updatedShuttles);
          }
          _campusShuttles.forEach((shuttle) {
            shuttle['_isImageLoading'] = false;
          });
          _campusShuttles.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching campus shuttles: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateCampusShuttleImageUrls(
      List<Map<String, dynamic>> shuttles) async {
    List<Future<void>> futures = [];
    for (final shuttle in shuttles) {
      if (shuttle['image_url'] == null ||
          shuttle['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(shuttle));
      }
    }
    await Future.wait(futures);
    return shuttles;
  }

  void _setupRealtime() {
    final campusShuttleTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_campusshuttle';
    _realtimeChannel = Supabase.instance.client
        .channel('campus_shuttle_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: campusShuttleTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newShuttleId = payload.newRecord['id'];
          final newShuttleResponse = await Supabase.instance.client
              .from(campusShuttleTableName)
              .select('*')
              .eq('id', newShuttleId)
              .single();
          if (mounted) {
            Map<String, dynamic> newShuttle = Map.from(newShuttleResponse);
            final updatedShuttle = await _updateCampusShuttleImageUrls([newShuttle]);
            setState(() {
              _campusShuttles.add(updatedShuttle.first);
              updatedShuttle.first['_isImageLoading'] = false;
              _campusShuttles.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedShuttleId = payload.newRecord['id'];
          final updatedShuttleResponse = await Supabase.instance.client
              .from(campusShuttleTableName)
              .select('*')
              .eq('id', updatedShuttleId)
              .single();
          if (mounted) {
            final updatedShuttle = Map<String, dynamic>.from(updatedShuttleResponse);
            setState(() {
              _campusShuttles = _campusShuttles.map((shuttle) {
                return shuttle['id'] == updatedShuttle['id'] ? updatedShuttle : shuttle;
              }).toList();
              _campusShuttles.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedShuttleId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _campusShuttles.removeWhere((shuttle) => shuttle['id'] == deletedShuttleId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("CampusShuttlePage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreCampusShuttles();
    }
  }

  Future<void> _loadMoreCampusShuttles() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more campus shuttles...");
      await _loadCampusShuttlesFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> shuttle) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CampusShuttleDetailPage(
            campusShuttle: shuttle,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("CampusShuttlePage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Campus Bus/Shuttle',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadCampusShuttlesFromSupabase(initialLoad: true);
              },
              child: _campusShuttles.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No campus shuttles available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _campusShuttles.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _campusShuttles.length) {
                          final shuttle = _campusShuttles[index];
                          return VisibilityDetector(
                            key: Key('shuttle_${shuttle['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (shuttle['image_url'] == null ||
                                      shuttle['image_url'] == 'assets/placeholder_image.png') &&
                                  !shuttle['_isImageLoading']) {
                                _fetchImageUrl(shuttle);
                              }
                            },
                            child: _buildCampusShuttleCard(shuttle, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> shuttle) async {
    if (shuttle['_isImageLoading'] == true) {
      print('Image loading already in progress for ${shuttle['fullname']}, skipping.');
      return;
    }
    if (shuttle['image_url'] != null &&
        shuttle['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${shuttle['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      shuttle['_isImageLoading'] = true;
    });

    final fullname = shuttle['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeShuttleBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/campusshuttle';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeShuttleBucket');
    print('Image URL before fetch: ${shuttle['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeShuttleBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeShuttleBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        shuttle['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        shuttle['_isImageLoading'] = false;
        print('Setting image_url for ${shuttle['fullname']} to: ${shuttle['image_url']}');
      });
    } else {
      shuttle['_isImageLoading'] = false;
    }
  }

  Widget _buildCampusShuttleCard(
    Map<String, dynamic> shuttle,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = shuttle['fullname'] ?? 'Unknown';
    final String route = shuttle['route'] ?? '';
    final String schedule = shuttle['schedule'] ?? '';
    final String hours = shuttle['hours'] ?? '';
    final String about = shuttle['about'] ?? '';
    final String imageUrl = shuttle['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, shuttle),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.directions_bus,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.directions_bus,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.directions_bus,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (route.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Route: $route',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (schedule.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Schedule: $schedule',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          hours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}