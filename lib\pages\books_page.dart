// books_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'book_detail_page.dart';

class BooksPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedBooks;
  final bool isFromDetailPage;

  const BooksPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedBooks,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<BooksPage> createState() => _BooksPageState();
}

class _BooksPageState extends State<BooksPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('books_list');
  List<Map<String, dynamic>> _books = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  String? _selectedYear;
  List<String> _availableYears = [];

  @override
  void initState() {
    super.initState();
    print("BooksPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant BooksPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.collegeNameForTable != widget.collegeNameForTable) {
      _loadInitialData();
    }
    print("BooksPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("BooksPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    await _fetchFilterOptions();
    if (widget.preloadedBooks != null && widget.preloadedBooks!.isNotEmpty) {
      print("Preloaded books found, using them.");
      setState(() {
        _books = List<Map<String, dynamic>>.from(widget.preloadedBooks!);
        _books.forEach((book) => book['_isImageLoading'] = false);
        _sortBooks();
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded books or empty list, loading from database.");
      await _loadBooksFromSupabase(initialLoad: true);
    }
  }

  Future<void> _fetchFilterOptions() async {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_books';
    try {
      final response = await Supabase.instance.client.from(tableName).select('year');
      if (mounted) {
        setState(() {
          _availableYears = (response as List)
              .map((e) => e['year'].toString())
              .where((y) => y != 'null' && y.isNotEmpty)
              .toSet()
              .toList()..sort((a,b) => b.compareTo(a));
        });
      }
    } catch (e) {
      print('Error fetching filter options: $e');
    }
  }

  Future<void> _checkForMoreData() async {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_books';
    
    try {
      var query = Supabase.instance.client.from(tableName).select('id');
      if (_selectedYear != null) query = query.eq('year', int.parse(_selectedYear!));
      
      final response = await query
          .order('year', ascending: false)
          .range(_books.length, _books.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadBooksFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadBooksFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_books';

    try {
      int startRange = initialLoad ? 0 : _books.length;
      int endRange = startRange + _pageSize - 1;

      var query = Supabase.instance.client.from(tableName).select('*');
      if (_selectedYear != null) query = query.eq('year', int.parse(_selectedYear!));

      final response = await query
          .order('year', ascending: false)
          .range(startRange, endRange);

      final updatedBooks = await _updateBookImageUrls(List<Map<String, dynamic>>.from(response));

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _books = updatedBooks;
          } else {
            _books.addAll(updatedBooks);
          }
          _books.forEach((book) => book['_isImageLoading'] = false);
          _sortBooks();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching books: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateBookImageUrls(
      List<Map<String, dynamic>> books) async {
    List<Future<void>> futures = [];
    for (final book in books) {
      if (book['image_url'] == null ||
          book['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(book));
      }
    }
    await Future.wait(futures);
    return books;
  }

  void _setupRealtime() {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_books';
    _realtimeChannel = Supabase.instance.client
        .channel('books_realtime')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: tableName,
      callback: (payload) async {
        if (!mounted) return;
        await _loadBooksFromSupabase(initialLoad: true);
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("BooksPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreBooks();
    }
  }

  Future<void> _loadMoreBooks() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more books...");
      await _loadBooksFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _sortBooks() {
    _books.sort((a, b) => (b['year'] ?? 0).compareTo(a['year'] ?? 0));
  }

  void _showOfflineSnackbar() {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> book) async {
    if (book['_isImageLoading'] == true) return;
    if (book['image_url'] != null && book['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => book['_isImageLoading'] = true);

    final fullname = book['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final bucketName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/books';

    try {
      final file = await Supabase.instance.client.storage.from(bucketName).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(bucketName).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
    }

    if (mounted) {
      setState(() {
        book['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        book['_isImageLoading'] = false;
      });
    } else {
      book['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> book) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BookDetailPage(
            book: book,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForTable: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  void _showFilterDialog() {
    String? tempYear = _selectedYear;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Filter by Year'),
              content: DropdownButton<String>(
                value: tempYear,
                hint: const Text('Select a Year'),
                isExpanded: true,
                items: _availableYears.map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(value: value, child: Text(value));
                }).toList(),
                onChanged: (String? newValue) {
                  setDialogState(() {
                    tempYear = newValue;
                  });
                },
              ),
              actions: [
                TextButton(
                  child: const Text('Clear'),
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (_selectedYear != null) {
                      setState(() {
                        _selectedYear = null;
                        _page = 0;
                        _hasMore = true;
                      });
                      _loadBooksFromSupabase(initialLoad: true);
                    }
                  },
                ),
                TextButton(
                  child: const Text('Apply'),
                  onPressed: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedYear = tempYear;
                      _page = 0;
                      _hasMore = true;
                    });
                    _loadBooksFromSupabase(initialLoad: true);
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    print("BooksPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Books',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.filter_list, color: theme.colorScheme.onSurface),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadBooksFromSupabase(initialLoad: true);
              },
              child: _books.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No books available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _books.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _books.length) {
                          final book = _books[index];
                          return VisibilityDetector(
                            key: Key('book_${book['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (book['image_url'] == null ||
                                      book['image_url'] == 'assets/placeholder_image.png') &&
                                  !book['_isImageLoading']) {
                                _fetchImageUrl(book);
                              }
                            },
                            child: _buildBookCard(book, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  String _formatAuthors(Map<String, dynamic> book) {
    final List<String> authors = [];
    if (book['author'] != null && book['author'].toString().isNotEmpty) {
      authors.add(book['author'].toString());
    }
    if (book['author2'] != null && book['author2'].toString().isNotEmpty) {
      authors.add(book['author2'].toString());
    }
    if (book['author3'] != null && book['author3'].toString().isNotEmpty) {
      authors.add(book['author3'].toString());
    }
    return authors.isNotEmpty ? authors.join(', ') : 'N/A';
  }

  Widget _buildBookCard(
    Map<String, dynamic> book,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = book['fullname'] ?? 'Unknown';
    final String authors = _formatAuthors(book);
    final String publisher = book['publisher'] ?? '';
    final String year = book['year']?.toString() ?? '';
    final String imageUrl = book['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, book),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                ? ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.book,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      errorWidget: (context, url, error) => CircleAvatar(
                        radius: 24,
                        backgroundColor: isDarkMode
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.book,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                  )
                : CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      Icons.book,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (authors != 'N/A')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          authors,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (publisher.isNotEmpty || year.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          [
                            if (publisher.isNotEmpty) publisher,
                            if (year.isNotEmpty) year,
                          ].join(' • '),
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}