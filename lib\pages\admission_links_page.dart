// admission_links_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';

class AdmissionLinksPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedLinks;

  const AdmissionLinksPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedLinks,
  }) : super(key: key);

  @override
  _AdmissionLinksPageState createState() => _AdmissionLinksPageState();
}

class _AdmissionLinksPageState extends State<AdmissionLinksPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('admission_links_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _links = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("AdmissionLinksPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant AdmissionLinksPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("AdmissionLinksPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("AdmissionLinksPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedLinks != null && widget.preloadedLinks!.isNotEmpty) {
      print("Preloaded links found, using them.");
      setState(() {
        _links = List<Map<String, dynamic>>.from(widget.preloadedLinks!);
        _links.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedLinks!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      // Check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded links or empty list, loading from database and cache.");
      await _loadLinksFromCache();
      await _loadLinksFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final linksTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_links';
    
    try {
      final response = await Supabase.instance.client
          .from(linksTableName)
          .select('id')
          .eq('admissionslink', true)
          .order('fullname', ascending: true)
          .range(_links.length, _links.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadLinksFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'admission_links_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedData = prefs.getString(cacheKey);

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> links = decodedData.cast<Map<String, dynamic>>();
        
        if (mounted && links.isNotEmpty) {
          setState(() {
            _links = links;
          });
          print("Loaded ${links.length} links from cache");
        }
      }
    } catch (e) {
      print("Error loading links from cache: $e");
    }
  }

  Future<void> _loadLinksFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadLinksFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    try {
      final linksTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_links';
      
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _links.length;
        endRange = _links.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      var query = Supabase.instance.client
          .from(linksTableName)
          .select('*')
          .eq('admissionslink', true)
          .order('fullname', ascending: true)
          .range(startRange, endRange);
      
      final response = await query;

      if (_isDisposed) return;

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _links = List<Map<String, dynamic>>.from(response);
          } else {
            _links.addAll(List<Map<String, dynamic>>.from(response));
          }
          _links.sort((a, b) =>
              (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });

        // Cache the data
        if (initialLoad) {
          _cacheLinks(_links);
        }
      }

    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error loading links: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<void> _cacheLinks(List<Map<String, dynamic>> links) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'admission_links_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(links));
      print("Cached ${links.length} links");
    } catch (e) {
      print("Error caching links: $e");
    }
  }

  void _setupRealtime() {
    final linksTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_links';
    _realtimeChannel = Supabase.instance.client
        .channel('admission_links_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: linksTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        print("Realtime update received for links: ${payload.eventType}");
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newRecord = payload.newRecord;
          if (newRecord['admissionslink'] == true) {
            if (mounted) {
              setState(() {
                _links.add(Map<String, dynamic>.from(newRecord));
                _links.sort((a, b) =>
                    (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
              });
            }
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedRecord = payload.newRecord;
          if (mounted) {
            setState(() {
              _links = _links.map((link) {
                return link['id'] == updatedRecord['id'] 
                    ? Map<String, dynamic>.from(updatedRecord) 
                    : link;
              }).where((link) => link['admissionslink'] == true).toList();
              _links.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _links.removeWhere((link) => link['id'] == deletedId);
            });
          }
        }
      },
    ).subscribe();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreLinks();
    }
  }

  Future<void> _loadMoreLinks() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more links...");
      await _loadLinksFromSupabase(initialLoad: false);
    }
  }

  Future<void> _openLink(String url) async {
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Link URL is not available')),
      );
      return;
    }

    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not open link: $url')),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("AdmissionLinksPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    print("AdmissionLinksPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Admission Links',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadLinksFromSupabase(initialLoad: true);
              },
              child: _links.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No admission links available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _links.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _links.length) {
                          return _buildLinkCard(
                            _links[index],
                            theme,
                            currentIsDarkMode,
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLinkCard(
    Map<String, dynamic> link,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = link['fullname'] ?? 'Unnamed Link';
    final String description = link['description'] ?? '';
    final String url = link['link'] ?? '';  // Changed from 'url' to 'link'
    final String category = link['category'] ?? '';

    // Determine icon based on URL or category
    IconData linkIcon = Icons.link;
    if (url.contains('youtube.com') || url.contains('youtu.be')) {
      linkIcon = Icons.video_library;
    } else if (url.contains('facebook.com')) {
      linkIcon = Icons.facebook;
    } else if (url.contains('twitter.com')) {
      linkIcon = Icons.flutter_dash;
    } else if (url.contains('instagram.com')) {
      linkIcon = Icons.camera_alt;
    } else if (url.contains('linkedin.com')) {
      linkIcon = Icons.work;
    } else if (url.contains('pdf')) {
      linkIcon = Icons.picture_as_pdf;
    } else if (url.contains('doc') || url.contains('docx')) {
      linkIcon = Icons.description;
    } else if (url.contains('xls') || url.contains('xlsx')) {
      linkIcon = Icons.table_chart;
    } else if (url.contains('ppt') || url.contains('pptx')) {
      linkIcon = Icons.slideshow;
    } else if (category.toLowerCase().contains('form')) {
      linkIcon = Icons.assignment;
    } else if (category.toLowerCase().contains('application')) {
      linkIcon = Icons.app_registration;
    } else if (category.toLowerCase().contains('scholarship')) {
      linkIcon = Icons.school;
    } else if (category.toLowerCase().contains('financial')) {
      linkIcon = Icons.attach_money;
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _openLink(url),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  linkIcon,
                  color: isDarkMode ? Colors.white : Colors.black,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              // Link details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (description.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 13,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    if (category.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        category,
                        style: TextStyle(
                          fontSize: 12,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Arrow icon
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}