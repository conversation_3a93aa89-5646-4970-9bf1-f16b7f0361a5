import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:convert';

import 'login_page.dart';
import 'people_directory_page.dart';
import 'departments_page.dart';

class TertiaryPeoplePage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryPeoplePage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryPeoplePage> createState() => _TertiaryPeoplePageState();
}

class _TertiaryPeoplePageState extends State<TertiaryPeoplePage> {
  List<Map<String, dynamic>>? _cachedPeople;
  List<Map<String, dynamic>>? _cachedStudents;
  List<Map<String, dynamic>>? _cachedDepartments;
  String? _lastCollegeName;
  bool _isLoadingPeople = false;
  bool _isLoadingStudents = false;
  bool _isLoadingDepartments = false;
  late RealtimeChannel _peopleRealtimeChannel;
  late RealtimeChannel _studentsRealtimeChannel;

  @override
  void initState() {
    super.initState();
    print("TertiaryPeoplePage initState called for ${widget.institutionName}");
    _loadCachedPeople();
    _loadCachedStudents();
    _loadCachedDepartments();
    _loadPeopleFromDatabaseAndCache();
    _loadStudentsFromDatabaseAndCache();
    _loadDepartmentsFromDatabaseAndCache();
    _setupPeopleRealtimeListener();
    _setupStudentsRealtimeListener();
  }

  @override
  void dispose() {
    _peopleRealtimeChannel.unsubscribe();
    _studentsRealtimeChannel.unsubscribe();
    super.dispose();
  }

  // People methods
  Future<List<Map<String, dynamic>>?> _getCachedPeople(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? peopleJson = prefs.getString(
        'people_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (peopleJson != null) {
      List<dynamic> decodedList = jsonDecode(peopleJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cachePeople(String collegeName, List<Map<String, dynamic>> people) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String peopleJson = jsonEncode(people);
    await prefs.setString(
        'people_${collegeName.toLowerCase().replaceAll(' ', '')}', peopleJson);
    print('People cached for $collegeName.');
  }

  Future<void> _loadCachedPeople() async {
    final cachedData = await _getCachedPeople(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedPeople = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded people from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadPeopleFromDatabaseAndCache() async {
    if (_isLoadingPeople) {
      return;
    }

    setState(() {
      _isLoadingPeople = true;
    });

    print("Fetching people for ${widget.institutionName} from database");
    final peopleTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_people';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(peopleTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedPeople = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingPeople = false;
          _cachePeople(widget.institutionName, response);
          print("People fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingPeople = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingPeople = false;
          _cachedPeople = [];
          print("Error fetching people for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingPeople = false;
      }
    }
  }

  void _setupPeopleRealtimeListener() {
    final peopleTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_people';
    _peopleRealtimeChannel = Supabase.instance.client
        .channel('people_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: peopleTableName,
      callback: (payload) async {
        print(
            "Realtime update received for people of ${widget.institutionName}: ${payload.eventType}");
        _loadPeopleFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Students methods
  Future<List<Map<String, dynamic>>?> _getCachedStudents(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? studentsJson = prefs.getString(
        'students_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (studentsJson != null) {
      List<dynamic> decodedList = jsonDecode(studentsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheStudents(String collegeName, List<Map<String, dynamic>> students) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String studentsJson = jsonEncode(students);
    await prefs.setString(
        'students_${collegeName.toLowerCase().replaceAll(' ', '')}', studentsJson);
    print('Students cached for $collegeName.');
  }

  Future<void> _loadCachedStudents() async {
    final cachedData = await _getCachedStudents(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedStudents = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded students from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadStudentsFromDatabaseAndCache() async {
    if (_isLoadingStudents) {
      return;
    }

    setState(() {
      _isLoadingStudents = true;
    });

    print("Fetching students for ${widget.institutionName} from database");
    final studentsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_currentstudents';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(studentsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedStudents = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingStudents = false;
          _cacheStudents(widget.institutionName, response);
          print("Students fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingStudents = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingStudents = false;
          _cachedStudents = [];
          print("Error fetching students for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingStudents = false;
      }
    }
  }

  void _setupStudentsRealtimeListener() {
    final studentsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_currentstudents';
    _studentsRealtimeChannel = Supabase.instance.client
        .channel('students_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: studentsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for students of ${widget.institutionName}: ${payload.eventType}");
        _loadStudentsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Departments methods
  Future<void> _loadCachedDepartments() async {
    // We don't need to cache departments separately as they are derived from people and students
    // This method is kept for consistency with the pattern
  }

  Future<void> _loadDepartmentsFromDatabaseAndCache() async {
    if (_isLoadingDepartments) {
      return;
    }

    setState(() {
      _isLoadingDepartments = true;
    });

    // Departments are derived from people and students, so we need to wait for both to load
    if (_isLoadingPeople || _isLoadingStudents) {
      Future.delayed(Duration(milliseconds: 500), () {
        _loadDepartmentsFromDatabaseAndCache();
      });
      return;
    }

    try {
      // Extract unique departments from people and students
      Set<String> uniqueDepartments = {};

      if (_cachedPeople != null) {
        for (var person in _cachedPeople!) {
          if (person['department'] != null && person['department'].toString().isNotEmpty) {
            uniqueDepartments.add(person['department']);
          }
        }
      }

      if (_cachedStudents != null) {
        for (var student in _cachedStudents!) {
          if (student['department'] != null && student['department'].toString().isNotEmpty) {
            uniqueDepartments.add(student['department']);
          }
        }
      }

      // Convert to list of maps
      List<Map<String, dynamic>> departments = [];
      for (var dept in uniqueDepartments) {
        departments.add({'department': dept});
      }

      // Sort departments alphabetically
      departments.sort((a, b) => (a['department'] ?? '').compareTo(b['department'] ?? ''));

      if (mounted) {
        setState(() {
          _cachedDepartments = departments;
          _isLoadingDepartments = false;
          print("Departments derived for ${widget.institutionName}");
        });
      } else {
        _isLoadingDepartments = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingDepartments = false;
          _cachedDepartments = [];
          print("Error deriving departments for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingDepartments = false;
      }
    }
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    // Always show grid items
    return Visibility(
      key: Key('people_grid_item_$title'),
      visible: true,
      child: Card(
        color: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (title == 'A-Z Directory') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PeopleDirectoryPage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: isDarkMode,
                    toggleTheme: widget.toggleTheme,
                    preloadedPeople: _cachedPeople,
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
            } else if (title == 'Departments') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DepartmentsPage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: isDarkMode,
                    toggleTheme: widget.toggleTheme,
                    preloadedDepartments: _cachedDepartments,
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
                ),
                ...[
                  const SizedBox(height: 8),
                  Flexible(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                ]
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = widget.isFromDetailPage;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'People',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildGridItem(context, 'A-Z Directory', Icons.people, theme, isFromDetailPage),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}