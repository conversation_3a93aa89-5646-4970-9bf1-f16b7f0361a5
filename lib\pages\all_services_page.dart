import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'login_page.dart';
import 'service_detail_page.dart';
import 'tertiary_services_page.dart';

class AllServicesPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AllServicesPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<AllServicesPage> createState() => _AllServicesPageState();
}

class _AllServicesPageState extends State<AllServicesPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('services_list');
  bool _isDisposed = false;
  List<Service> _services = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 10;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("AllServicesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant AllServicesPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("AllServicesPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("AllServicesPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    await _loadServicesFromSupabase(initialLoad: true);
  }

  Future<void> _loadServicesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadServicesFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_services';

    try {
      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _services.length;
        endRange = _services.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(startRange, endRange);

      final List<Service> services = List<Map<String, dynamic>>.from(response)
          .map((json) => Service.fromJson(json))
          .toList();

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _services = services;
          } else {
            _services.addAll(services);
          }
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });
      }
    } catch (e) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = e.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching services: $e";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
        print('Error fetching services: $e');
      }
    }
  }

  void _setupRealtime() {
    final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_services';
    _realtimeChannel = Supabase.instance.client
        .channel('services')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: tableName,
      callback: (payload) async {
        if (!mounted) return;
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newServiceId = payload.newRecord['id'];
          final newServiceResponse = await Supabase.instance.client
              .from(tableName)
              .select('*')
              .eq('id', newServiceId)
              .single();
          if (mounted) {
            final newService = Service.fromJson(Map.from(newServiceResponse));
            setState(() {
              _services.add(newService);
              _services.sort((a, b) =>
                  a.fullname.toLowerCase().compareTo(b.fullname.toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedServiceId = payload.newRecord['id'];
          final updatedServiceResponse = await Supabase.instance.client
              .from(tableName)
              .select('*')
              .eq('id', updatedServiceId)
              .single();
          if (mounted) {
            final updatedService = Service.fromJson(Map<String, dynamic>.from(updatedServiceResponse));
            setState(() {
              _services = _services.map((service) {
                return service.id == updatedService.id ? updatedService : service;
              }).toList();
              _services.sort((a, b) =>
                  a.fullname.toLowerCase().compareTo(b.fullname.toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedServiceId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _services.removeWhere((service) => service.id == deletedServiceId);
            });
          }
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("AllServicesPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreServices();
    }
  }

  Future<void> _loadMoreServices() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more services...");
      await _loadServicesFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Service service) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ServiceDetailPage(
            service: service,
            institutionName: widget.institutionName,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print("AllServicesPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'All Services',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadServicesFromSupabase(initialLoad: true);
              },
              child: _services.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No services available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _services.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _services.length) {
                          final service = _services[index];
                          return VisibilityDetector(
                            key: Key('service_${service.id}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              // Placeholder for any future visibility-based logic
                            },
                            child: _buildServiceCard(service, theme, currentIsDarkMode),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildServiceCard(
    Service service,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = service.fullname;
    final String department = service.department;
    final String price = service.price;
    final String time = service.time;
    final String about = service.about;

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, service),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.room_service,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty && about != 'No description available')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about.length > 100 ? '${about.substring(0, 100)}...' : about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (department.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Department: $department',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (price.isNotEmpty && price != 'Price not specified')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Price: $price',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (time.isNotEmpty && time != 'Time not specified')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Time: $time',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}