// selection_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';
import 'selection_detail_page.dart';

class SelectionPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedSelection;
  final bool isFromDetailPage;

  const SelectionPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedSelection,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _SelectionPageState createState() => _SelectionPageState();
}

class _SelectionPageState extends State<SelectionPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('selection_list');
  bool _isDisposed = false;
  
  // Data lists
  List<Map<String, dynamic>> _selectedStudents = []; // Master list from Supabase
  List<Map<String, dynamic>> _filteredStudents = []; // List displayed on screen
  
  // Loading and pagination state
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  // Search and Filter state
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  Set<dynamic> _availableYears = {};
  Set<String> _availableMajors = {};
  Set<String> _availableGenders = {};

  // State variables for single-selection dropdowns
  dynamic _selectedYear;
  String? _selectedMajor;
  String? _selectedGender;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }
  
  void _onSearchChanged() {
    if (_searchQuery != _searchController.text) {
      setState(() {
        _searchQuery = _searchController.text;
        _applyFiltersAndSearch();
      });
    }
  }
  
  Future<void> _loadInitialData() async {
    if (widget.preloadedSelection != null && widget.preloadedSelection!.isNotEmpty) {
      setState(() {
        _selectedStudents = List<Map<String, dynamic>>.from(widget.preloadedSelection!);
        _hasMore = widget.preloadedSelection!.length == _pageSize;
        _page = 0;
      });
      _updateAvailableFilters();
      _applyFiltersAndSearch();
      await _checkForMoreData();
    } else {
      await _loadSelectionFromCache();
      await _loadSelectionFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final selectionTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_selection';
    try {
      final response = await Supabase.instance.client
          .from(selectionTableName)
          .select('id')
          .range(_selectedStudents.length, _selectedStudents.length + _pageSize - 1);
      
      if(mounted) setState(() => _hasMore = response.isNotEmpty);
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadSelectionFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'selection_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedData = prefs.getString(cacheKey);

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> selectedStudents = decodedData.cast<Map<String, dynamic>>();

        if (mounted && selectedStudents.isNotEmpty) {
          setState(() => _selectedStudents = selectedStudents);
          _updateAvailableFilters();
          _applyFiltersAndSearch();
        }
      }
    } catch (e) {
      print("Error loading selection from cache: $e");
    }
  }

  Future<void> _loadSelectionFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) return;
    
    setState(() {
      if (initialLoad) _isLoading = true;
      else _isLoadingMore = true;
    });

    try {
      final selectionTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_selection';
      final startRange = initialLoad ? 0 : _selectedStudents.length;
      final endRange = startRange + _pageSize - 1;

      final response = await Supabase.instance.client
          .from(selectionTableName)
          .select('*')
          .order('lastname', ascending: true)
          .range(startRange, endRange);

      if (_isDisposed) return;

      final newStudents = List<Map<String, dynamic>>.from(response);

      setState(() {
        if (initialLoad) {
          _selectedStudents = newStudents;
          _page = 0;
        } else {
          _selectedStudents.addAll(newStudents);
          _page++;
        }
        _hasMore = newStudents.length == _pageSize;
      });

      _updateAvailableFilters();
      _applyFiltersAndSearch();
      
      if (initialLoad) _cacheSelection(_selectedStudents);

    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error loading selection: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() => _hasMore = false);
      }
    } finally {
      if(mounted) {
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final selectionTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_selection';
    _realtimeChannel = Supabase.instance.client
        .channel('selection_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: selectionTableName,
      callback: (payload) async {
        if (mounted) await _refreshData();
      },
    ).subscribe();
  }
  
  Future<void> _refreshData() async {
    _page = 0;
    _hasMore = true;
    await _loadSelectionFromSupabase(initialLoad: true);
  }

  void _updateAvailableFilters() {
    if (!mounted) return;
    final years = <dynamic>{};
    final majors = <String>{};
    final genders = <String>{};
    for (var student in _selectedStudents) {
      if (student['year'] != null) years.add(student['year']);
      if (student['major'] != null && student['major'].isNotEmpty) majors.add(student['major']);
      if (student['gender'] != null && student['gender'].isNotEmpty) genders.add(student['gender']);
    }
    setState(() {
      _availableYears = years;
      _availableMajors = majors;
      _availableGenders = genders;
    });
  }

  void _applyFiltersAndSearch() {
    if (!mounted) return;
    
    List<Map<String, dynamic>> results = List.from(_selectedStudents);

    // Apply filters
    if (_selectedGender != null && _selectedGender!.isNotEmpty) {
      results = results.where((s) => 
        s['gender'] != null && 
        s['gender'].toString().toLowerCase() == _selectedGender!.toLowerCase()
      ).toList();
    }
    
    if (_selectedYear != null) {
      results = results.where((s) => 
        s['year'] != null && 
        s['year'].toString() == _selectedYear.toString()
      ).toList();
    }
    
    if (_selectedMajor != null && _selectedMajor!.isNotEmpty) {
      results = results.where((s) => 
        s['major'] != null && 
        s['major'].toString().toLowerCase() == _selectedMajor!.toLowerCase()
      ).toList();
    }

    // Apply search
    if (_searchQuery.isNotEmpty) {
      results = results.where((s) {
        final fullName = '${s['firstname'] ?? ''} ${s['lastname'] ?? ''}'.toLowerCase();
        final major = (s['major'] ?? '').toLowerCase();
        return fullName.contains(_searchQuery.toLowerCase()) || 
               major.contains(_searchQuery.toLowerCase());
      }).toList();
    }

    setState(() => _filteredStudents = results);
  }

  void _resetFilters() {
    setState(() {
      _selectedGender = null;
      _selectedYear = null;
      _selectedMajor = null;
      _applyFiltersAndSearch();
    });
  }

  Future<void> _cacheSelection(List<Map<String, dynamic>> students) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'selection_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(students));
    } catch (e) {
      print("Error caching selection: $e");
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreSelection();
    }
  }

  Future<void> _loadMoreSelection() async {
    if (!_isLoadingMore && _hasMore) {
      await _loadSelectionFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> student) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SelectionDetailPage(
            student: student,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  void _showOfflineSnackbar() {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text("Offline. Please check your internet connection."), backgroundColor: Colors.redAccent),
    );
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.redAccent),
    );
  }

  IconData _getIconForFilter(String filterName) {
    switch (filterName.toLowerCase()) {
      case 'gender': return Icons.people_outline;
      case 'year': return Icons.calendar_today_outlined;
      case 'major': return Icons.school_outlined;
      default: return Icons.filter_list_outlined;
    }
  }

  Widget _buildFilterDropdown({
    required String label,
    required IconData icon,
    required dynamic currentValue,
    required Set<dynamic> items,
    required Function(dynamic) onChanged,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    List sortedItems = items.toList()..sort();

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: DropdownButtonFormField<dynamic>(
        value: currentValue,
        items: sortedItems.map((item) {
          return DropdownMenuItem<dynamic>(
            value: item,
            child: Text(
              item.toString(),
              style: TextStyle(color: isDark ? Colors.white : Colors.black),
            ),
          );
        }).toList(),
        onChanged: onChanged,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(color: isDark ? Colors.white : Colors.black),
          prefixIcon: Icon(icon, color: isDark ? Colors.white : Colors.black),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          filled: true,
          fillColor: theme.colorScheme.surface,
          contentPadding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 12.0),
          suffixIcon: currentValue != null
              ? IconButton(
                  icon: Icon(Icons.clear, color: isDark ? Colors.white : Colors.black),
                  onPressed: () => onChanged(null),
                )
              : null,
        ),
        style: TextStyle(color: isDark ? Colors.white : Colors.black),
        dropdownColor: theme.colorScheme.surface,
      ),
    );
  }

  void _showFilterBottomSheet() {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: Theme.of(context).colorScheme.surface,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: StatefulBuilder(
            builder: (BuildContext context, StateSetter setModalState) {
              final theme = Theme.of(context);
              final isDark = theme.brightness == Brightness.dark;
              
              dynamic tempSelectedYear = _selectedYear;
              String? tempSelectedMajor = _selectedMajor;
              String? tempSelectedGender = _selectedGender;

              return Container(
                constraints: const BoxConstraints(maxHeight: 500),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Filter Students',
                          style: theme.textTheme.titleLarge?.copyWith(
                            color: theme.colorScheme.onSurface,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: Icon(Icons.close, color: theme.colorScheme.onSurface),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    Flexible(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            if (_availableGenders.isNotEmpty)
                              _buildFilterDropdown(
                                label: 'Gender',
                                icon: _getIconForFilter('gender'),
                                currentValue: tempSelectedGender,
                                items: _availableGenders,
                                onChanged: (newValue) => setModalState(() => tempSelectedGender = newValue),
                              ),
                            if (_availableYears.isNotEmpty)
                              _buildFilterDropdown(
                                label: 'Year',
                                icon: _getIconForFilter('year'),
                                currentValue: tempSelectedYear,
                                items: _availableYears,
                                onChanged: (newValue) => setModalState(() => tempSelectedYear = newValue),
                              ),
                            if (_availableMajors.isNotEmpty)
                              _buildFilterDropdown(
                                label: 'Major',
                                icon: _getIconForFilter('major'),
                                currentValue: tempSelectedMajor,
                                items: _availableMajors,
                                onChanged: (newValue) => setModalState(() => tempSelectedMajor = newValue),
                              ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () {
                              setModalState(() {
                                tempSelectedGender = null;
                                tempSelectedMajor = null;
                                tempSelectedYear = null;
                              });
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: isDark ? Colors.white : Colors.black,
                            ),
                            child: const Text('Reset'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _selectedGender = tempSelectedGender;
                                _selectedYear = tempSelectedYear;
                                _selectedMajor = tempSelectedMajor;
                                _applyFiltersAndSearch();
                              });
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: isDark ? Colors.white : Colors.black,
                              foregroundColor: isDark ? Colors.black : Colors.white,
                            ),
                            child: const Text('Apply'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: _buildAppBar(theme),
      body: _buildBody(theme),
      bottomNavigationBar: _isSearching ? null : _buildBottomNavBar(theme, currentIsDarkMode),
    );
  }

  AppBar _buildAppBar(ThemeData theme) {
    if (_isSearching) {
      return AppBar(
        backgroundColor: theme.colorScheme.surface,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () {
            setState(() {
              _isSearching = false;
              _searchQuery = '';
              _searchController.clear();
            });
            _applyFiltersAndSearch();
          },
        ),
        title: TextField(
          controller: _searchController,
          autofocus: true,
          // *** FIX HERE: Explicitly remove all borders ***
          decoration: const InputDecoration(
            hintText: 'Search by name or major...',
            border: InputBorder.none,
            focusedBorder: InputBorder.none,
            enabledBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
          ),
          style: TextStyle(color: theme.colorScheme.onSurface, fontSize: 18),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.clear, color: theme.colorScheme.onSurface),
            onPressed: () => _searchController.clear(),
          ),
        ],
      );
    } else {
      return AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Selected Students',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.search, color: theme.colorScheme.onSurface),
            onPressed: () => setState(() => _isSearching = true),
          ),
          IconButton(
            icon: Icon(Icons.filter_list, color: theme.colorScheme.onSurface),
            onPressed: _showFilterBottomSheet,
          ),
        ],
      );
    }
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_selectedStudents.isEmpty) {
       return RefreshIndicator(
         onRefresh: _refreshData,
         child: LayoutBuilder(
            builder: (context, constraints) => SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: constraints.maxHeight),
                child: const Center(child: Text('No selected students found.')),
              ),
            ),
          ),
       );
    }

    if (_filteredStudents.isEmpty) {
      return Center(
        child: Text('No students match your criteria.', style: TextStyle(color: theme.colorScheme.onSurfaceVariant)),
      );
    }

    return RefreshIndicator(
      color: theme.colorScheme.onSurface,
      onRefresh: () async {
        _resetFilters();
        if (_isSearching) {
            setState(() {
              _isSearching = false;
              _searchQuery = '';
              _searchController.clear();
            });
        }
        await _refreshData();
      },
      child: ListView.builder(
        key: _listKey,
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        itemCount: _filteredStudents.length + (_hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index < _filteredStudents.length) {
            final student = _filteredStudents[index];
            return _buildStudentCard(student, theme, theme.brightness == Brightness.dark);
          } else {
            return _isLoadingMore
                ? const Center(child: Padding(padding: EdgeInsets.all(16), child: CircularProgressIndicator()))
                : const SizedBox.shrink();
          }
        },
      ),
    );
  }

  Widget _buildStudentCard(Map<String, dynamic> student, ThemeData theme, bool isDarkMode) {
    final String fullName = '${student['firstname'] ?? ''} ${student['lastname'] ?? ''}'.trim();
    final String gender = student['gender'] ?? '';
    
    IconData avatarIcon;
    if (gender.toLowerCase() == 'female') avatarIcon = Icons.female;
    else if (gender.toLowerCase() == 'male') avatarIcon = Icons.male;
    else avatarIcon = Icons.person;

    List<String> infoItems = [
      if (student['major'] != null && student['major'].isNotEmpty) student['major'],
      if (student['year'] != null) 'Year ${student['year']}',
      if (student['yearofentry'] != null) 'Entry ${student['yearofentry']}',
      if (student['nationality'] != null && student['nationality'].isNotEmpty) student['nationality'],
    ];
    String infoText = infoItems.join(' • ');

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, student),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode ? Colors.white.withOpacity(0.1) : Colors.black.withOpacity(0.1),
                child: Icon(avatarIcon, size: 24, color: isDarkMode ? Colors.white : Colors.black),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(fullName, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface)),
                    if (infoText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          infoText,
                          style: TextStyle(color: theme.colorScheme.onSurfaceVariant, fontSize: 13),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Icon(Icons.arrow_forward_ios, size: 16, color: theme.colorScheme.onSurfaceVariant),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildBottomNavBar(ThemeData theme, bool currentIsDarkMode) {
    return Container(
      color: theme.colorScheme.surface,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: Icon(Icons.home_outlined, color: theme.colorScheme.onSurface),
                onPressed: () => Navigator.of(context).popUntil((route) => route.isFirst),
              ),
              IconButton(
                icon: Icon(
                  currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: widget.toggleTheme,
              ),
              IconButton(
                icon: Icon(Icons.person_outline, color: theme.colorScheme.onSurface),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LoginPage(isDarkMode: currentIsDarkMode, toggleTheme: widget.toggleTheme),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}