import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'dart:typed_data';

class KnowledgeBaseIndexer {
  static final supabase = Supabase.instance.client;

  // A static lock to prevent multiple indexing processes for the same college at once.
  static final Map<int, bool> _isIndexingLock = {};

  /// Checks for new or deleted documents and updates the search index accordingly.
  static Future<void> startIndexingIfNeeded(
      BuildContext context, Map<String, dynamic> collegeData) async {
    if (collegeData['id'] == null || collegeData['fullname'] == null) {
      print("? Indexing skipped: college data is missing 'id' or 'fullname'.");
      return;
    }

    final int collegeId = collegeData['id'];
    final String collegeName = collegeData['fullname'];
    final String collegeIdentifier = collegeData['tableprefix']?.toString().trim() ??
        collegeData['fullname']!.toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]+'), '').replaceAll(' ', '_');

    // If indexing is already running for this college, skip.
    if (_isIndexingLock[collegeId] == true) {
      print("?? Indexing for $collegeName is already in progress. Skipping.");
      return;
    }

    try {
      _isIndexingLock[collegeId] = true;
      print("?? Checking knowledge base for $collegeName (ID: $collegeId)...");

      // 1. Get all PDF files from Supabase Storage.
      final knowledgeBaseDir = 'knowledgebase';
      final storageFiles = await supabase.storage.from(collegeIdentifier).list(path: knowledgeBaseDir);
      final storageFileNames = storageFiles
          .where((file) => file.name.toLowerCase().endsWith('.pdf'))
          .map((file) => file.name)
          .toSet();

      // 2. Get all file names currently in our database for this college.
      final dbFilesResponse = await supabase
          .from('document_chunks')
          .select('source_file')
          .eq('college_id', collegeId);
          
      final dbFileNames = (dbFilesResponse as List)
          .map((row) => row['source_file'] as String)
          .toSet();

      // 3. Determine what has changed.
      final filesToIndex = storageFileNames.difference(dbFileNames);
      final filesToDelete = dbFileNames.difference(storageFileNames);

      if (filesToIndex.isEmpty && filesToDelete.isEmpty) {
        print("? Knowledge base for $collegeName is already up-to-date.");
        return; // Nothing to do.
      }

      // Show feedback to the user that something is happening in the background.
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Updating AI knowledge for $collegeName...'),
            duration: const Duration(seconds: 4),
          ),
        );
      }

      // 4. Process deletions.
      if (filesToDelete.isNotEmpty) {
        print("??? Deleting ${filesToDelete.length} obsolete file(s) from index...");
        await supabase
            .from('document_chunks')
            .delete()
            .eq('college_id', collegeId)
            // --- THIS IS THE CORRECTED LINE USING .filter() ---
            .filter('source_file', 'in', filesToDelete.toList());
      }

      // 5. Process new files.
      if (filesToIndex.isNotEmpty) {
        print("?? Indexing ${filesToIndex.length} new file(s)...");
        for (final fileName in filesToIndex) {
          await _indexSingleFile(collegeId, collegeIdentifier, knowledgeBaseDir, fileName);
        }
      }

      print("? Indexing process completed for $collegeName.");

    } catch (e) {
      print("? Indexing failed for $collegeName. Error: $e");
    } finally {
      // Release the lock.
      _isIndexingLock[collegeId] = false;
    }
  }

  /// Indexes a single PDF file and inserts its chunks into the database.
  static Future<void> _indexSingleFile(int collegeId, String collegeIdentifier, String dir, String fileName) async {
    print("   - Processing file: $fileName");
    try {
      final filePath = '$dir/$fileName';
      final Uint8List fileBytes = await supabase.storage.from(collegeIdentifier).download(filePath);
      
      final PdfDocument document = PdfDocument(inputBytes: fileBytes);
      String text = PdfTextExtractor(document).extractText();
      document.dispose();

      final chunks = _splitTextIntoChunks(text, chunkSize: 1500, chunkOverlap: 300);

      final List<Map<String, dynamic>> rowsToInsert = [];
      for (final chunk in chunks) {
        if (chunk.trim().length > 20) { 
          rowsToInsert.add({
            'college_id': collegeId,
            'source_file': fileName,
            'content': chunk.trim(),
          });
        }
      }
      
      if (rowsToInsert.isNotEmpty) {
        await supabase.from('document_chunks').insert(rowsToInsert);
        print("     > Inserted ${rowsToInsert.length} chunks for $fileName");
      }
    } catch (e) {
      print("     > Error processing file $fileName: $e");
    }
  }

  /// Splits a long string of text into chunks of a fixed size with overlap.
  static List<String> _splitTextIntoChunks(String text, {int chunkSize = 1500, int chunkOverlap = 300}) {
    if (text.isEmpty) return [];
    final normalizedText = text.replaceAll(RegExp(r'\s+'), ' ');
    final List<String> chunks = [];

    for (int i = 0; i < normalizedText.length; i += (chunkSize - chunkOverlap)) {
      int end = i + chunkSize;
      if (end > normalizedText.length) {
        end = normalizedText.length;
      }
      chunks.add(normalizedText.substring(i, end));
      if (end == normalizedText.length) break;
    }
    return chunks;
  }
}