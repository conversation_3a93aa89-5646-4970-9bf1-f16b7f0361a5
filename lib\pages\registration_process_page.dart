// registration_process_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';
import 'registration_process_detail_page.dart';

class RegistrationProcessPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedSteps;

  const RegistrationProcessPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedSteps,
  }) : super(key: key);

  @override
  _RegistrationProcessPageState createState() => _RegistrationProcessPageState();
}

class _RegistrationProcessPageState extends State<RegistrationProcessPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('registration_process_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _registrationProcessSteps = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("RegistrationProcessPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant RegistrationProcessPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("RegistrationProcessPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("RegistrationProcessPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedSteps != null && widget.preloadedSteps!.isNotEmpty) {
      print("Preloaded steps found, using them.");
      setState(() {
        _registrationProcessSteps = List<Map<String, dynamic>>.from(widget.preloadedSteps!);
        _registrationProcessSteps.sort((a, b) =>
            (a['stepnumber'] ?? 0).compareTo(b['stepnumber'] ?? 0));
        _hasMore = widget.preloadedSteps!.length == _pageSize;
        _page = 0; // Reset page counter
      });
      // Check if there's more data available
      await _checkForMoreData();
    } else {
      print("No preloaded steps or empty list, loading from database and cache.");
      await _loadRegistrationProcessFromCache();
      await _loadRegistrationProcessFromSupabase(initialLoad: true);
    }
  }

  Future<void> _checkForMoreData() async {
    final registrationProcessTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_registrationprocess';
    
    try {
      final response = await Supabase.instance.client
          .from(registrationProcessTableName)
          .select('id')
          .order('stepnumber', ascending: true)
          .range(_registrationProcessSteps.length, _registrationProcessSteps.length + _pageSize - 1);
      
      setState(() {
        _hasMore = response.isNotEmpty;
      });
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadRegistrationProcessFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'registration_process_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedData = prefs.getString(cacheKey);

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> registrationProcessSteps = decodedData.cast<Map<String, dynamic>>();

        if (mounted && registrationProcessSteps.isNotEmpty) {
          setState(() {
            _registrationProcessSteps = registrationProcessSteps;
          });
          print("Loaded ${registrationProcessSteps.length} registration process steps from cache");
        }
      }
    } catch (e) {
      print("Error loading registration process from cache: $e");
    }
  }

  Future<void> _loadRegistrationProcessFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadRegistrationProcessFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() {
        _isLoading = true;
      });
    } else {
      setState(() {
        _isLoadingMore = true;
      });
    }

    try {
      final registrationProcessTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_registrationprocess';

      int startRange;
      int endRange;
      
      if (initialLoad) {
        startRange = 0;
        endRange = _pageSize - 1;
        _page = 0;
      } else {
        startRange = _registrationProcessSteps.length;
        endRange = _registrationProcessSteps.length + _pageSize - 1;
      }

      print("Fetching range: $startRange to $endRange");

      final response = await Supabase.instance.client
          .from(registrationProcessTableName)
          .select('*')
          .order('stepnumber', ascending: true)
          .range(startRange, endRange);

      if (_isDisposed) return;

      if (mounted) {
        setState(() {
          if (initialLoad) {
            _registrationProcessSteps = List<Map<String, dynamic>>.from(response);
          } else {
            _registrationProcessSteps.addAll(List<Map<String, dynamic>>.from(response));
          }
          _registrationProcessSteps.sort((a, b) =>
              (a['stepnumber'] ?? 0).compareTo(b['stepnumber'] ?? 0));
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (!initialLoad) {
            _page++;
          }
        });

        // Cache the data
        if (initialLoad) {
          _cacheRegistrationProcess(_registrationProcessSteps);
        }
      }

    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error loading registration process: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<void> _cacheRegistrationProcess(List<Map<String, dynamic>> steps) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'registration_process_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(steps));
      print("Cached ${steps.length} registration process steps");
    } catch (e) {
      print("Error caching registration process: $e");
    }
  }

  void _setupRealtime() {
    final registrationProcessTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_registrationprocess';
    _realtimeChannel = Supabase.instance.client
        .channel('registration_process_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: registrationProcessTableName,
      callback: (payload) async {
        if (!mounted) return;
        
        print("Realtime update received for registration process: ${payload.eventType}");
        
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newRecord = payload.newRecord;
          if (mounted) {
            setState(() {
              _registrationProcessSteps.add(Map<String, dynamic>.from(newRecord));
              _registrationProcessSteps.sort((a, b) =>
                  (a['stepnumber'] ?? 0).compareTo(b['stepnumber'] ?? 0));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedRecord = payload.newRecord;
          if (mounted) {
            setState(() {
              _registrationProcessSteps = _registrationProcessSteps.map((step) {
                return step['id'] == updatedRecord['id'] 
                    ? Map<String, dynamic>.from(updatedRecord) 
                    : step;
              }).toList();
              _registrationProcessSteps.sort((a, b) =>
                  (a['stepnumber'] ?? 0).compareTo(b['stepnumber'] ?? 0));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedId = payload.oldRecord['id'];
          if (mounted) {
            setState(() {
              _registrationProcessSteps.removeWhere((step) => step['id'] == deletedId);
            });
          }
        }
      },
    ).subscribe();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMoreSteps();
    }
  }

  Future<void> _loadMoreSteps() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more steps...");
      await _loadRegistrationProcessFromSupabase(initialLoad: false);
    }
  }

  Future<void> _openLink(String link) async {
    if (link.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Link is not available')),
      );
      return;
    }

    final Uri uri = Uri.parse(link);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not open link: $link')),
      );
    }
  }

  String _formatDeadline(String? deadlineStr) {
    if (deadlineStr == null || deadlineStr.isEmpty) {
      return 'No deadline specified';
    }

    try {
      final DateTime deadline = DateTime.parse(deadlineStr);
      return DateFormat('MMMM d, y').format(deadline);
    } catch (e) {
      return deadlineStr;
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("RegistrationProcessPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    print("RegistrationProcessPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Registration Process',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadRegistrationProcessFromSupabase(initialLoad: true);
              },
              child: _registrationProcessSteps.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No registration process steps available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _registrationProcessSteps.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _registrationProcessSteps.length) {
                          return _buildStepCard(
                            _registrationProcessSteps[index],
                            theme,
                            currentIsDarkMode,
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStepCard(
    Map<String, dynamic> step,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final int stepNumber = step['stepnumber'] ?? 0;
    final String stepName = step['stepname'] ?? 'Unknown Step';
    final String description = step['description'] ?? '';
    final String link = step['link'] ?? '';
    final String department = step['department'] ?? '';
    final String targetAudience = step['targetaudience'] ?? '';
    final String term = step['term'] ?? '';
    final String deadline = _formatDeadline(step['deadline']?.toString());
    final bool hasDeadlinePassed = step['deadline'] != null ?
        DateTime.parse(step['deadline'].toString()).isBefore(DateTime.now()) : false;

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => RegistrationProcessDetailPage(
                step: step,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Step number circle
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Text(
                  stepNumber.toString(),
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Step details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      stepName,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (description.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 13,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    if (step['deadline'] != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Deadline: $deadline',
                        style: TextStyle(
                          fontSize: 12,
                          color: hasDeadlinePassed ? Colors.red : theme.colorScheme.onSurfaceVariant,
                          fontWeight: hasDeadlinePassed ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ],
                    if (department.isNotEmpty || targetAudience.isNotEmpty || term.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        [
                          if (department.isNotEmpty) department,
                          if (targetAudience.isNotEmpty) targetAudience,
                          if (term.isNotEmpty) term,
                        ].join(' • '),
                        style: TextStyle(
                          fontSize: 12,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Arrow icon
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}