// patents_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'patent_detail_page.dart';

class PatentsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedPatents;
  final bool isFromDetailPage;

  const PatentsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedPatents,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<PatentsPage> createState() => _PatentsPageState();
}

class _PatentsPageState extends State<PatentsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('patents_list');
  List<Map<String, dynamic>> _patents = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 5;
  bool _hasMore = true;

  String? _selectedYear;
  List<String> _availableYears = [];

  @override
  void initState() {
    super.initState();
    print("PatentsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant PatentsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.collegeNameForTable != widget.collegeNameForTable) {
      _loadInitialData();
    }
    print("PatentsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("PatentsPage didChangeDependencies called");
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    await _fetchFilterOptions();
    if (widget.preloadedPatents != null && widget.preloadedPatents!.isNotEmpty) {
      print("Preloaded patents found, using them.");
      setState(() {
        _patents = List<Map<String, dynamic>>.from(widget.preloadedPatents!);
        _sortPatents();
        _page = 0;
      });
      await _checkForMoreData();
    } else {
      print("No preloaded patents or empty list, loading from database.");
      await _loadPatentsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _fetchFilterOptions() async {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_patents';
    try {
      final response = await Supabase.instance.client.from(tableName).select('year');
      if (mounted) {
        setState(() {
          _availableYears = (response as List)
              .map((e) => e['year'].toString())
              .where((y) => y != 'null' && y.isNotEmpty)
              .toSet()
              .toList()..sort((a,b) => b.compareTo(a));
        });
      }
    } catch (e) {
      print('Error fetching filter options: $e');
    }
  }

  Future<void> _checkForMoreData() async {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_patents';
    
    try {
      var query = Supabase.instance.client.from(tableName).select('id');
      if (_selectedYear != null) query = query.eq('year', int.parse(_selectedYear!));
      
      final response = await query
          .order('year', ascending: false)
          .range(_patents.length, _patents.length + _pageSize - 1);
      
      if (mounted) {
        setState(() {
          _hasMore = response.isNotEmpty;
        });
      }
    } catch (error) {
      print("Error checking for more data: $error");
    }
  }

  Future<void> _loadPatentsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || _isLoadingMore || (!_hasMore && !initialLoad)) {
      return;
    }

    print("_loadPatentsFromSupabase called - initialLoad: $initialLoad, current page: $_page");
    
    if (initialLoad) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoadingMore = true);
    }

    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_patents';

    try {
      int startRange = initialLoad ? 0 : _patents.length;
      int endRange = startRange + _pageSize - 1;

      var query = Supabase.instance.client.from(tableName).select('*');
      if (_selectedYear != null) query = query.eq('year', int.parse(_selectedYear!));

      final response = await query
          .order('year', ascending: false)
          .range(startRange, endRange);

      if (mounted) {
        setState(() {
          final newPatents = List<Map<String, dynamic>>.from(response);
          if (initialLoad) {
            _patents = newPatents;
          } else {
            _patents.addAll(newPatents);
          }
          _sortPatents();
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = response.length == _pageSize;
          if (initialLoad) _page = 0;
          if (!initialLoad) _page++;
        });
      }
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching patents: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }

  void _setupRealtime() {
    final tableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_patents';
    _realtimeChannel = Supabase.instance.client
        .channel('patents_realtime')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: tableName,
      callback: (payload) async {
        if (!mounted) return;
        await _loadPatentsFromSupabase(initialLoad: true);
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("PatentsPage dispose called");
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        !_isLoadingMore &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100) {
      _loadMorePatents();
    }
  }

  Future<void> _loadMorePatents() async {
    if (!_isLoadingMore && _hasMore) {
      print("Loading more patents...");
      await _loadPatentsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _sortPatents() {
    _patents.sort((a, b) => (b['year'] ?? 0).compareTo(a['year'] ?? 0));
  }

  void _showOfflineSnackbar() {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _navigateToDetail(BuildContext context, Map<String, dynamic> patent) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PatentDetailPage(
            patent: patent,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForTable: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  void _showFilterDialog() {
    String? tempYear = _selectedYear;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Filter by Year'),
              content: DropdownButton<String>(
                value: tempYear,
                hint: const Text('Select a Year'),
                isExpanded: true,
                items: _availableYears.map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(value: value, child: Text(value));
                }).toList(),
                onChanged: (String? newValue) {
                  setDialogState(() {
                    tempYear = newValue;
                  });
                },
              ),
              actions: [
                TextButton(
                  child: const Text('Clear'),
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (_selectedYear != null) {
                      setState(() {
                        _selectedYear = null;
                        _page = 0;
                        _hasMore = true;
                      });
                      _loadPatentsFromSupabase(initialLoad: true);
                    }
                  },
                ),
                TextButton(
                  child: const Text('Apply'),
                  onPressed: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedYear = tempYear;
                      _page = 0;
                      _hasMore = true;
                    });
                    _loadPatentsFromSupabase(initialLoad: true);
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    print("PatentsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Patents',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.filter_list, color: theme.colorScheme.onSurface),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              color: theme.colorScheme.onSurface,
              onRefresh: () async {
                setState(() {
                  _page = 0;
                  _hasMore = true;
                });
                await _loadPatentsFromSupabase(initialLoad: true);
              },
              child: _patents.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No patents available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _patents.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _patents.length) {
                          final patent = _patents[index];
                          return _buildPatentCard(patent, theme, currentIsDarkMode);
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  String _formatInventors(Map<String, dynamic> patent) {
    final List<String> inventors = [];
    if (patent['inventor'] != null && patent['inventor'].toString().isNotEmpty) {
      inventors.add(patent['inventor'].toString());
    }
    if (patent['inventor2'] != null && patent['inventor2'].toString().isNotEmpty) {
      inventors.add(patent['inventor2'].toString());
    }
    if (patent['inventor3'] != null && patent['inventor3'].toString().isNotEmpty) {
      inventors.add(patent['inventor3'].toString());
    }
    return inventors.isNotEmpty ? inventors.join(', ') : 'N/A';
  }

  Widget _buildPatentCard(
    Map<String, dynamic> patent,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = patent['fullname'] ?? 'Unknown';
    final String inventors = _formatInventors(patent);
    final String year = patent['year']?.toString() ?? 'N/A';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, patent),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.description,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (inventors != 'N/A')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          inventors,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (year != 'N/A')
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Year: $year',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}